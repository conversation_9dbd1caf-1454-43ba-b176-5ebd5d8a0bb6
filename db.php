<?php
// db.php 数据库连接（PDO版）
$host = 'localhost';
$user = 'root';
$pass = '';
$dbname = '7spa';
$dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
try {
    $pdo = new PDO($dsn, $user, $pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
} catch (PDOException $e) {
    // 记录错误到日志，不输出HTML
    error_log("Database connection failed: " . $e->getMessage());
    $pdo = null;
}
?>
