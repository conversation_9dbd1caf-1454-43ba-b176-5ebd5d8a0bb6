<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            border-left: 4px solid #00c6a2;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .btn {
            background: #00c6a2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #00a085;
        }
        .image-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .image-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            width: 200px;
        }
        .image-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        .image-info {
            padding: 8px;
            font-size: 12px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 轮播图API测试</h1>
        
        <div>
            <button class="btn" onclick="testAPI()">测试API接口</button>
            <button class="btn" onclick="testImages()">测试图片加载</button>
            <button class="btn" onclick="checkFiles()">检查文件存在</button>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
        <div id="images" class="image-preview"></div>
    </div>

    <script>
        function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在测试API...';
            
            fetch('banner_list.php')
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);
                    return response.text();
                })
                .then(text => {
                    console.log('Raw response:', text);
                    try {
                        const data = JSON.parse(text);
                        resultDiv.textContent = `API测试结果:

状态: ${data.success ? '✅ 成功' : '❌ 失败'}
消息: ${data.msg || '无'}
数据数量: ${data.data ? data.data.length : 0}

完整响应:
${JSON.stringify(data, null, 2)}`;
                        
                        if (data.success && data.data && data.data.length > 0) {
                            showImages(data.data);
                        }
                    } catch (error) {
                        resultDiv.className = 'result error';
                        resultDiv.textContent = `JSON解析失败:
原始响应: ${text}
错误: ${error.message}`;
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `网络请求失败: ${error.message}`;
                });
        }
        
        function testImages() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在测试图片加载...';
            
            fetch('banner_list.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data && data.data.length > 0) {
                        const imageTests = data.data.map(banner => {
                            return new Promise((resolve) => {
                                const img = new Image();
                                img.onload = () => resolve({
                                    id: banner.id,
                                    img: banner.img,
                                    status: '✅ 加载成功',
                                    size: `${img.naturalWidth}x${img.naturalHeight}`
                                });
                                img.onerror = () => resolve({
                                    id: banner.id,
                                    img: banner.img,
                                    status: '❌ 加载失败',
                                    size: '无法获取'
                                });
                                img.src = banner.img;
                            });
                        });
                        
                        Promise.all(imageTests).then(results => {
                            resultDiv.textContent = `图片加载测试结果:

${results.map(result => 
    `ID: ${result.id}
路径: ${result.img}
状态: ${result.status}
尺寸: ${result.size}
`).join('\n')}`;
                        });
                    } else {
                        resultDiv.textContent = '没有轮播图数据可测试';
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `测试失败: ${error.message}`;
                });
        }
        
        function checkFiles() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在检查文件...';
            
            // 检查banner.json文件
            fetch('admin/banner.json')
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(data => {
                    resultDiv.textContent = `文件检查结果:

✅ admin/banner.json 存在
数据结构:
${JSON.stringify(data, null, 2)}

数据分析:
- 轮播图数量: ${data.length}
- 包含字段: ${data.length > 0 ? Object.keys(data[0]).join(', ') : '无'}
- 是否有is_active字段: ${data.length > 0 && data[0].hasOwnProperty('is_active') ? '是' : '否'}`;
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `文件检查失败:
❌ admin/banner.json 不存在或无法访问
错误: ${error.message}`;
                });
        }
        
        function showImages(banners) {
            const imagesDiv = document.getElementById('images');
            imagesDiv.innerHTML = '';
            
            banners.forEach(banner => {
                const item = document.createElement('div');
                item.className = 'image-item';
                
                item.innerHTML = `
                    <img src="${banner.img}" alt="轮播图${banner.id}" 
                         onerror="this.src='images/lbt.png'; this.style.border='2px solid red';">
                    <div class="image-info">
                        <div><strong>ID:</strong> ${banner.id}</div>
                        <div><strong>路径:</strong> ${banner.img}</div>
                        <div><strong>链接:</strong> ${banner.link || '无'}</div>
                    </div>
                `;
                
                imagesDiv.appendChild(item);
            });
        }
        
        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            testAPI();
        });
    </script>
</body>
</html>
