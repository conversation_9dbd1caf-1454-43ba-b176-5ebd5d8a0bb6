<?php
// 禁用错误输出到页面，防止破坏JSON格式
error_reporting(0);
ini_set('display_errors', 0);

header('Content-Type: application/json; charset=utf-8');

$host = 'localhost';
$db   = '7spa';
$user = 'root';
$pass = '';
$charset = 'utf8mb4';

$phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
$password = isset($_POST['password']) ? trim($_POST['password']) : '';

if (!$phone || !$password) {
    echo json_encode(['success' => false, 'msg' => '账号和密码不能为空']);
    exit;
}

$dsn = "mysql:host=$host;dbname=$db;charset=$charset";
try {
    $pdo = new PDO($dsn, $user, $pass, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
    $stmt = $pdo->prepare("SELECT id, password, nickname, avatar FROM user WHERE phone = ?");
    $stmt->execute([$phone]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    if (!$user || !password_verify($password, $user['password'])) {
        echo json_encode(['success' => false, 'msg' => '账号或密码错误']);
        exit;
    }
    // 登录成功，返回用户信息
    unset($user['password']);
    echo json_encode(['success' => true, 'msg' => '登录成功', 'user' => $user]);
} catch (Exception $e) {
    // 记录错误日志
    error_log("Login error: " . $e->getMessage());
    echo json_encode(['success' => false, 'msg' => '数据库连接失败，请稍后重试']);
}
?>
