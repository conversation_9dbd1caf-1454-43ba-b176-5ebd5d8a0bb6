<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>热门城市功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .city-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 8px;
            margin-top: 15px;
        }
        .city-card {
            background: #fff;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 12px 8px;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
            cursor: pointer;
            min-height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .city-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        .city-card.hot {
            border-color: #ffc107;
            background: linear-gradient(135deg, #fff9c4 0%, #fff 100%);
        }
        .city-name {
            font-size: 13px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
            line-height: 1.2;
            word-break: break-all;
        }
        .city-id {
            font-size: 10px;
            color: #999;
            margin-bottom: 8px;
        }
        .city-status {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 12px;
            margin-bottom: 8px;
        }
        .status-hot {
            background: #ffc107;
            color: #212529;
        }
        .status-normal {
            background: #e9ecef;
            color: #6c757d;
        }
        .action-buttons {
            display: flex;
            gap: 2px;
        }
        .btn-hot {
            flex: 1;
            height: 24px;
            font-size: 9px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            padding: 0;
        }
        .btn-hot.set-hot {
            background: #28a745;
            color: white;
        }
        .btn-hot.cancel-hot {
            background: #ffc107;
            color: #212529;
        }
        .btn-delete {
            flex: 1;
            height: 24px;
            font-size: 10px;
            border-radius: 4px;
            background: #dc3545;
            border: none;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            padding: 0;
        }
        .stats {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        .stat-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-number.hot {
            color: #ffc107;
        }
        .stat-number.normal {
            color: #6c757d;
        }
        .stat-number.total {
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⭐ 热门城市功能测试</h1>
        
        <div class="test-section">
            <h3>📊 城市统计</h3>
            <button onclick="loadCityStats()">刷新统计</button>
            <div id="city-stats" class="stats"></div>
        </div>
        
        <div class="test-section">
            <h3>🏙️ 城市列表（一行10列布局）</h3>
            <p style="color: #666; font-size: 14px;">
                ⭐ 黄色边框 = 热门城市 | 普通边框 = 普通城市<br>
                点击星星按钮可以切换热门状态
            </p>
            <button onclick="loadCities()">加载城市列表</button>
            <button onclick="testSetHot()">测试设为热门</button>
            <button onclick="testCancelHot()">测试取消热门</button>
            <div id="cities-container"></div>
        </div>
        
        <div class="test-section">
            <h3>🧪 API测试</h3>
            <button onclick="testHotAPI()">测试热门城市API</button>
            <div id="api-result" class="result"></div>
        </div>
    </div>

    <script>
        let currentCities = [];
        
        function loadCityStats() {
            const statsDiv = document.getElementById('city-stats');
            statsDiv.innerHTML = '<div style="text-align: center; color: #999;">正在加载统计...</div>';
            
            fetch('admin/city_list.php')
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data)) {
                        currentCities = data;
                        const total = data.length;
                        const hotCities = data.filter(city => city.is_hot == 1);
                        const normalCities = data.filter(city => city.is_hot != 1);
                        
                        statsDiv.innerHTML = `
                            <div class="stat-item">
                                <div class="stat-number total">${total}</div>
                                <div class="stat-label">总城市数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number hot">${hotCities.length}</div>
                                <div class="stat-label">热门城市</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number normal">${normalCities.length}</div>
                                <div class="stat-label">普通城市</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">${((hotCities.length / total) * 100).toFixed(1)}%</div>
                                <div class="stat-label">热门比例</div>
                            </div>
                        `;
                    } else {
                        statsDiv.innerHTML = '<div style="text-align: center; color: #f56c6c;">加载失败</div>';
                    }
                })
                .catch(error => {
                    statsDiv.innerHTML = '<div style="text-align: center; color: #f56c6c;">网络错误</div>';
                });
        }
        
        function loadCities() {
            const container = document.getElementById('cities-container');
            container.innerHTML = '<div style="text-align: center; padding: 40px; color: #999;">正在加载城市列表...</div>';
            
            fetch('admin/city_list.php')
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data)) {
                        currentCities = data;
                        renderCities(data);
                    } else {
                        container.innerHTML = '<div style="text-align: center; padding: 40px; color: #f56c6c;">加载失败</div>';
                    }
                })
                .catch(error => {
                    container.innerHTML = '<div style="text-align: center; padding: 40px; color: #f56c6c;">网络错误</div>';
                });
        }
        
        function renderCities(cities) {
            const container = document.getElementById('cities-container');
            
            if (cities.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #999;">暂无城市数据</div>';
                return;
            }
            
            container.innerHTML = `
                <div class="city-grid">
                    ${cities.map(city => `
                        <div class="city-card ${city.is_hot == 1 ? 'hot' : ''}">
                            <div>
                                <div style="font-size: 12px; color: #667eea; margin-bottom: 4px;">🏙️</div>
                                <div class="city-name">
                                    ${city.name}
                                    ${city.is_hot == 1 ? '<span style="color: #ffc107;">⭐</span>' : ''}
                                </div>
                                <div class="city-id">#${city.id}</div>
                                <div class="city-status ${city.is_hot == 1 ? 'status-hot' : 'status-normal'}">
                                    ${city.is_hot == 1 ? '热门城市' : '普通城市'}
                                </div>
                            </div>
                            <div class="action-buttons">
                                <button 
                                    class="btn-hot ${city.is_hot == 1 ? 'cancel-hot' : 'set-hot'}"
                                    onclick="toggleCityHot(${city.id}, ${city.is_hot == 1 ? 0 : 1})"
                                    title="${city.is_hot == 1 ? '取消热门' : '设为热门'}">
                                    ${city.is_hot == 1 ? '⭐' : '☆'}
                                </button>
                                <button 
                                    class="btn-delete"
                                    onclick="deleteCity(${city.id})"
                                    title="删除城市">
                                    🗑️
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }
        
        function toggleCityHot(cityId, isHot) {
            const city = currentCities.find(c => c.id == cityId);
            const actionText = isHot ? '设为热门城市' : '取消热门城市';
            
            if (confirm(`确定要将"${city.name}"${actionText}吗？`)) {
                fetch('admin/city_set_hot.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: cityId,
                        is_hot: isHot
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.msg);
                        loadCities(); // 重新加载列表
                        loadCityStats(); // 更新统计
                    } else {
                        alert('操作失败: ' + data.msg);
                    }
                })
                .catch(error => {
                    alert('网络错误，操作失败');
                });
            }
        }
        
        function deleteCity(cityId) {
            const city = currentCities.find(c => c.id == cityId);
            if (confirm(`确定要删除"${city.name}"吗？`)) {
                alert('删除功能演示 - 城市ID: ' + cityId);
            }
        }
        
        function testSetHot() {
            const normalCities = currentCities.filter(city => city.is_hot != 1);
            if (normalCities.length > 0) {
                const testCity = normalCities[0];
                toggleCityHot(testCity.id, 1);
            } else {
                alert('没有普通城市可以设为热门');
            }
        }
        
        function testCancelHot() {
            const hotCities = currentCities.filter(city => city.is_hot == 1);
            if (hotCities.length > 0) {
                const testCity = hotCities[0];
                toggleCityHot(testCity.id, 0);
            } else {
                alert('没有热门城市可以取消');
            }
        }
        
        function testHotAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.textContent = '正在测试热门城市API...';
            resultDiv.className = 'result';
            
            if (currentCities.length === 0) {
                resultDiv.textContent = '请先加载城市列表';
                resultDiv.className = 'result error';
                return;
            }
            
            const testCity = currentCities[0];
            const newHotStatus = testCity.is_hot == 1 ? 0 : 1;
            
            fetch('admin/city_set_hot.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: testCity.id,
                    is_hot: newHotStatus
                })
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.textContent = `API测试结果:

请求: ${testCity.name} (ID: ${testCity.id})
操作: ${newHotStatus ? '设为热门' : '取消热门'}

响应:
${JSON.stringify(data, null, 2)}`;
                resultDiv.className = data.success ? 'result success' : 'result error';
                
                if (data.success) {
                    loadCities(); // 重新加载列表
                    loadCityStats(); // 更新统计
                }
            })
            .catch(error => {
                resultDiv.textContent = 'API测试失败: ' + error.message;
                resultDiv.className = 'result error';
            });
        }
        
        // 页面加载时自动加载数据
        window.onload = function() {
            loadCityStats();
            loadCities();
        };
    </script>
</body>
</html>
