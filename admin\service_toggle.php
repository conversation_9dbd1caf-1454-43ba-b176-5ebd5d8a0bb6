<?php
// service_toggle.php - 服务激活/停用
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

try {
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $action = isset($_POST['action']) ? trim($_POST['action']) : '';
    
    if ($id <= 0) {
        echo json_encode(['success' => false, 'msg' => '无效的服务ID']);
        exit;
    }

    if (!in_array($action, ['activate', 'deactivate'])) {
        echo json_encode(['success' => false, 'msg' => '无效的操作类型']);
        exit;
    }

    // 查询服务信息
    $stmt = $pdo->prepare("SELECT id, name, is_active FROM services WHERE id = ?");
    $stmt->execute([$id]);
    $service = $stmt->fetch();
    
    if (!$service) {
        echo json_encode(['success' => false, 'msg' => '服务项目不存在']);
        exit;
    }

    $newStatus = ($action === 'activate') ? 1 : 0;
    
    // 检查当前状态
    if ($service['is_active'] == $newStatus) {
        $statusText = $newStatus ? '激活' : '停用';
        echo json_encode(['success' => false, 'msg' => "服务项目已经是{$statusText}状态"]);
        exit;
    }

    // 更新状态
    $stmt = $pdo->prepare("UPDATE services SET is_active = ? WHERE id = ?");
    $result = $stmt->execute([$newStatus, $id]);
    
    if ($result) {
        $statusText = $newStatus ? '激活' : '停用';
        echo json_encode([
            'success' => true, 
            'msg' => "服务项目「{$service['name']}」已{$statusText}"
        ]);
    } else {
        echo json_encode(['success' => false, 'msg' => '操作失败']);
    }

} catch (Exception $e) {
    error_log("Service toggle error: " . $e->getMessage());
    echo json_encode(['success' => false, 'msg' => '操作失败: ' . $e->getMessage()]);
}
?>
