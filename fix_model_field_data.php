<?php
// fix_model_field_data.php - 修复现有技师记录的model字段数据
header('Content-Type: application/json; charset=utf-8');

require_once 'db.php';

try {
    // 检查technician表中model字段的情况
    $checkStmt = $pdo->query("SELECT id, nick, phone, model FROM technician ORDER BY id");
    $technicians = $checkStmt->fetchAll(PDO::FETCH_ASSOC);
    
    $totalCount = count($technicians);
    $nullCount = 0;
    $emptyCount = 0;
    $validCount = 0;
    $fixedCount = 0;
    
    $details = [];
    
    foreach ($technicians as $tech) {
        $modelValue = $tech['model'];
        $status = '';
        
        if ($modelValue === null) {
            $nullCount++;
            $status = 'NULL';
            
            // 为NULL的记录设置默认值
            $updateStmt = $pdo->prepare("UPDATE technician SET model = '1' WHERE id = ?");
            if ($updateStmt->execute([$tech['id']])) {
                $fixedCount++;
                $status .= ' → 已修复为"1"';
            }
        } elseif ($modelValue === '') {
            $emptyCount++;
            $status = '空字符串';
            
            // 为空字符串的记录设置默认值
            $updateStmt = $pdo->prepare("UPDATE technician SET model = '1' WHERE id = ?");
            if ($updateStmt->execute([$tech['id']])) {
                $fixedCount++;
                $status .= ' → 已修复为"1"';
            }
        } else {
            $validCount++;
            $status = "有效值: \"{$modelValue}\"";
        }
        
        $details[] = [
            'id' => $tech['id'],
            'nick' => $tech['nick'],
            'phone' => $tech['phone'],
            'model' => $modelValue,
            'status' => $status
        ];
    }
    
    // 验证修复结果
    $verifyStmt = $pdo->query("SELECT COUNT(*) as count FROM technician WHERE model IS NULL OR model = ''");
    $remainingIssues = $verifyStmt->fetch()['count'];
    
    echo json_encode([
        'success' => true,
        'message' => 'model字段数据检查和修复完成',
        'statistics' => [
            'total_technicians' => $totalCount,
            'null_values' => $nullCount,
            'empty_values' => $emptyCount,
            'valid_values' => $validCount,
            'fixed_records' => $fixedCount,
            'remaining_issues' => $remainingIssues
        ],
        'details' => $details,
        'summary' => "共检查 {$totalCount} 条技师记录，修复了 {$fixedCount} 条记录，剩余问题 {$remainingIssues} 条"
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'message' => '修复model字段数据失败'
    ]);
}
?>
