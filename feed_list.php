<?php
// feed_list.php
header('Content-Type: application/json; charset=utf-8');
require_once 'db.php';

// 查询所有动态，按时间倒序
$sql = 'SELECT f.*, u.nickname, u.avatar FROM feed f LEFT JOIN user u ON f.user_id = u.id ORDER BY f.id DESC LIMIT 100';
$stmt = $pdo->query($sql);
$feeds = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $row['images'] = $row['images'] ? json_decode($row['images'], true) : [];
    $feeds[] = $row;
}
echo json_encode(['success' => true, 'feeds' => $feeds], JSON_UNESCAPED_UNICODE);
