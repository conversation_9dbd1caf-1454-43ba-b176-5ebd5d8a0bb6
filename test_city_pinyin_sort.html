<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>城市拼音排序测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        .city-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .city-item {
            background: white;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s ease;
        }
        .city-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }
        .city-item.hot {
            border-color: #ffc107;
            background: #fff8e1;
        }
        .city-name {
            font-weight: bold;
            color: #333;
        }
        .city-pinyin {
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .city-hot {
            color: #ffc107;
            font-size: 16px;
        }
        .alphabet-nav {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 15px 0;
            padding: 15px;
            background: #e3f2fd;
            border-radius: 8px;
        }
        .alphabet-letter {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s ease;
        }
        .alphabet-letter:hover {
            background: #007bff;
            color: white;
        }
        .alphabet-letter.active {
            background: #007bff;
            color: white;
        }
        .alphabet-letter.has-cities {
            border-color: #007bff;
            color: #007bff;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #999;
        }
        .error {
            text-align: center;
            padding: 40px;
            color: #dc3545;
            background: #f8d7da;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏙️ 城市拼音排序测试</h1>
        
        <div class="demo-section">
            <h3>📊 排序说明</h3>
            <p style="color: #666; line-height: 1.6;">
                城市列表现在按以下规则排序：<br>
                1. <strong>热门城市优先</strong> - 标记为热门的城市显示在前面<br>
                2. <strong>拼音首字母排序</strong> - 按城市名称的拼音首字母A-Z排序<br>
                3. <strong>同字母内排序</strong> - 相同首字母的城市按名称排序
            </p>
            
            <div class="stats" id="stats">
                <div class="stat-item">
                    <div class="stat-number" id="total-cities">-</div>
                    <div class="stat-label">总城市数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="hot-cities">-</div>
                    <div class="stat-label">热门城市</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="alphabet-count">-</div>
                    <div class="stat-label">字母分组</div>
                </div>
            </div>
            
            <div>
                <button class="btn" onclick="loadCities()">🔄 重新加载</button>
                <button class="btn success" onclick="openCityManagement()">🔗 打开城市管理</button>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🔤 字母导航</h3>
            <div class="alphabet-nav" id="alphabet-nav">
                <!-- 字母导航将通过JavaScript生成 -->
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🏙️ 城市列表</h3>
            <div id="cities-container">
                <div class="loading">正在加载城市列表...</div>
            </div>
        </div>
    </div>

    <script>
        let cities = [];
        let currentFilter = 'ALL';
        
        // 页面加载时自动加载城市
        document.addEventListener('DOMContentLoaded', function() {
            loadCities();
        });
        
        // 加载城市列表
        async function loadCities() {
            const container = document.getElementById('cities-container');
            container.innerHTML = '<div class="loading">正在加载城市列表...</div>';
            
            try {
                const response = await fetch('admin/city_list.php');
                const data = await response.json();
                
                if (Array.isArray(data)) {
                    cities = data;
                    renderCities();
                    updateStats();
                    generateAlphabetNav();
                } else {
                    throw new Error('返回数据格式错误');
                }
            } catch (error) {
                console.error('加载城市失败:', error);
                container.innerHTML = `
                    <div class="error">
                        <h4>❌ 加载失败</h4>
                        <p>${error.message}</p>
                        <button class="btn" onclick="loadCities()">重新加载</button>
                    </div>
                `;
            }
        }
        
        // 渲染城市列表
        function renderCities() {
            const container = document.getElementById('cities-container');
            
            if (cities.length === 0) {
                container.innerHTML = '<div class="loading">暂无城市数据</div>';
                return;
            }
            
            // 过滤城市
            let filteredCities = cities;
            if (currentFilter !== 'ALL') {
                filteredCities = cities.filter(city => city.pinyin_first === currentFilter);
            }
            
            // 按字母分组
            const groupedCities = {};
            filteredCities.forEach(city => {
                const letter = city.pinyin_first;
                if (!groupedCities[letter]) {
                    groupedCities[letter] = [];
                }
                groupedCities[letter].push(city);
            });
            
            // 生成HTML
            let html = '';
            const letters = Object.keys(groupedCities).sort();
            
            letters.forEach(letter => {
                html += `
                    <div style="margin-bottom: 30px;">
                        <h4 style="margin: 20px 0 10px 0; padding: 8px 12px; background: #007bff; color: white; border-radius: 4px; display: inline-block;">
                            ${letter} (${groupedCities[letter].length}个城市)
                        </h4>
                        <div class="city-grid">
                            ${groupedCities[letter].map(city => `
                                <div class="city-item ${city.is_hot ? 'hot' : ''}">
                                    <div>
                                        <div class="city-name">${city.name}</div>
                                        <div style="font-size: 12px; color: #666; margin-top: 4px;">
                                            ID: ${city.id} | 坐标: ${city.lng ? city.lng.toFixed(2) : 'N/A'}, ${city.lat ? city.lat.toFixed(2) : 'N/A'}
                                        </div>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span class="city-pinyin">${city.pinyin_first}</span>
                                        ${city.is_hot ? '<span class="city-hot">⭐</span>' : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // 更新统计信息
        function updateStats() {
            const totalCities = cities.length;
            const hotCities = cities.filter(city => city.is_hot).length;
            const alphabetGroups = new Set(cities.map(city => city.pinyin_first)).size;
            
            document.getElementById('total-cities').textContent = totalCities;
            document.getElementById('hot-cities').textContent = hotCities;
            document.getElementById('alphabet-count').textContent = alphabetGroups;
        }
        
        // 生成字母导航
        function generateAlphabetNav() {
            const nav = document.getElementById('alphabet-nav');
            const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
            const usedLetters = new Set(cities.map(city => city.pinyin_first));
            
            nav.innerHTML = `
                <div class="alphabet-letter ${currentFilter === 'ALL' ? 'active' : ''}" onclick="filterByLetter('ALL')">
                    全部
                </div>
                ${letters.map(letter => `
                    <div class="alphabet-letter ${usedLetters.has(letter) ? 'has-cities' : ''} ${currentFilter === letter ? 'active' : ''}" 
                         onclick="filterByLetter('${letter}')"
                         title="${usedLetters.has(letter) ? `${getCitiesCountByLetter(letter)}个城市` : '无城市'}">
                        ${letter}
                    </div>
                `).join('')}
            `;
        }
        
        // 按字母过滤
        function filterByLetter(letter) {
            currentFilter = letter;
            renderCities();
            generateAlphabetNav();
        }
        
        // 获取指定字母的城市数量
        function getCitiesCountByLetter(letter) {
            return cities.filter(city => city.pinyin_first === letter).length;
        }
        
        // 打开城市管理页面
        function openCityManagement() {
            // 这里需要先登录后台
            window.open('admin/admin_login.html', '_blank');
        }
        
        // 测试拼音排序功能
        function testPinyinSort() {
            console.log('城市拼音排序测试:');
            cities.forEach(city => {
                console.log(`${city.name} -> ${city.pinyin_first} (热门: ${city.is_hot})`);
            });
            
            // 验证排序是否正确
            let isCorrectOrder = true;
            for (let i = 1; i < cities.length; i++) {
                const prev = cities[i - 1];
                const curr = cities[i];
                
                // 检查热门城市是否在前面
                if (prev.is_hot < curr.is_hot) {
                    console.error(`排序错误: ${prev.name}(热门:${prev.is_hot}) 应该在 ${curr.name}(热门:${curr.is_hot}) 之后`);
                    isCorrectOrder = false;
                }
                
                // 在相同热门状态下，检查拼音排序
                if (prev.is_hot === curr.is_hot && prev.pinyin_first > curr.pinyin_first) {
                    console.error(`拼音排序错误: ${prev.name}(${prev.pinyin_first}) 应该在 ${curr.name}(${curr.pinyin_first}) 之后`);
                    isCorrectOrder = false;
                }
            }
            
            if (isCorrectOrder) {
                console.log('✅ 排序正确！');
            } else {
                console.log('❌ 排序有问题！');
            }
        }
        
        // 添加测试按钮（开发调试用）
        if (window.location.hostname === 'localhost') {
            document.addEventListener('DOMContentLoaded', function() {
                const container = document.querySelector('.container');
                const testBtn = document.createElement('button');
                testBtn.textContent = '🧪 测试排序';
                testBtn.className = 'btn';
                testBtn.onclick = testPinyinSort;
                testBtn.style.position = 'fixed';
                testBtn.style.bottom = '20px';
                testBtn.style.right = '20px';
                testBtn.style.zIndex = '1000';
                document.body.appendChild(testBtn);
            });
        }
    </script>
</body>
</html>
