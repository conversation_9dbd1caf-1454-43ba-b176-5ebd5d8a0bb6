<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试技师型号字段显示问题</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .test-form {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 调试技师型号字段显示问题</h1>
        
        <div class="section">
            <h3>📋 问题描述</h3>
            <p>技师修改资料时，"你的型号"字段保存后再次打开没有显示选中状态。</p>
            
            <div class="info">
                <strong>可能的原因：</strong><br>
                • technician_status.php 没有返回 model 字段<br>
                • 数据库中 model 字段为 NULL 或空值<br>
                • 前端模板中选中逻辑有问题<br>
                • 数据类型不匹配导致比较失败
            </div>
        </div>
        
        <div class="section">
            <h3>🔍 调试步骤</h3>
            <div style="margin: 15px 0;">
                <button class="btn" onclick="checkTechnicianAPI()">📡 1. 检查API返回</button>
                <button class="btn" onclick="testModelSelection()">🎯 2. 测试型号选择</button>
                <button class="btn success" onclick="simulateFormRender()">🎨 3. 模拟表单渲染</button>
            </div>
            
            <div id="debug-result">
                <div class="info">点击上方按钮开始调试</div>
            </div>
        </div>
        
        <div class="section">
            <h3>🧪 测试表单</h3>
            <p>输入手机号测试技师数据获取和型号字段显示：</p>
            
            <div class="test-form">
                <div class="form-group">
                    <label>技师手机号</label>
                    <input type="tel" id="test-phone" placeholder="请输入技师手机号" value="13800138000">
                </div>
                <div class="form-group">
                    <label>你的型号（测试显示）</label>
                    <select id="test-model-select">
                        <option value="">请选择</option>
                        <option value="1">1</option>
                        <option value="0">0</option>
                        <option value="0.5">0.5</option>
                        <option value="不10">不10</option>
                    </select>
                </div>
                <div style="margin-top: 15px;">
                    <button class="btn success" onclick="loadTechnicianData()">📥 加载技师数据</button>
                    <button class="btn" onclick="clearTestForm()">🗑️ 清空表单</button>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>📊 调试信息</h3>
            <div id="debug-log" class="debug-info">
                调试日志将显示在这里...
            </div>
        </div>
    </div>

    <script>
        let debugLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            debugLog.push(logEntry);
            
            const logContainer = document.getElementById('debug-log');
            logContainer.textContent = debugLog.join('\n');
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(logEntry);
        }
        
        // 检查技师API返回
        async function checkTechnicianAPI() {
            log('开始检查技师API返回数据...');
            const resultContainer = document.getElementById('debug-result');
            
            try {
                const testPhone = '13800138000'; // 使用测试手机号
                log(`测试手机号: ${testPhone}`);
                
                const response = await fetch('technician_status.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'phone=' + encodeURIComponent(testPhone)
                });
                
                const data = await response.json();
                log(`API响应: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success && data.data) {
                    const techData = data.data;
                    const hasModel = 'model' in techData;
                    const modelValue = techData.model;
                    
                    resultContainer.innerHTML = `
                        <div class="success">
                            ✅ API检查完成<br><br>
                            
                            <strong>返回数据：</strong><br>
                            • 技师昵称: ${techData.nick || '未设置'}<br>
                            • 手机号: ${techData.phone || '未设置'}<br>
                            • 尺寸: ${techData.size || '未设置'}<br>
                            • 包含model字段: ${hasModel ? '是' : '否'}<br>
                            • model值: ${modelValue !== undefined ? `"${modelValue}"` : 'undefined'}<br>
                            • model类型: ${typeof modelValue}<br><br>
                            
                            <strong>完整数据：</strong><br>
                            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 11px; max-height: 200px; overflow-y: auto;">
                                ${JSON.stringify(techData, null, 2)}
                            </div>
                        </div>
                    `;
                } else {
                    resultContainer.innerHTML = `
                        <div class="error">
                            ❌ API检查失败<br>
                            错误信息: ${data.msg || '未知错误'}
                        </div>
                    `;
                }
            } catch (error) {
                log(`API检查失败: ${error.message}`, 'error');
                resultContainer.innerHTML = `
                    <div class="error">❌ 请求失败: ${error.message}</div>
                `;
            }
        }
        
        // 测试型号选择逻辑
        function testModelSelection() {
            log('开始测试型号选择逻辑...');
            const resultContainer = document.getElementById('debug-result');
            
            // 模拟不同的model值
            const testCases = [
                { model: '1', expected: '1' },
                { model: '0', expected: '0' },
                { model: '0.5', expected: '0.5' },
                { model: '不10', expected: '不10' },
                { model: '', expected: '空字符串' },
                { model: null, expected: 'null' },
                { model: undefined, expected: 'undefined' }
            ];
            
            let results = [];
            
            testCases.forEach((testCase, index) => {
                const { model, expected } = testCase;
                
                // 测试选中逻辑
                const selected1 = model === '1' ? 'selected' : '';
                const selected0 = model === '0' ? 'selected' : '';
                const selected05 = model === '0.5' ? 'selected' : '';
                const selected不10 = model === '不10' ? 'selected' : '';
                
                results.push(`
                    测试 ${index + 1}: model = ${expected}<br>
                    • 选项"1": ${selected1 || '未选中'}<br>
                    • 选项"0": ${selected0 || '未选中'}<br>
                    • 选项"0.5": ${selected05 || '未选中'}<br>
                    • 选项"不10": ${selected不10 || '未选中'}<br>
                `);
                
                log(`测试用例 ${index + 1}: model="${model}" (${typeof model})`);
                log(`选中结果: 1=${!!selected1}, 0=${!!selected0}, 0.5=${!!selected05}, 不10=${!!selected不10}`);
            });
            
            resultContainer.innerHTML = `
                <div class="info">
                    🎯 型号选择逻辑测试结果<br><br>
                    ${results.join('<br>')}
                </div>
            `;
        }
        
        // 模拟表单渲染
        function simulateFormRender() {
            log('开始模拟表单渲染...');
            const resultContainer = document.getElementById('debug-result');
            
            // 模拟从API获取的数据
            const mockTechData = {
                nick: '测试技师',
                phone: '13800138000',
                model: '0.5' // 模拟保存的型号值
            };
            
            log(`模拟技师数据: ${JSON.stringify(mockTechData)}`);
            
            // 生成HTML模板
            const htmlTemplate = `
                <select name="model" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <option value="">请选择</option>
                    <option value="1" ${mockTechData.model === '1' ? 'selected' : ''}>1</option>
                    <option value="0" ${mockTechData.model === '0' ? 'selected' : ''}>0</option>
                    <option value="0.5" ${mockTechData.model === '0.5' ? 'selected' : ''}>0.5</option>
                    <option value="不10" ${mockTechData.model === '不10' ? 'selected' : ''}>不10</option>
                </select>
            `;
            
            resultContainer.innerHTML = `
                <div class="success">
                    🎨 表单渲染模拟<br><br>
                    
                    <strong>模拟数据：</strong><br>
                    • 技师昵称: ${mockTechData.nick}<br>
                    • 手机号: ${mockTechData.phone}<br>
                    • 型号值: "${mockTechData.model}"<br><br>
                    
                    <strong>生成的HTML：</strong><br>
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0;">
                        <code style="font-size: 11px;">${htmlTemplate.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code>
                    </div>
                    
                    <strong>实际渲染效果：</strong><br>
                    <div style="margin: 10px 0;">
                        ${htmlTemplate}
                    </div>
                </div>
            `;
            
            log('表单渲染模拟完成');
        }
        
        // 加载技师数据
        async function loadTechnicianData() {
            const phone = document.getElementById('test-phone').value.trim();
            const modelSelect = document.getElementById('test-model-select');
            
            if (!phone) {
                alert('请输入手机号');
                return;
            }
            
            log(`开始加载技师数据: ${phone}`);
            
            try {
                const response = await fetch('technician_status.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'phone=' + encodeURIComponent(phone)
                });
                
                const data = await response.json();
                log(`技师数据加载结果: ${JSON.stringify(data)}`);
                
                if (data.success && data.data) {
                    const techData = data.data;
                    
                    // 设置型号选择框的值
                    modelSelect.value = techData.model || '';
                    
                    log(`设置型号选择框值: "${techData.model}"`);
                    log(`选择框当前值: "${modelSelect.value}"`);
                    log(`选择框选中选项: "${modelSelect.options[modelSelect.selectedIndex].text}"`);
                    
                    alert(`数据加载成功！\n技师: ${techData.nick}\n型号: ${techData.model || '未设置'}`);
                } else {
                    alert('未找到技师数据: ' + (data.msg || '未知错误'));
                }
            } catch (error) {
                log(`加载技师数据失败: ${error.message}`, 'error');
                alert('加载失败: ' + error.message);
            }
        }
        
        // 清空测试表单
        function clearTestForm() {
            document.getElementById('test-phone').value = '';
            document.getElementById('test-model-select').value = '';
            log('测试表单已清空');
        }
        
        // 页面加载时自动开始检查
        document.addEventListener('DOMContentLoaded', function() {
            log('调试页面加载完成');
            setTimeout(checkTechnicianAPI, 1000);
        });
    </script>
</body>
</html>
