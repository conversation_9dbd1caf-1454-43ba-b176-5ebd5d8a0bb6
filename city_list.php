<?php
// city_list.php - 获取城市列表
header('Content-Type: application/json; charset=utf-8');
require_once 'db.php';

try {
    // 查询城市列表
    $stmt = $pdo->query("SELECT id, name, lng, lat, created_at, is_hot FROM city ORDER BY name ASC");
    $cities = $stmt ? $stmt->fetchAll() : [];
    
    // 格式化数据
    $result = [];
    foreach ($cities as $city) {
        $result[] = [
            'id' => $city['id'],
            'name' => $city['name'],
            'lng' => $city['lng'],
            'lat' => $city['lat'],
            'created_at' => $city['created_at'],
            'is_hot' => $city['is_hot'] ? true : false
        ];
    }
    
    echo json_encode([
        'success' => true,
        'data' => $result,
        'total' => count($result)
    ]);

} catch (Exception $e) {
    error_log("City list error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'msg' => '获取城市列表失败: ' . $e->getMessage(),
        'data' => []
    ]);
}
?>
