<?php
// city_list.php - 获取城市列表

// 禁用错误输出到浏览器，防止HTML错误信息污染JSON响应
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// 开启输出缓冲，确保只输出JSON
ob_start();

header('Content-Type: application/json; charset=utf-8');

// 统一的JSON响应函数
function sendJsonResponse($success, $message, $data = null) {
    ob_clean();
    $response = ['success' => $success, 'msg' => $message];
    if ($data !== null) {
        $response['data'] = $data;
    }
    echo json_encode($response);
    exit;
}

// 注册致命错误处理器
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '服务器内部错误', 'data' => []]);
        exit;
    }
});

try {
    require_once 'db.php';

    // 检查数据库连接
    if ($pdo === null) {
        sendJsonResponse(false, '数据库连接失败', []);
    }
} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    sendJsonResponse(false, '数据库连接失败', []);
}

try {
    // 查询城市列表
    $stmt = $pdo->query("SELECT id, name, lng, lat, created_at, is_hot FROM city ORDER BY name ASC");
    $cities = $stmt ? $stmt->fetchAll() : [];
    
    // 格式化数据
    $result = [];
    foreach ($cities as $city) {
        $result[] = [
            'id' => $city['id'],
            'name' => $city['name'],
            'lng' => $city['lng'],
            'lat' => $city['lat'],
            'created_at' => $city['created_at'],
            'is_hot' => $city['is_hot'] ? true : false
        ];
    }
    
    sendJsonResponse(true, '获取成功', $result);

} catch (Exception $e) {
    error_log("City list error: " . $e->getMessage());
    sendJsonResponse(false, '获取城市列表失败，请稍后重试', []);
}
?>
