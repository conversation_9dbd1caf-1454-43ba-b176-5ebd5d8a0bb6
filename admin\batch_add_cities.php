<?php
// batch_add_cities.php - 批量添加中国所有市
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

// 中国热门大城市列表（只包含知名度高的大城市）
$hotCities = [
    // 直辖市（4个）
    '北京市', '上海市', '天津市', '重庆市',

    // 副省级城市和省会城市（27个）
    '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市', '西安市',
    '郑州市', '济南市', '青岛市', '大连市', '沈阳市', '长春市', '哈尔滨市',
    '石家庄市', '太原市', '呼和浩特市', '南昌市', '合肥市', '福州市', '厦门市',
    '长沙市', '南宁市', '海口市', '贵阳市', '昆明市', '拉萨市', '兰州市',
    '西宁市', '银川市', '乌鲁木齐市',

    // 经济发达的地级市（35个）
    '苏州市', '无锡市', '宁波市', '温州市', '佛山市', '东莞市', '中山市', '珠海市',
    '常州市', '徐州市', '扬州市', '泰州市', '南通市', '盐城市', '淮安市',
    '嘉兴市', '湖州市', '绍兴市', '金华市', '台州市', '丽水市',
    '泉州市', '漳州市', '莆田市', '三明市', '龙岩市', '宁德市',
    '烟台市', '潍坊市', '临沂市', '济宁市', '泰安市', '威海市', '日照市',
    '洛阳市', '开封市', '新乡市', '焦作市', '安阳市', '平顶山市',
    '宜昌市', '襄阳市', '荆州市', '黄石市', '十堰市',
    '株洲市', '湘潭市', '衡阳市', '岳阳市', '常德市', '张家界市',
    '柳州市', '桂林市', '北海市', '梧州市',
    '三亚市', '儋州市',
    '绵阳市', '德阳市', '宜宾市', '南充市', '乐山市',
    '遵义市', '安顺市',
    '大理市', '丽江市', '曲靖市', '玉溪市',
    '宝鸡市', '咸阳市', '渭南市', '汉中市',
    '包头市', '鄂尔多斯市', '呼伦贝尔市',
    '大庆市', '齐齐哈尔市', '牡丹江市',
    '吉林市', '延边朝鲜族自治州',
    '鞍山市', '抚顺市', '锦州市', '营口市',
    '唐山市', '保定市', '廊坊市', '秦皇岛市',
    '大同市', '运城市', '临汾市',
    '芜湖市', '蚌埠市', '阜阳市', '安庆市', '马鞍山市',
    '九江市', '赣州市', '上饶市', '宜春市',
    '淄博市', '枣庄市', '东营市', '聊城市', '德州市', '滨州市',
    '克拉玛依市', '喀什市', '伊犁哈萨克自治州'
];

try {
    $successCount = 0;
    $skipCount = 0;
    $errorCount = 0;
    $errors = [];

    // 检查每个城市是否已存在，如果不存在则添加
    $checkStmt = $pdo->prepare("SELECT id FROM city WHERE name = ?");
    $insertStmt = $pdo->prepare("INSERT INTO city (name, created_at) VALUES (?, NOW())");

    foreach ($hotCities as $cityName) {
        try {
            // 检查是否已存在
            $checkStmt->execute([$cityName]);
            if ($checkStmt->fetch()) {
                $skipCount++;
                continue;
            }

            // 插入新城市
            if ($insertStmt->execute([$cityName])) {
                $successCount++;
            } else {
                $errorCount++;
                $errors[] = "插入 {$cityName} 失败";
            }
        } catch (Exception $e) {
            $errorCount++;
            $errors[] = "处理 {$cityName} 时出错: " . $e->getMessage();
        }
    }

    echo json_encode([
        'success' => true,
        'msg' => "批量添加完成",
        'data' => [
            'total' => count($hotCities),
            'success' => $successCount,
            'skipped' => $skipCount,
            'errors' => $errorCount,
            'error_details' => $errors
        ]
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'msg' => '批量添加失败: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
