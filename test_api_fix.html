<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .btn-test {
            background: #28a745;
        }
        .btn-test:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 热门城市API修复测试</h1>
        
        <div class="test-section">
            <h3>📋 获取城市列表</h3>
            <button onclick="loadCities()">获取城市列表</button>
            <div id="cities-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>⭐ 测试设为热门城市</h3>
            <p style="color: #666; font-size: 14px;">
                这将测试修复后的API，使用正确的JSON格式发送数据
            </p>
            <button class="btn-test" onclick="testSetHot()">测试设为热门</button>
            <button class="btn-test" onclick="testCancelHot()">测试取消热门</button>
            <div id="hot-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🔍 API调试信息</h3>
            <button onclick="testAPIDebug()">查看API调试信息</button>
            <div id="debug-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 验证结果</h3>
            <button onclick="verifyChanges()">验证热门状态变更</button>
            <div id="verify-result" class="result"></div>
        </div>
    </div>

    <script>
        let testCities = [];
        
        function loadCities() {
            const resultDiv = document.getElementById('cities-result');
            resultDiv.textContent = '正在加载城市列表...';
            resultDiv.className = 'result';
            
            fetch('admin/city_list.php')
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data)) {
                        testCities = data;
                        const hotCities = data.filter(city => city.is_hot == 1);
                        const normalCities = data.filter(city => city.is_hot != 1);
                        
                        resultDiv.textContent = `城市列表加载成功！

总城市数: ${data.length}
热门城市: ${hotCities.length} 个
普通城市: ${normalCities.length} 个

前5个城市:
${data.slice(0, 5).map(city => `${city.id}. ${city.name} ${city.is_hot == 1 ? '⭐热门' : '普通'}`).join('\n')}

可用于测试的城市ID: ${data.slice(0, 3).map(city => city.id).join(', ')}`;
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.textContent = '加载失败: ' + JSON.stringify(data, null, 2);
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '网络错误: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function testSetHot() {
            if (testCities.length === 0) {
                alert('请先加载城市列表');
                return;
            }
            
            // 找一个普通城市来设为热门
            const normalCity = testCities.find(city => city.is_hot != 1);
            if (!normalCity) {
                alert('没有普通城市可以设为热门');
                return;
            }
            
            testHotAPI(normalCity.id, 1, '设为热门');
        }
        
        function testCancelHot() {
            if (testCities.length === 0) {
                alert('请先加载城市列表');
                return;
            }
            
            // 找一个热门城市来取消热门
            const hotCity = testCities.find(city => city.is_hot == 1);
            if (!hotCity) {
                alert('没有热门城市可以取消');
                return;
            }
            
            testHotAPI(hotCity.id, 0, '取消热门');
        }
        
        function testHotAPI(cityId, isHot, action) {
            const resultDiv = document.getElementById('hot-result');
            const city = testCities.find(c => c.id == cityId);
            
            resultDiv.textContent = `正在测试${action}...
城市: ${city.name} (ID: ${cityId})
操作: ${action} (is_hot: ${isHot})`;
            resultDiv.className = 'result';
            
            fetch('admin/city_set_hot.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: cityId,
                    is_hot: isHot
                })
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.textContent = `${action}测试结果:

请求数据:
- 城市ID: ${cityId}
- 城市名称: ${city.name}
- 操作: ${action}
- is_hot: ${isHot}

API响应:
${JSON.stringify(data, null, 2)}

${data.success ? '✅ 操作成功！' : '❌ 操作失败！'}`;
                resultDiv.className = data.success ? 'result success' : 'result error';
                
                if (data.success) {
                    // 更新本地数据
                    const cityIndex = testCities.findIndex(c => c.id == cityId);
                    if (cityIndex !== -1) {
                        testCities[cityIndex].is_hot = isHot;
                    }
                }
            })
            .catch(error => {
                resultDiv.textContent = `${action}测试失败:

网络错误: ${error.message}

请检查：
1. 是否已登录后台管理系统
2. API文件是否存在
3. 网络连接是否正常`;
                resultDiv.className = 'result error';
            });
        }
        
        function testAPIDebug() {
            const resultDiv = document.getElementById('debug-result');
            
            if (testCities.length === 0) {
                resultDiv.textContent = '请先加载城市列表';
                resultDiv.className = 'result error';
                return;
            }
            
            const testCity = testCities[0];
            resultDiv.textContent = '正在发送调试请求...';
            resultDiv.className = 'result';
            
            // 发送一个故意错误的请求来查看调试信息
            fetch('admin/city_set_hot.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    // 故意不发送id字段来触发调试信息
                    is_hot: 1
                })
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.textContent = `API调试信息:

发送的数据: {"is_hot": 1} (故意不包含id)

API响应:
${JSON.stringify(data, null, 2)}

这应该显示"城市ID不能为空"的错误和调试信息`;
                resultDiv.className = 'result error';
            })
            .catch(error => {
                resultDiv.textContent = '调试请求失败: ' + error.message;
                resultDiv.className = 'result error';
            });
        }
        
        function verifyChanges() {
            const resultDiv = document.getElementById('verify-result');
            resultDiv.textContent = '正在验证热门状态变更...';
            resultDiv.className = 'result';
            
            // 重新加载城市列表来验证变更
            fetch('admin/city_list.php')
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data)) {
                        const hotCities = data.filter(city => city.is_hot == 1);
                        const normalCities = data.filter(city => city.is_hot != 1);
                        
                        // 比较变更
                        const oldHotCount = testCities.filter(city => city.is_hot == 1).length;
                        const newHotCount = hotCities.length;
                        const changed = oldHotCount !== newHotCount;
                        
                        resultDiv.textContent = `验证结果:

${changed ? '✅ 检测到热门状态变更' : '⚠️ 未检测到变更'}

统计对比:
- 操作前热门城市: ${oldHotCount} 个
- 操作后热门城市: ${newHotCount} 个
- 变化: ${newHotCount - oldHotCount > 0 ? '+' : ''}${newHotCount - oldHotCount}

当前热门城市:
${hotCities.slice(0, 10).map(city => `- ${city.name} (ID: ${city.id})`).join('\n')}
${hotCities.length > 10 ? `... 还有 ${hotCities.length - 10} 个` : ''}`;
                        
                        resultDiv.className = 'result success';
                        testCities = data; // 更新本地数据
                    } else {
                        resultDiv.textContent = '验证失败: ' + JSON.stringify(data, null, 2);
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '验证错误: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        // 页面加载时自动加载城市列表
        window.onload = function() {
            loadCities();
        };
    </script>
</body>
</html>
