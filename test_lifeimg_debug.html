<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生活照上传问题调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-section h3 {
            color: #495057;
            margin-top: 0;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.danger {
            background: #dc3545;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 生活照上传问题调试</h1>
        
        <div class="test-section">
            <h3>🐛 问题描述</h3>
            <p><strong>现象：</strong> 工作照片可以更换，但生活照片上传2张时显示 HTTP 500 错误</p>
            <p><strong>可能原因：</strong></p>
            <ul>
                <li>多文件上传处理逻辑错误</li>
                <li>PHP 配置限制</li>
                <li>文件数组访问错误</li>
                <li>内存或执行时间限制</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔍 步骤1: 检查PHP配置</h3>
            <p>首先检查服务器的PHP配置是否支持多文件上传：</p>
            <button class="btn" onclick="checkPHPConfig()">检查PHP配置</button>
            <div id="phpConfigResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🧪 步骤2: 测试生活照上传</h3>
            <p>使用调试版本测试生活照上传功能：</p>
            <button class="btn secondary" onclick="testLifeimgUpload()">测试生活照上传</button>
            <div id="lifeimgTestResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔧 步骤3: 调试技师编辑</h3>
            <p>使用调试版本的技师编辑功能：</p>
            <form id="debugForm" enctype="multipart/form-data" style="margin-bottom: 15px;">
                <div style="margin-bottom: 10px;">
                    <label>技师ID: <input type="number" name="id" value="1" required style="padding: 5px;"></label>
                </div>
                <div style="margin-bottom: 10px;">
                    <label>昵称: <input type="text" name="nick" value="测试技师" required style="padding: 5px;"></label>
                </div>
                <div style="margin-bottom: 10px;">
                    <label>生活照 (选择2张): <input type="file" name="lifeimg" accept="image/*" multiple style="padding: 5px;"></label>
                </div>
                <button type="submit" class="btn">调试上传</button>
            </form>
            <div id="debugResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📋 解决方案</h3>
            <div id="solutions" style="display: none;">
                <h4>根据测试结果，可能的解决方案：</h4>
                <ul id="solutionList"></ul>
            </div>
        </div>
    </div>

    <script>
        function checkPHPConfig() {
            const resultDiv = document.getElementById('phpConfigResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '检查中...';
            resultDiv.className = 'result';

            fetch('admin/check_php_config.php')
                .then(response => response.json())
                .then(data => {
                    resultDiv.textContent = JSON.stringify(data, null, 2);
                    resultDiv.className = 'result success';
                    analyzePHPConfig(data);
                })
                .catch(error => {
                    resultDiv.textContent = '错误: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }

        function testLifeimgUpload() {
            const resultDiv = document.getElementById('lifeimgTestResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<a href="admin/test_lifeimg_upload.php" target="_blank" class="btn">打开生活照上传测试页面</a>';
        }

        function analyzePHPConfig(data) {
            const solutions = document.getElementById('solutions');
            const solutionList = document.getElementById('solutionList');
            
            solutions.style.display = 'block';
            solutionList.innerHTML = '';

            if (data.config) {
                if (data.config.file_uploads !== '1') {
                    solutionList.innerHTML += '<li>启用 file_uploads 配置</li>';
                }
                if (parseInt(data.config.max_file_uploads) < 20) {
                    solutionList.innerHTML += '<li>增加 max_file_uploads 到 20 或更多</li>';
                }
                if (!data.config.uploads_dir_writable) {
                    solutionList.innerHTML += '<li>确保 uploads 目录可写</li>';
                }
                if (!data.config.finfo_available) {
                    solutionList.innerHTML += '<li>启用 fileinfo 扩展</li>';
                }
            }

            if (solutionList.innerHTML === '') {
                solutionList.innerHTML = '<li>PHP 配置看起来正常，问题可能在代码逻辑中</li>';
            }
        }

        document.getElementById('debugForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('debugResult');
            
            resultDiv.style.display = 'block';
            resultDiv.textContent = '上传中...';
            resultDiv.className = 'result';
            
            fetch('admin/tech_edit_debug.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = data.success ? 'result success' : 'result error';
                
                if (data.debug) {
                    analyzeDebugResult(data.debug);
                }
            })
            .catch(error => {
                resultDiv.textContent = '错误: ' + error.message;
                resultDiv.className = 'result error';
            });
        });

        function analyzeDebugResult(debug) {
            const solutions = document.getElementById('solutions');
            const solutionList = document.getElementById('solutionList');
            
            solutions.style.display = 'block';
            
            if (debug.lifeimg_structure) {
                if (!debug.is_multiple) {
                    solutionList.innerHTML += '<li>文件上传格式不是多文件数组格式</li>';
                } else if (debug.file_count === 0) {
                    solutionList.innerHTML += '<li>没有检测到上传的文件</li>';
                } else if (debug.validation_error) {
                    solutionList.innerHTML += '<li>文件验证失败: ' + debug.validation_error + '</li>';
                } else if (debug.upload_error) {
                    solutionList.innerHTML += '<li>文件上传失败: ' + debug.upload_error + '</li>';
                } else if (debug.upload_success) {
                    solutionList.innerHTML += '<li style="color: green;">调试上传成功！问题可能在原始代码的复杂逻辑中</li>';
                }
            }
        }
    </script>
</body>
</html>
