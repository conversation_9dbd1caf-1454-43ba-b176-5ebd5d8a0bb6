<?php
// 路费管理文章接口
header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';
if (!$pdo) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['success'=>false, 'msg'=>'数据库连接失败']);
    exit;
}

// 数据库表名
define('TABLE', 'taxi_article');

// 创建表（如不存在）
$create_sql = "CREATE TABLE IF NOT EXISTS `".TABLE."` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `title` VARCHAR(50) NOT NULL,
  `content` TEXT NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
$pdo->exec($create_sql);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $content = trim($_POST['content'] ?? '');
    error_log('[taxi_article.php] POST title: ' . $title . ' | content: ' . $content);
    if ($title === '' || $content === '') {
        error_log('[taxi_article.php] POST empty title or content');
        echo json_encode(['success'=>false, 'msg'=>'标题和内容不能为空']);
        exit;
    }
    // 只保留一条记录，更新或插入
    $check = $pdo->query("SELECT id FROM `".TABLE."` LIMIT 1");
    $row = $check->fetch(PDO::FETCH_ASSOC);
    if ($row) {
        error_log('[taxi_article.php] UPDATE id: ' . $row['id']);
        $stmt = $pdo->prepare("UPDATE `".TABLE."` SET title=?, content=?, updated_at=NOW() WHERE id=?");
        $stmt->execute([$title, $content, $row['id']]);
    } else {
        error_log('[taxi_article.php] INSERT');
        $stmt = $pdo->prepare("INSERT INTO `".TABLE."` (title, content) VALUES (?, ?)");
        $stmt->execute([$title, $content]);
    }
    error_log('[taxi_article.php] 保存成功');
    echo json_encode(['success'=>true, 'msg'=>'保存成功']);
    exit;
}
// GET: 返回最新一条

$res = $pdo->query("SELECT title, content FROM `".TABLE."` ORDER BY updated_at DESC LIMIT 1");
$row = $res->fetch(PDO::FETCH_ASSOC);
if ($row) {
    echo json_encode(['success'=>true, 'data'=>['title'=>$row['title'], 'content'=>$row['content']]]);
} else {
    echo json_encode(['success'=>true, 'data'=>['title'=>'', 'content'=>'']]);
}
