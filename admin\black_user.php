<?php
require_once '../db.php';
header('Content-Type: application/json; charset=utf-8');

$id = isset($_POST['id']) ? intval($_POST['id']) : 0;
if ($id <= 0) {
    echo json_encode(['success' => false, 'msg' => '参数错误']);
    exit;
}
// user表需有is_black字段，没有则自动添加
$pdo->exec("ALTER TABLE user ADD COLUMN IF NOT EXISTS is_black TINYINT(1) DEFAULT 0;");
$stmt = $pdo->prepare("UPDATE user SET is_black=1 WHERE id=?");
$res = $stmt->execute([$id]);
if ($res) {
    echo json_encode(['success' => true, 'msg' => '已拉黑']);
} else {
    echo json_encode(['success' => false, 'msg' => '操作失败']);
}
exit;
