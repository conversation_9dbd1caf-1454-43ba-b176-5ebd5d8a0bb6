<?php
// feed_delete.php - 删除动态
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

try {
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    
    if ($id <= 0) {
        echo json_encode(['success' => false, 'msg' => '无效的动态ID']);
        exit;
    }

    // 查询动态信息，获取关联文件用于删除
    $stmt = $pdo->prepare("SELECT id, user_id, content, images, video FROM feeds WHERE id = ?");
    $stmt->execute([$id]);
    $feed = $stmt->fetch();
    
    if (!$feed) {
        echo json_encode(['success' => false, 'msg' => '动态不存在']);
        exit;
    }

    // 删除关联的文件
    $filesToDelete = [];
    
    // 处理图片文件
    if ($feed['images']) {
        $images = json_decode($feed['images'], true);
        if (is_array($images)) {
            foreach ($images as $image) {
                if ($image) {
                    $filesToDelete[] = $image;
                }
            }
        }
    }
    
    // 处理视频文件
    if ($feed['video']) {
        $filesToDelete[] = $feed['video'];
    }

    // 删除数据库记录
    $stmt = $pdo->prepare("DELETE FROM feeds WHERE id = ?");
    $result = $stmt->execute([$id]);
    
    if ($result) {
        // 删除关联文件
        foreach ($filesToDelete as $file) {
            $filePath = __DIR__ . '/../' . $file;
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
        
        // 获取用户信息用于日志
        $stmt = $pdo->prepare("SELECT nickname, phone FROM user WHERE id = ?");
        $stmt->execute([$feed['user_id']]);
        $user = $stmt->fetch();
        $userName = $user ? ($user['nickname'] ?: $user['phone']) : "用户ID: {$feed['user_id']}";
        $userInfo = $user ? "用户: {$userName}({$user['phone']})" : "用户ID: {$feed['user_id']}";
        
        echo json_encode([
            'success' => true, 
            'msg' => "动态已删除 ({$userInfo})"
        ]);
    } else {
        echo json_encode(['success' => false, 'msg' => '删除失败']);
    }

} catch (Exception $e) {
    error_log("Feed delete error: " . $e->getMessage());
    echo json_encode(['success' => false, 'msg' => '删除失败: ' . $e->getMessage()]);
}
?>
