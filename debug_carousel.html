<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            border-left: 4px solid #007bff;
            max-height: 300px;
            overflow-y: auto;
        }
        .carousel-container {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
            border-radius: 12px;
            border: 2px solid #ddd;
            margin: 20px 0;
        }
        .carousel-wrapper {
            display: flex;
            width: 100%;
            height: 100%;
            transition: transform 0.5s ease-in-out;
        }
        .carousel-slide {
            min-width: 100%;
            height: 100%;
            position: relative;
        }
        .carousel-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }
        .carousel-indicators {
            position: absolute;
            bottom: 12px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
            z-index: 10;
        }
        .carousel-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .carousel-indicator.active {
            background: rgba(255, 255, 255, 0.9);
            transform: scale(1.2);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 轮播图调试工具</h1>
        
        <div id="status" class="status loading">正在初始化...</div>
        
        <div>
            <button class="btn" onclick="testAPI()">测试API</button>
            <button class="btn" onclick="loadCarousel()">加载轮播图</button>
            <button class="btn" onclick="clearDebug()">清除调试信息</button>
        </div>
        
        <div id="carousel" class="carousel-container">
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">
                等待加载轮播图...
            </div>
        </div>
        
        <div id="debug" class="debug-info">调试信息将显示在这里...</div>
    </div>

    <script>
        let debugInfo = [];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.push(`[${timestamp}] ${message}`);
            updateDebugDisplay();
        }
        
        function updateDebugDisplay() {
            const debugDiv = document.getElementById('debug');
            debugDiv.textContent = debugInfo.join('\n');
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }
        
        function updateStatus(message, type = 'loading') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function clearDebug() {
            debugInfo = [];
            updateDebugDisplay();
        }
        
        async function testAPI() {
            log('开始测试API...');
            updateStatus('测试API中...', 'loading');
            
            try {
                const response = await fetch('banner_list.php');
                log(`API响应状态: ${response.status}`);
                
                const text = await response.text();
                log(`原始响应: ${text}`);
                
                const data = JSON.parse(text);
                log(`解析后的数据: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    log(`API调用成功，轮播图数量: ${data.data.length}`);
                    updateStatus(`API测试成功，找到 ${data.data.length} 个轮播图`, 'success');
                    
                    if (data.data.length > 0) {
                        log('轮播图列表:');
                        data.data.forEach((item, index) => {
                            log(`  ${index + 1}. ID: ${item.id}, 图片: ${item.img}, 链接: ${item.link || '无'}`);
                        });
                    }
                } else {
                    log(`API调用失败: ${data.msg}`);
                    updateStatus(`API测试失败: ${data.msg}`, 'error');
                }
            } catch (error) {
                log(`API测试出错: ${error.message}`);
                updateStatus(`API测试出错: ${error.message}`, 'error');
            }
        }
        
        async function loadCarousel() {
            log('开始加载轮播图...');
            updateStatus('加载轮播图中...', 'loading');
            
            try {
                const response = await fetch('banner_list.php');
                const data = await response.json();
                
                if (data.success && Array.isArray(data.data) && data.data.length > 0) {
                    log(`成功获取 ${data.data.length} 个轮播图`);
                    renderCarousel(data.data);
                    updateStatus(`轮播图加载成功，共 ${data.data.length} 张`, 'success');
                } else {
                    log('没有轮播图数据');
                    showEmptyCarousel();
                    updateStatus('没有轮播图数据', 'error');
                }
            } catch (error) {
                log(`加载轮播图失败: ${error.message}`);
                showErrorCarousel();
                updateStatus(`加载失败: ${error.message}`, 'error');
            }
        }
        
        function renderCarousel(slides) {
            log('开始渲染轮播图...');
            const carousel = document.getElementById('carousel');
            
            const html = `
                <div class="carousel-wrapper">
                    ${slides.map((slide, index) => `
                        <div class="carousel-slide">
                            <img src="${slide.img}" 
                                 alt="轮播图${index + 1}"
                                 onload="log('图片加载成功: ${slide.img}')"
                                 onerror="log('图片加载失败: ${slide.img}'); this.src='images/lbt.png'"
                                 style="${slide.link ? 'cursor: pointer;' : ''}"
                                 ${slide.link ? `onclick="window.open('${slide.link}', '_blank')"` : ''} />
                        </div>
                    `).join('')}
                </div>
                
                ${slides.length > 1 ? `
                    <div class="carousel-indicators">
                        ${slides.map((_, index) => `
                            <div class="carousel-indicator ${index === 0 ? 'active' : ''}"
                                 onclick="goToSlide(${index})"></div>
                        `).join('')}
                    </div>
                ` : ''}
            `;
            
            carousel.innerHTML = html;
            log('轮播图渲染完成');
            
            // 测试图片加载
            slides.forEach((slide, index) => {
                const img = new Image();
                img.onload = () => log(`预加载成功: ${slide.img} (${img.naturalWidth}x${img.naturalHeight})`);
                img.onerror = () => log(`预加载失败: ${slide.img}`);
                img.src = slide.img;
            });
        }
        
        function showEmptyCarousel() {
            const carousel = document.getElementById('carousel');
            carousel.innerHTML = `
                <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; color: #999;">
                    <div style="font-size: 48px; margin-bottom: 12px;">🖼️</div>
                    <div>暂无轮播图</div>
                </div>
            `;
        }
        
        function showErrorCarousel() {
            const carousel = document.getElementById('carousel');
            carousel.innerHTML = `
                <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; color: #999;">
                    <div style="font-size: 48px; margin-bottom: 12px;">⚠️</div>
                    <div>轮播图加载失败</div>
                    <button onclick="loadCarousel()" style="margin-top: 12px; padding: 6px 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">重新加载</button>
                </div>
            `;
        }
        
        let currentSlide = 0;
        let totalSlides = 0;
        
        function goToSlide(index) {
            log(`切换到第 ${index + 1} 张轮播图`);
            currentSlide = index;
            
            const wrapper = document.querySelector('.carousel-wrapper');
            if (wrapper) {
                wrapper.style.transform = `translateX(-${index * 100}%)`;
            }
            
            // 更新指示器
            const indicators = document.querySelectorAll('.carousel-indicator');
            indicators.forEach((indicator, i) => {
                indicator.classList.toggle('active', i === index);
            });
        }
        
        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始自动测试');
            testAPI().then(() => {
                setTimeout(() => {
                    loadCarousel();
                }, 1000);
            });
        });
    </script>
</body>
</html>
