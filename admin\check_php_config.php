<?php
// 检查PHP配置
header('Content-Type: application/json; charset=utf-8');

$config = [
    'file_uploads' => ini_get('file_uploads'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_file_uploads' => ini_get('max_file_uploads'),
    'max_input_vars' => ini_get('max_input_vars'),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time'),
    'upload_tmp_dir' => ini_get('upload_tmp_dir'),
    'tmp_dir' => sys_get_temp_dir(),
    'uploads_dir_exists' => is_dir(__DIR__ . '/../uploads/'),
    'uploads_dir_writable' => is_writable(__DIR__ . '/../uploads/'),
    'finfo_available' => extension_loaded('fileinfo')
];

echo json_encode([
    'success' => true,
    'config' => $config,
    'recommendations' => [
        'file_uploads should be On' => $config['file_uploads'] == '1' ? 'OK' : 'PROBLEM',
        'upload_max_filesize should be >= 5M' => 'Check manually',
        'post_max_size should be >= upload_max_filesize' => 'Check manually',
        'max_file_uploads should be >= 20' => $config['max_file_uploads'] >= 20 ? 'OK' : 'LOW',
        'uploads directory writable' => $config['uploads_dir_writable'] ? 'OK' : 'PROBLEM',
        'fileinfo extension' => $config['finfo_available'] ? 'OK' : 'PROBLEM'
    ]
], JSON_PRETTY_PRINT);
?>
