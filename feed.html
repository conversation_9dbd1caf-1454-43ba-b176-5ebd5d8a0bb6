<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态</title>
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,500,700&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: 'Roboto', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: #222;
            min-height: 100vh;
            position: relative;
        }
        /* 底部导航栏样式，完全同步主页 */
        .bottom-nav {
            position: fixed;
            left: 16px;
            right: 16px;
            bottom: 16px;
            background: #fff;
            border-top: none;
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0,198,162,0.18), 0 2px 8px rgba(0,0,0,0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
            z-index: 120;
            transition: box-shadow 0.3s, bottom 0.3s;
        }
        .nav-item {
            text-align: center;
            font-size: 12px;
            color: #888;
            flex: 1;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .nav-item.active {
            color: #2196f3;
        }
        .nav-icon {
            display: block;
            font-size: 24px;
            margin-bottom: 2px;
        }
        .nav-item.center {
            z-index: 101;
            flex: none;
            width: auto;
            margin: 0 32px;
        }
        .nav-plus {
            position: absolute;
            top: -34px;
            left: 50%;
            transform: translateX(-50%);
            width: 48px;
            height: 48px;
            background: #00c6a2;
            border-radius: 50%;
            box-shadow: 0 12px 32px rgba(0,198,162,0.25), 0 2px 8px rgba(0,0,0,0.10);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 28px;
            border: 4px solid #fff;
            z-index: 130;
            transition: box-shadow 0.3s, top 0.3s;
            animation: navPlusFloat 1.2s infinite ease-in-out alternate;
            cursor: pointer;
        }
        @keyframes navPlusFloat {
            0% { box-shadow: 0 8px 24px rgba(0,198,162,0.18), 0 2px 8px rgba(0,0,0,0.10); top: -24px; }
            100% { box-shadow: 0 16px 32px rgba(0,198,162,0.25), 0 4px 16px rgba(0,0,0,0.12); top: -32px; }
        }
        .nav-plus:hover {
            box-shadow: 0 16px 48px 0 rgba(0,198,162,0.35), 0 4px 16px rgba(0,0,0,0.15);
            transform: translateX(-50%) scale(1.08);
        }

        /* 轮播图样式 */
        .carousel-container {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
            border-radius: 0 0 18px 18px;
            background: #fff;
            border: 1px solid #eee;
        }

        .carousel-wrapper {
            display: flex;
            width: 100%;
            height: 100%;
            transition: transform 0.5s ease-in-out;
        }

        .carousel-slide {
            min-width: 100%;
            width: 100%;
            height: 100%;
            position: relative;
            flex-shrink: 0;
        }

        .carousel-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
            border: none;
            outline: none;
        }

        .carousel-indicators {
            position: absolute;
            bottom: 12px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
            z-index: 10;
        }

        .carousel-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .carousel-indicator.active {
            background: rgba(255, 255, 255, 0.9);
            transform: scale(1.2);
        }

        .carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.3);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .carousel-nav:hover {
            background: rgba(0, 0, 0, 0.6);
            transform: translateY(-50%) scale(1.1);
        }

        .carousel-nav.prev {
            left: 12px;
        }

        .carousel-nav.next {
            right: 12px;
        }

        .carousel-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 180px;
            background: #f5f5f5;
            color: #999;
            font-size: 14px;
        }

        .carousel-error {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 180px;
            background: #f5f5f5;
            color: #999;
            font-size: 14px;
        }

        /* 动画效果 */
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .carousel-slide {
            animation: fadeIn 0.5s ease-out;
        }

        /* 其他页面内容样式可补充 */
    </style>
</head>
<body>
    <!-- 顶部轮播图 -->
    <div style="width:100%; background:#fff; border-radius:0 0 18px 18px; box-shadow:0 2px 8px rgba(0,0,0,0.04); margin-bottom: 20px;">
      <div id="carousel" class="carousel-container">
        <!-- 轮播图内容将通过JavaScript动态加载 -->
        <div class="carousel-loading">
          <div style="display: flex; align-items: center; justify-content: center; height: 200px; color: #999;">
            <div style="display: flex; align-items: center; gap: 8px;">
              <div style="width: 16px; height: 16px; border: 2px solid #ddd; border-top: 2px solid #00c6a2; border-radius: 50%; animation: spin 1s linear infinite;"></div>
              正在加载轮播图...
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 动态列表容器 -->
    <div style="padding: 0 16px 100px 16px;">
        <!-- 动态列表 -->
        <div id="feed-list">
            <!-- 动态内容将通过JavaScript动态加载 -->
        </div>

        <!-- 空状态提示 -->
        <div id="feed-empty" style="display: none; text-align: center; padding: 60px 20px; color: #999;">
            <div style="font-size: 48px; margin-bottom: 16px;">📱</div>
            <div style="font-size: 16px; margin-bottom: 8px;">正在开发</div>
            <div style="font-size: 14px;">敬请期待</div>
        </div>

        <!-- 加载更多 -->
        <div id="load-more" style="display: none; text-align: center; padding: 20px;">
            <button style="background: #f5f5f5; border: none; padding: 12px 24px; border-radius: 20px; color: #666; cursor: pointer;">
                加载更多
            </button>
        </div>
    </div>

    <div class="bottom-nav">
    <div class="nav-item" id="nav-home" onclick="window.location.href='index.html'">
        <span class="nav-icon">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><path d="M3 10.5L12 4l9 6.5V20a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V10.5z" stroke="#888" stroke-width="2"/></svg>
        </span>
        首页
    </div>
    <div class="nav-item" id="nav-technicians" onclick="window.location.href='technicians.html'">
        <span class="nav-icon">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><rect x="4" y="4" width="16" height="16" rx="4" stroke="#888" stroke-width="2"/><path d="M8 8h8v8H8V8z" stroke="#888" stroke-width="2"/></svg>
        </span>
        技师
    </div>
    <div class="nav-item center">
        <span class="nav-plus">
            <svg width="38" height="38" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="12" r="10" fill="#00c6a2"/><path d="M12 8v8M8 12h8" stroke="#fff" stroke-width="2"/></svg>
        </span>
    </div>
    <div class="nav-item active" id="nav-feed">
        <span class="nav-icon">
            <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g stroke="#2196f3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M14 14L22 7.5C20.5 5.5 17.5 5.5 16 7.5C14.5 9.5 14 14 14 14Z"/>
                    <path d="M14 14L6 7.5C7.5 5.5 10.5 5.5 12 7.5C13.5 9.5 14 14 14 14Z"/>
                    <path d="M14 14L6 20.5C7.5 22.5 10.5 22.5 12 20.5C13.5 18.5 14 14 14 14Z"/>
                    <path d="M14 14L22 20.5C20.5 22.5 17.5 22.5 16 20.5C14.5 18.5 14 14 14 14Z"/>
                    <circle cx="14" cy="14" r="2.5" fill="#2196f3" stroke="none"/>
                </g>
            </svg>
        </span>
        动态
    </div>
    <div class="nav-item" id="nav-mine" onclick="window.location.href='profile.html'">
        <span class="nav-icon">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="8" r="4" stroke="#888" stroke-width="2"/><path d="M4 20c0-2.2 3.6-4 8-4s8 1.8 8 4" stroke="#888" stroke-width="2"/></svg>
        </span>
        我的
    </div>
</div>
    <script>
// 轮播图功能
class Carousel {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.currentSlide = 0;
        this.slides = [];
        this.autoPlayInterval = null;
        this.autoPlayDelay = 4000; // 4秒自动切换

        this.init();
    }

    async init() {
        console.log('轮播图初始化开始...');
        try {
            await this.loadBanners();
            if (this.slides.length > 0) {
                console.log('有轮播图数据，开始渲染');
                this.render();
                this.bindEvents();
                this.startAutoPlay();
                console.log('轮播图初始化完成');
            } else {
                console.log('没有轮播图数据，显示空状态');
                this.showEmptyState();
            }
        } catch (error) {
            console.error('轮播图初始化失败:', error);
            this.showErrorState();
        }
    }

    async loadBanners() {
        console.log('开始加载轮播图...');
        const response = await fetch('banner_list.php');
        console.log('API响应状态:', response.status);

        const data = await response.json();
        console.log('API返回数据:', data);

        if (data.success && Array.isArray(data.data)) {
            this.slides = data.data;
            console.log('轮播图数据加载成功，数量:', this.slides.length);
        } else {
            console.error('轮播图数据加载失败:', data);
            throw new Error(data.msg || '获取轮播图失败');
        }
    }

    render() {
        console.log('开始渲染轮播图，数量:', this.slides.length);
        if (this.slides.length === 0) {
            console.log('没有轮播图数据，显示空状态');
            this.showEmptyState();
            return;
        }
        console.log('轮播图数据:', this.slides);

        const html = `
            <div class="carousel-wrapper" id="carousel-wrapper">
                ${this.slides.map((slide, index) => `
                    <div class="carousel-slide" data-index="${index}">
                        <img src="${slide.img}" alt="轮播图${index + 1}"
                             onerror="this.src='images/lbt.png'"
                             ${slide.link ? `onclick="window.open('${slide.link}', '_blank')"` : ''}
                             style="${slide.link ? 'cursor: pointer;' : ''}" />
                    </div>
                `).join('')}
            </div>

            ${this.slides.length > 1 ? `
                <!-- 导航按钮 -->
                <button class="carousel-nav prev" onclick="carousel.prevSlide()">‹</button>
                <button class="carousel-nav next" onclick="carousel.nextSlide()">›</button>

                <!-- 指示器 -->
                <div class="carousel-indicators">
                    ${this.slides.map((_, index) => `
                        <div class="carousel-indicator ${index === 0 ? 'active' : ''}"
                             onclick="carousel.goToSlide(${index})"></div>
                    `).join('')}
                </div>
            ` : ''}
        `;

        this.container.innerHTML = html;
        this.wrapper = document.getElementById('carousel-wrapper');
        console.log('轮播图HTML已插入到容器中');
    }

    showEmptyState() {
        this.container.innerHTML = `
            <div class="carousel-error">
                <div style="font-size: 48px; margin-bottom: 12px;">🖼️</div>
                <div>暂无轮播图</div>
                <div style="font-size: 12px; margin-top: 4px; opacity: 0.7;">管理员还未上传轮播图</div>
            </div>
        `;
    }

    showErrorState() {
        this.container.innerHTML = `
            <div class="carousel-error">
                <div style="font-size: 48px; margin-bottom: 12px;">⚠️</div>
                <div>轮播图加载失败</div>
                <button onclick="carousel.init()" style="
                    margin-top: 12px; padding: 6px 12px; background: #00c6a2;
                    color: white; border: none; border-radius: 4px; cursor: pointer;
                ">重新加载</button>
            </div>
        `;
    }

    bindEvents() {
        if (this.slides.length <= 1) return;

        // 触摸滑动支持
        let startX = 0;
        let startY = 0;
        let isDragging = false;

        this.container.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            isDragging = true;
            this.stopAutoPlay();
        });

        this.container.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            e.preventDefault();
        });

        this.container.addEventListener('touchend', (e) => {
            if (!isDragging) return;
            isDragging = false;

            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const deltaX = endX - startX;
            const deltaY = endY - startY;

            // 判断是否为水平滑动
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0) {
                    this.prevSlide();
                } else {
                    this.nextSlide();
                }
            }

            this.startAutoPlay();
        });

        // 鼠标悬停暂停自动播放
        this.container.addEventListener('mouseenter', () => this.stopAutoPlay());
        this.container.addEventListener('mouseleave', () => this.startAutoPlay());
    }

    goToSlide(index) {
        if (index < 0 || index >= this.slides.length) return;

        this.currentSlide = index;
        this.updateSlidePosition();
        this.updateIndicators();
    }

    nextSlide() {
        const nextIndex = (this.currentSlide + 1) % this.slides.length;
        this.goToSlide(nextIndex);
    }

    prevSlide() {
        const prevIndex = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
        this.goToSlide(prevIndex);
    }

    updateSlidePosition() {
        if (!this.wrapper) return;
        const translateX = -this.currentSlide * 100;
        this.wrapper.style.transform = `translateX(${translateX}%)`;
    }

    updateIndicators() {
        const indicators = this.container.querySelectorAll('.carousel-indicator');
        indicators.forEach((indicator, index) => {
            indicator.classList.toggle('active', index === this.currentSlide);
        });
    }

    startAutoPlay() {
        if (this.slides.length <= 1) return;

        this.stopAutoPlay();
        this.autoPlayInterval = setInterval(() => {
            this.nextSlide();
        }, this.autoPlayDelay);
    }

    stopAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }

    destroy() {
        this.stopAutoPlay();
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}

// 全局轮播图实例
let carousel;

// 首页按钮跳转主页
document.getElementById('nav-home').addEventListener('click', function() {
  window.location.href = 'index.html';
});
// 动态按钮跳转动态页
document.getElementById('nav-feed').addEventListener('click', function() {
  window.location.href = 'feed.html';
});
// 我的按钮判断登录
document.getElementById('nav-mine').addEventListener('click', function() {
  if (localStorage.getItem('isLogin') === '1') {
    window.location.href = 'profile.html';
  } else {
    window.location.href = 'login.html';
  }
});

// 动态列表渲染
function renderFeedList(list) {
  console.log('开始渲染动态列表，数据:', list);
  var feedList = document.getElementById('feed-list');
  console.log('动态列表容器:', feedList);

  if (!feedList) {
    console.error('找不到动态列表容器 #feed-list');
    return;
  }

  // 先隐藏空状态
  var feedEmpty = document.getElementById('feed-empty');
  if (feedEmpty) {
    feedEmpty.style.display = 'none';
  }

  // 清空现有内容
  feedList.innerHTML = '';

  if (!list || list.length === 0) {
    console.log('没有动态数据，显示空状态');
    if (feedEmpty) {
      feedEmpty.style.display = 'block';
    }
    return;
  }

  console.log('有动态数据，开始渲染', list.length, '条动态');
  list.forEach(function(feed) {
    var card = document.createElement('div');
    card.className = 'feed-card';
    card.style.cssText = 'background:#fff; border-radius:16px; box-shadow:0 2px 8px rgba(0,0,0,0.04); margin:16px 0 0 0; padding:18px 18px 12px 18px;';
    // 头部
    var head = document.createElement('div');
    head.style.cssText = 'display:flex; align-items:center; justify-content:space-between;';
    var left = document.createElement('div');
    left.style.cssText = 'display:flex; align-items:center;';
    var avatar = document.createElement('img');
    avatar.src = feed.avatar || 'images/tx.jpg';
    avatar.alt = '头像';
    avatar.style.cssText = 'width:38px; height:38px; border-radius:50%; object-fit:cover; margin-right:10px;';
    left.appendChild(avatar);
    var info = document.createElement('div');
    info.style.cssText = 'display:flex; flex-direction:column;';
    var nick = document.createElement('div');
    // 优先显示昵称，若无则显示手机号
    nick.textContent = feed.nickname || feed.nick || feed.phone || '用户';
    nick.style.cssText = 'font-size:16px; color:#222; font-weight:500;';
    var time = document.createElement('div');
    time.textContent = feed.time || feed.created_at || '';
    time.style.cssText = 'font-size:12px; color:#bbb; margin-top:2px;';
    info.appendChild(nick);
    info.appendChild(time);
    // 地理位置显示（如有）
    if (feed.location) {
      var loc = document.createElement('div');
      loc.textContent = feed.location;
      loc.style.cssText = 'font-size:12px; color:#00c6a2; margin-top:2px;';
      info.appendChild(loc);
    }
    left.appendChild(info);
    head.appendChild(left);
    // 技师动态不需要菜单功能
    card.appendChild(head);
    // 内容
    var content = document.createElement('div');
    content.style.cssText = 'font-size:15px; color:#222; line-height:1.7; margin:12px 0 0 0;';
    content.textContent = feed.content || '';
    card.appendChild(content);
    // 图片/视频 3:4比例显示，居左
    if (feed.images && Array.isArray(feed.images) && feed.images.length > 0) {
      var imgWrap = document.createElement('div');
      imgWrap.style.cssText = 'margin:14px 0 0 0; border-radius:12px; overflow:hidden; display:flex; gap:12px; flex-wrap:wrap; justify-content:flex-start;';
      feed.images.forEach(function(img) {
        if (img.match(/\.(mp4|webm|ogg)$/i)) {
          var video = document.createElement('video');
          video.src = img;
          video.controls = true;
          video.style.cssText = 'width:240px; height:320px; object-fit:cover; border-radius:10px; background:#000; aspect-ratio:3/4;'; // 放大一倍
          imgWrap.appendChild(video);
        } else {
          var image = document.createElement('img');
          image.src = img;
          image.alt = '配图';
          image.style.cssText = 'width:240px; height:320px; object-fit:cover; border-radius:10px; background:#fafbfc; aspect-ratio:3/4;'; // 放大一倍
          imgWrap.appendChild(image);
        }
      });
      card.appendChild(imgWrap);
    }
    // 技师动态信息区
    if (feed.technician_info) {
      var techInfo = document.createElement('div');
      techInfo.style.cssText = 'margin-top:12px; padding:12px; background:#f8f9fa; border-radius:8px; font-size:13px; color:#666;';
      var actionText = feed.technician_info.action_type === 'move' ? '更换城市' : '新加入';
      var previousCityText = feed.technician_info.previous_city ? ` (从${feed.technician_info.previous_city})` : '';
      techInfo.innerHTML = `
        <div style="display:flex; align-items:center; gap:8px; margin-bottom:6px;">
          <span style="background:#00c6a2; color:white; padding:2px 6px; border-radius:10px; font-size:11px;">${actionText}</span>
          <span style="color:#00c6a2; font-weight:500;">${feed.technician_info.city}${previousCityText}</span>
        </div>
        <div style="color:#999; font-size:12px;">
          年龄:${feed.technician_info.age} | 身高:${feed.technician_info.height}cm | 体重:${feed.technician_info.weight}kg | 服务年限:${feed.technician_info.service_years}年
        </div>
      `;
      card.appendChild(techInfo);
    }

    // 简化的操作区（只显示时间和位置）
    var actions = document.createElement('div');
    actions.style.cssText = 'margin-top:10px; padding-top:8px; border-top:1px solid #f0f0f0; font-size:13px; color:#999; text-align:right;';
    actions.innerHTML = `<span>${feed.time}</span>`;
    card.appendChild(actions);
    // 技师动态不需要点赞评论功能
    // 技师动态不需要菜单功能，移除相关事件处理
    // 给卡片加唯一标识，便于前端隐藏
    card.setAttribute('data-feed-id', feed.id);
    card.setAttribute('data-user-id', feed.user_id);
    feedList.appendChild(card);
    console.log('动态卡片已添加到列表，卡片内容:', card.innerHTML.substring(0, 100) + '...');
  });

  console.log('动态列表渲染完成，总共渲染了', list.length, '条动态');
  console.log('动态列表容器当前内容:', feedList.innerHTML.length, '字符');
}

// 拉取技师动态列表
function fetchFeedList() {
  console.log('开始获取技师动态列表...');
  fetch('technician_feed_list.php')
    .then(async res => {
      console.log('API响应状态:', res.status, res.statusText);
      let data;
      try {
        data = await res.json();
      } catch(e) {
        console.error('JSON解析失败，尝试文本解析:', e);
        try {
          const txt = await res.text();
          console.log('API原始响应:', txt);
          data = JSON.parse(txt);
        } catch(e2) {
          console.error('文本解析也失败:', e2);
          data = { feeds: [] };
        }
      }
      console.log('技师动态API响应:', data);
      console.log('动态数量:', data.feeds ? data.feeds.length : 0);
      renderFeedList(data.feeds || []);
    })
    .catch((error) => {
      console.error('加载技师动态失败:', error);
      renderFeedList([]);
    });
}
// 简化的轮播图初始化
function initSimpleCarousel() {
    console.log('开始简化轮播图初始化');

    fetch('banner_list.php')
        .then(response => response.json())
        .then(data => {
            console.log('轮播图API响应:', data);

            if (data.success && data.data && data.data.length > 0) {
                const carousel = document.getElementById('carousel');
                if (carousel) {
                    const html = `
                        <div class="carousel-wrapper" style="display: flex; width: 100%; height: 100%;">
                            ${data.data.map((slide, index) => `
                                <div class="carousel-slide" style="min-width: 100%; height: 100%; flex-shrink: 0;">
                                    <img src="${slide.img}" alt="轮播图${index + 1}"
                                         style="width: 100%; height: 100%; object-fit: cover; display: block;"
                                         onerror="this.src='images/lbt.png'"
                                         onload="console.log('轮播图${index + 1}加载成功')" />
                                </div>
                            `).join('')}
                        </div>
                        ${data.data.length > 1 ? `
                            <div class="carousel-indicators" style="position: absolute; bottom: 12px; left: 50%; transform: translateX(-50%); display: flex; gap: 8px; z-index: 10;">
                                ${data.data.map((_, index) => `
                                    <div class="carousel-indicator ${index === 0 ? 'active' : ''}"
                                         style="width: 8px; height: 8px; border-radius: 50%; background: rgba(255, 255, 255, ${index === 0 ? '0.9' : '0.5'}); cursor: pointer; transition: all 0.3s ease;"
                                         onclick="goToSlide(${index})"></div>
                                `).join('')}
                            </div>
                        ` : ''}
                    `;
                    carousel.innerHTML = html;
                    console.log('轮播图渲染完成，数量:', data.data.length);

                    // 添加轮播图切换功能
                    if (data.data.length > 1) {
                        initCarouselControls(data.data.length);
                    }
                } else {
                    console.error('找不到轮播图容器');
                }
            } else {
                console.log('没有轮播图数据');
                const carousel = document.getElementById('carousel');
                if (carousel) {
                    carousel.innerHTML = `
                        <div style="display: flex; align-items: center; justify-content: center; height: 180px; background: #f5f5f5; color: #999;">
                            暂无轮播图
                        </div>
                    `;
                }
            }
        })
        .catch(error => {
            console.error('轮播图加载失败:', error);
            const carousel = document.getElementById('carousel');
            if (carousel) {
                carousel.innerHTML = `
                    <div style="display: flex; align-items: center; justify-content: center; height: 180px; background: #f5f5f5; color: #999;">
                        轮播图加载失败
                    </div>
                `;
            }
        });
}

// 轮播图控制功能
let currentSlideIndex = 0;
let totalSlides = 0;
let autoPlayInterval = null;

function initCarouselControls(slideCount) {
    totalSlides = slideCount;
    currentSlideIndex = 0;

    // 启动自动播放
    startAutoPlay();

    console.log('轮播图控制初始化完成，总数:', totalSlides);
}

function goToSlide(index) {
    if (index < 0 || index >= totalSlides) return;

    currentSlideIndex = index;
    updateSlidePosition();
    updateIndicators();

    // 重启自动播放
    startAutoPlay();
}

function nextSlide() {
    const nextIndex = (currentSlideIndex + 1) % totalSlides;
    goToSlide(nextIndex);
}

function prevSlide() {
    const prevIndex = (currentSlideIndex - 1 + totalSlides) % totalSlides;
    goToSlide(prevIndex);
}

function updateSlidePosition() {
    const wrapper = document.querySelector('.carousel-wrapper');
    if (wrapper) {
        const translateX = -currentSlideIndex * 100;
        wrapper.style.transform = `translateX(${translateX}%)`;
    }
}

function updateIndicators() {
    const indicators = document.querySelectorAll('.carousel-indicator');
    indicators.forEach((indicator, index) => {
        if (index === currentSlideIndex) {
            indicator.style.background = 'rgba(255, 255, 255, 0.9)';
            indicator.style.transform = 'scale(1.2)';
            indicator.classList.add('active');
        } else {
            indicator.style.background = 'rgba(255, 255, 255, 0.5)';
            indicator.style.transform = 'scale(1)';
            indicator.classList.remove('active');
        }
    });
}

function startAutoPlay() {
    if (totalSlides <= 1) return;

    stopAutoPlay();
    autoPlayInterval = setInterval(() => {
        nextSlide();
    }, 4000); // 4秒切换一次
}

function stopAutoPlay() {
    if (autoPlayInterval) {
        clearInterval(autoPlayInterval);
        autoPlayInterval = null;
    }
}

// 页面加载完成后初始化
window.addEventListener('DOMContentLoaded', function() {
    console.log('页面DOM加载完成');

    // 初始化轮播图
    setTimeout(() => {
        initSimpleCarousel();
    }, 500);

    // 加载动态列表
    fetchFeedList();
});
    </script>
</body>
</html>
