<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技师登入页面</title>
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,500,700&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: 'Roboto', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: #222;
            min-height: 100vh;
            position: relative;
        }
        /* 以下为底部导航栏样式，完全同步主页 */
        .bottom-nav {
            position: fixed;
            left: 16px;
            right: 16px;
            bottom: 16px;
            background: #fff;
            border-top: none;
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0,198,162,0.18), 0 2px 8px rgba(0,0,0,0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
            z-index: 120;
            transition: box-shadow 0.3s, bottom 0.3s;
        }
        .nav-item {
            text-align: center;
            font-size: 12px;
            color: #888;
            flex: 1;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .nav-item.active {
            color: #00c6a2;
        }
        .nav-icon {
            display: block;
            font-size: 24px;
            margin-bottom: 2px;
        }
        .nav-item.center {
            z-index: 101;
            flex: none;
            width: auto;
            margin: 0 32px;
        }
        .nav-plus {
            position: absolute;
            top: -34px;
            left: 50%;
            transform: translateX(-50%);
            width: 48px;
            height: 48px;
            background: #00c6a2;
            border-radius: 50%;
            box-shadow: 0 12px 32px rgba(0,198,162,0.25), 0 2px 8px rgba(0,0,0,0.10);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 28px;
            border: 4px solid #fff;
            z-index: 130;
            transition: box-shadow 0.3s, top 0.3s;
            animation: navPlusFloat 1.2s infinite ease-in-out alternate;
            cursor: pointer;
        }
        @keyframes navPlusFloat {
            0% { box-shadow: 0 8px 24px rgba(0,198,162,0.18), 0 2px 8px rgba(0,0,0,0.10); top: -24px; }
            100% { box-shadow: 0 16px 32px rgba(0,198,162,0.25), 0 4px 16px rgba(0,0,0,0.12); top: -32px; }
        }
        .nav-plus:hover {
            box-shadow: 0 16px 48px 0 rgba(0,198,162,0.35), 0 4px 16px rgba(0,0,0,0.15);
            transform: translateX(-50%) scale(1.08);
        }
        /* 登录页原有样式保留 */
        .login-bg {
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            width: 100vw; height: 100vh;
            object-fit: cover;
            z-index: 1;
        }
        .login-mask {
            position: absolute;
            left: 0; right: 0; bottom: 0; top: 0;
            background: linear-gradient(to bottom, rgba(0,0,0,0) 40%, #181a1f 100%);
            z-index: 2;
        }
        .login-main {
            position: relative;
            z-index: 3;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .login-logo {
            margin-top: 60px;
            background: #fff;
            border-radius: 20px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.12);
            padding: 18px 24px 10px 24px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .login-logo img {
            width: 72px;
            height: 72px;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.10);
        }
        .login-logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-top: 8px;
            text-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .login-btn {
            margin-top: 48px;
            width: 80vw;
            max-width: 340px;
            height: 52px;
            background: #ffe000;
            color: #222;
            font-size: 18px;
            font-weight: bold;
            border: none;
            border-radius: 26px;
            box-shadow: 0 2px 8px rgba(255,224,0,0.18);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s;
        }
        .login-btn:hover {
            background: #ffd600;
        }
        .login-btn-icon {
            margin-right: 8px;
            display: flex;
            align-items: center;
        }
        .login-socials {
            margin-top: 32px;
            display: flex;
            justify-content: center;
            gap: 32px;
        }
        .login-social {
            width: 48px;
            height: 48px;
            background: #fff;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.10);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: box-shadow 0.2s;
        }
        .login-social img {
            width: 28px;
            height: 28px;
        }
        .login-agree {
            margin-top: 36px;
            font-size: 13px;
            color: #bbb;
            text-align: center;
        }
        .login-agree a {
            color: #1976d2;
            text-decoration: none;
            margin: 0 2px;
        }
        @media (max-width: 480px) {
            .login-logo img { width: 56px; height: 56px; }
            .login-logo-text { font-size: 20px; }
            .login-btn { font-size: 16px; height: 44px; }
            .login-social { width: 40px; height: 40px; }
            .login-social img { width: 22px; height: 22px; }
        }
    </style>
</head>
<body>
    <img class="login-bg" src="images/login-bg.jpg" alt="背景" />
    <div class="login-mask"></div>
    <div class="login-main">
        <div class="login-logo">
            <span>
              <svg width="72" height="72" viewBox="0 0 72 72" fill="none">
                <rect x="0" y="0" width="72" height="72" rx="16" fill="#e0fff3"/>
                <text x="36" y="44" text-anchor="middle" font-size="32" font-family="PingFang SC, Microsoft YaHei, sans-serif" fill="#00c6a2" font-weight="bold">云顶</text>
              </svg>
            </span>
            <div class="login-logo-text"></div>
        </div>
        <form id="login-form" style="width:80vw;max-width:340px;margin-top:32px;display:flex;flex-direction:column;gap:18px;">
          <input id="username" type="text" placeholder="请输入你的手机号" maxlength="32" style="height:44px;border-radius:12px;border:1px solid #eee;padding:0 14px;font-size:16px;outline:none;" required />
          <input id="password" type="password" placeholder="请输入密码，默认123456" maxlength="32" style="height:44px;border-radius:12px;border:1px solid #eee;padding:0 14px;font-size:16px;outline:none;" required />
          <button type="submit" class="login-btn" id="login-btn" style="margin-top:0;">登录</button>
          <button type="button" class="login-btn" id="register-btn" style="margin-top:12px;background:#fff;color:#00c6a2;border:1px solid #00c6a2;">注册</button>
        <!-- ...existing code... -->
        </form>
        <!-- 注册弹窗移到form外部，避免嵌套冲突 -->
        <div id="register-modal" style="display:none;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.32);z-index:999;align-items:center;justify-content:center;">
          <div style="background:#fff;border-radius:18px;box-shadow:0 4px 24px rgba(0,0,0,0.12);padding:32px 24px 24px 24px;max-width:340px;width:80vw;display:flex;flex-direction:column;gap:18px;">
            <div style="font-size:22px;font-weight:bold;color:#222;text-align:center;margin-bottom:8px;">用户注册</div>
            <input id="reg-phone" type="tel" placeholder="请输入真实手机号，方便客服联系" maxlength="11" pattern="^1[3-9]\d{9}$" style="height:44px;border-radius:12px;border:1px solid #eee;padding:0 14px;font-size:16px;outline:none;" required />
            <input id="reg-password" type="password" placeholder="请输入密码" maxlength="32" style="height:44px;border-radius:12px;border:1px solid #eee;padding:0 14px;font-size:16px;outline:none;" required />
            <button type="button" id="reg-submit" class="login-btn" style="margin-top:0;">提交注册</button>
            <button type="button" id="reg-cancel" style="margin-top:8px;background:#fff;color:#888;border:none;font-size:15px;">取消</button>
          </div>
        </div>
        </form>
        <!-- 第三方登录入口已移除 -->
        <div class="login-agree">
            <input type="checkbox" id="agree" checked style="vertical-align:middle;margin-right:4px;" />
            我已阅读并同意 <a href="#">《用户协议》</a> 和 <a href="#">《隐私政策》</a>
        </div>
    </div>

  <div class="bottom-nav">
    <div class="nav-item" id="nav-home">
      <span class="nav-icon">
        <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><path d="M3 10.5L12 4l9 6.5V20a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V10.5z" stroke="#00c6a2" stroke-width="2"/></svg>
      </span>
      首页
    </div>
    <div class="nav-item">
      <span class="nav-icon">
        <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><rect x="4" y="4" width="16" height="16" rx="4" stroke="#888" stroke-width="2"/><path d="M8 8h8v8H8V8z" stroke="#888" stroke-width="2"/></svg>
      </span>
      技师
    </div>
    <div class="nav-item center">
      <span class="nav-plus">
        <svg width="38" height="38" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="12" r="10" fill="#00c6a2"/><path d="M12 8v8M8 12h8" stroke="#fff" stroke-width="2"/></svg>
      </span>
    </div>
    <div class="nav-item active" id="nav-feed">
      <span class="nav-icon">
        <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g stroke="#2196f3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M14 14L22 7.5C20.5 5.5 17.5 5.5 16 7.5C14.5 9.5 14 14 14 14Z"/>
            <path d="M14 14L6 7.5C7.5 5.5 10.5 5.5 12 7.5C13.5 9.5 14 14 14 14Z"/>
            <path d="M14 14L6 20.5C7.5 22.5 10.5 22.5 12 20.5C13.5 18.5 14 14 14 14Z"/>
            <path d="M14 14L22 20.5C20.5 22.5 17.5 22.5 16 20.5C14.5 18.5 14 14 14 14Z"/>
            <circle cx="14" cy="14" r="2.5" fill="#2196f3" stroke="none"/>
          </g>
        </svg>
      </span>
      动态
    </div>
    <div class="nav-item" id="nav-mine">
      <span class="nav-icon">
        <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="8" r="4" stroke="#888" stroke-width="2"/><path d="M4 20c0-2.2 3.6-4 8-4s8 1.8 8 4" stroke="#888" stroke-width="2"/></svg>
      </span>
      我的
    </div>
  </div>

  <script>
    // 首页按钮跳转主页
    document.getElementById('nav-home').addEventListener('click', function() {
      window.location.href = 'index.html';
    });
    // 动态按钮跳转动态页
    document.getElementById('nav-feed').addEventListener('click', function() {
      window.location.href = 'feed.html';
    });
    // 我的按钮判断登录
    document.getElementById('nav-mine').addEventListener('click', function() {
      if (localStorage.getItem('isLogin') === '1') {
        window.location.href = 'profile.html';
      } else {
        window.location.href = 'login.html';
      }
    });

    // 登录表单逻辑（账号密码）
    document.getElementById('login-form').addEventListener('submit', function(e) {
      e.preventDefault();
      console.log('登录表单提交事件触发');
      const username = document.getElementById('username').value.trim();
      const password = document.getElementById('password').value.trim();
      const agree = document.getElementById('agree').checked;
      if (!username) {
        alert('请输入账号');
        return;
      }
      if (!password || password.length < 4) {
        alert('请输入不少于4位的密码');
        return;
      }
      if (!agree) {
        alert('请勾选同意协议');
        return;
      }
      // 退出登录时自动清除登录状态
      localStorage.removeItem('isLogin');
      // 记住账号密码
      localStorage.setItem('login_username', username);
      localStorage.setItem('login_password', password);
      // 调用后端登录接口
      console.log('点击登录', username, password);
      fetch('login.php', {
        method: 'POST',
        body: new URLSearchParams({ phone: username, password }),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      })
      .then(res => {
        console.log('响应状态:', res.status);
        console.log('响应头:', res.headers.get('content-type'));

        if (!res.ok) {
          throw new Error(`HTTP ${res.status}: ${res.statusText}`);
        }

        // 检查内容类型是否为JSON
        const contentType = res.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          // 如果不是JSON，先读取文本内容用于调试
          return res.text().then(text => {
            console.error('服务器返回非JSON内容:', text.substring(0, 200));
            throw new Error('服务器返回的不是JSON格式');
          });
        }

        return res.json();
      })
      .then(data => {
        console.log('后端返回:', data); // 调试输出
        if (data.success) {
          localStorage.setItem('isLogin', '1');
          window.location.href = 'profile.html';
        } else {
          alert(data.msg || '登录失败');
        }
      })
      .catch(err => {
        console.error('登录请求失败', err);
        if (err.message.includes('JSON')) {
          alert('服务器响应格式错误，请联系管理员');
        } else if (err.message.includes('HTTP')) {
          alert('服务器错误：' + err.message);
        } else {
          alert('登录请求失败：' + err.message);
        }
      });
    });

    // 注册按钮逻辑（仅跳转注册页或弹窗，后续可扩展）
    // 注册按钮弹窗逻辑
    document.getElementById('register-btn').addEventListener('click', function() {
      document.getElementById('register-modal').style.display = 'flex';
    });

    // 注册弹窗取消
    document.getElementById('reg-cancel').addEventListener('click', function() {
      document.getElementById('register-modal').style.display = 'none';
    });

    // 注册提交逻辑
    document.getElementById('reg-submit').addEventListener('click', function() {
      const phone = document.getElementById('reg-phone').value.trim();
      const password = document.getElementById('reg-password').value.trim();
      if (!/^1[3-9]\d{9}$/.test(phone)) {
        alert('请输入正确的手机号');
        return;
      }
      if (!password || password.length < 6) {
        alert('请输入不少于6位的密码');
        return;
      }
      // 调用后端注册接口
      fetch('register.php', {
        method: 'POST',
        body: new URLSearchParams({ phone, password }),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      })
      .then(res => {
        console.log('注册响应状态:', res.status);
        console.log('注册响应头:', res.headers.get('content-type'));

        if (!res.ok) {
          throw new Error(`HTTP ${res.status}: ${res.statusText}`);
        }

        // 检查内容类型是否为JSON
        const contentType = res.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          return res.text().then(text => {
            console.error('注册服务器返回非JSON内容:', text.substring(0, 200));
            throw new Error('服务器返回的不是JSON格式');
          });
        }

        return res.json();
      })
      .then(data => {
        alert(data.msg);
        if (data.success) {
          document.getElementById('register-modal').style.display = 'none';
        }
      })
      .catch(err => {
        console.error('注册请求失败', err);
        if (err.message.includes('JSON')) {
          alert('服务器响应格式错误，请联系管理员');
        } else if (err.message.includes('HTTP')) {
          alert('服务器错误：' + err.message);
        } else {
          alert('网络错误：' + err.message);
        }
      });
    });

    // 登录表单自动填充记住的账号密码
    window.addEventListener('DOMContentLoaded', function() {
      var savedUser = localStorage.getItem('login_username') || '';
      var savedPwd = localStorage.getItem('login_password') || '';
      if (savedUser) document.getElementById('username').value = savedUser;
      if (savedPwd) document.getElementById('password').value = savedPwd;
    });
  </script>
</body>
</html>
