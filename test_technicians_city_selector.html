<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技师页面城市选择器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        .city-selector-preview {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            text-align: center;
        }
        .current-city-display {
            display: inline-block;
            padding: 12px 20px;
            background: linear-gradient(135deg, #00c6a2 0%, #00a085 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .current-city-display:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 198, 162, 0.3);
        }
        .current-city-text {
            display: inline-block;
            margin-left: 10px;
            padding: 8px 16px;
            background: #f0f8ff;
            border: 1px solid #00c6a2;
            border-radius: 20px;
            color: #00c6a2;
            font-weight: bold;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .feature-list {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin-bottom: 8px;
            color: #666;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .comparison-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
        .comparison-item h4 {
            margin-top: 0;
            color: #007bff;
        }
        .city-list-preview {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 10px;
            background: #fafafa;
            font-size: 12px;
        }
        .city-group-preview {
            margin-bottom: 15px;
        }
        .city-group-title {
            background: #f8f9fa;
            padding: 4px 8px;
            font-weight: bold;
            color: #666;
            border-radius: 3px;
            margin-bottom: 5px;
        }
        .city-item-preview {
            padding: 4px 8px;
            margin: 2px 0;
            background: white;
            border-radius: 3px;
            border: 1px solid #eee;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        .error {
            text-align: center;
            padding: 20px;
            color: #dc3545;
            background: #f8d7da;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏙️ 技师页面城市选择器测试</h1>
        
        <div class="demo-section">
            <h3>🎯 功能说明</h3>
            <p style="color: #666; line-height: 1.6;">
                技师页面的城市选择器现在支持按拼音首字母A-Z排序显示，提供更好的用户体验。
            </p>
            
            <div class="feature-list">
                <h4>✨ 新增功能</h4>
                <ul>
                    <li><strong>拼音分组显示</strong> - 城市按拼音首字母分组，A-Z有序排列</li>
                    <li><strong>热门城市优先</strong> - 热门城市仍然优先显示在顶部</li>
                    <li><strong>分组标题</strong> - 每个字母分组都有清晰的标题和数量</li>
                    <li><strong>字母导航</strong> - 右侧A-Z索引支持快速定位到分组</li>
                    <li><strong>搜索兼容</strong> - 搜索功能与拼音分组完美兼容</li>
                    <li><strong>全部城市特殊处理</strong> - "全部城市"选项始终显示在最前面</li>
                </ul>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🎮 功能演示</h3>
            <div class="city-selector-preview">
                <p style="margin-bottom: 15px; color: #666;">点击下方按钮体验新的城市选择器</p>
                <button class="current-city-display" onclick="openTechniciansPage()">
                    📍 选择城市
                </button>
                <span class="current-city-text" id="demo-current-city">全部城市</span>
                
                <div style="margin-top: 20px;">
                    <button class="btn" onclick="loadCityPreview()">🔄 加载城市预览</button>
                    <button class="btn success" onclick="openTechniciansPage()">🔗 打开技师页面</button>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>📊 排序对比</h3>
            <div class="comparison">
                <div class="comparison-item">
                    <h4>🆕 新排序 (拼音A-Z分组)</h4>
                    <div id="new-sort-preview" class="city-list-preview">
                        <div class="loading">点击"加载城市预览"查看</div>
                    </div>
                </div>
                <div class="comparison-item">
                    <h4>📝 旧排序 (简单字母排序)</h4>
                    <div id="old-sort-preview" class="city-list-preview">
                        <div class="loading">点击"加载城市预览"查看</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🔤 字母分布</h3>
            <div id="alphabet-distribution" style="font-family: monospace; font-size: 12px; background: white; padding: 15px; border-radius: 6px;">
                <div class="loading">点击"加载城市预览"查看字母分布</div>
            </div>
        </div>
    </div>

    <script>
        let cities = [];
        
        // 页面加载时的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('技师页面城市选择器测试页面加载完成');
        });
        
        // 加载城市预览
        async function loadCityPreview() {
            try {
                const response = await fetch('admin/city_list.php');
                const data = await response.json();
                
                if (Array.isArray(data)) {
                    cities = data.map(city => city.name).filter(name => name && name.trim());
                    
                    // 去重但保持排序
                    const uniqueCities = [];
                    const seen = new Set();
                    cities.forEach(city => {
                        if (!seen.has(city)) {
                            seen.add(city);
                            uniqueCities.push(city);
                        }
                    });
                    
                    cities = uniqueCities;
                    cities.unshift('全部城市'); // 添加全部城市选项
                    
                    showNewSortPreview();
                    showOldSortPreview();
                    showAlphabetDistribution();
                } else {
                    throw new Error('数据格式错误');
                }
            } catch (error) {
                console.error('加载城市失败:', error);
                document.getElementById('new-sort-preview').innerHTML = '<div class="error">加载失败</div>';
                document.getElementById('old-sort-preview').innerHTML = '<div class="error">加载失败</div>';
            }
        }
        
        // 显示新排序预览（拼音分组）
        function showNewSortPreview() {
            const container = document.getElementById('new-sort-preview');
            const html = renderCitiesByPinyin(cities);
            container.innerHTML = html;
        }
        
        // 显示旧排序预览（简单排序）
        function showOldSortPreview() {
            const container = document.getElementById('old-sort-preview');
            const sortedCities = [...cities].sort();
            
            const html = sortedCities.map(city => `
                <div class="city-item-preview">${city}</div>
            `).join('');
            
            container.innerHTML = html;
        }
        
        // 按拼音分组渲染城市（复制technicians.html的逻辑）
        function renderCitiesByPinyin(cities) {
            const regularCities = cities.filter(city => city !== '全部城市');
            
            // 按拼音首字母分组
            const groupedCities = {};
            regularCities.forEach(city => {
                const letter = getCityFirstLetter(city);
                if (!groupedCities[letter]) {
                    groupedCities[letter] = [];
                }
                groupedCities[letter].push(city);
            });
            
            const letters = Object.keys(groupedCities).sort();
            let html = '';
            
            // 全部城市选项
            if (cities.includes('全部城市')) {
                html += `
                    <div class="city-group-preview">
                        <div class="city-group-title">🌐 特殊选项</div>
                        <div class="city-item-preview" style="background: #f0f8ff; border-color: #00c6a2;">全部城市</div>
                    </div>
                `;
            }
            
            // 按字母分组
            letters.forEach(letter => {
                const citiesInGroup = groupedCities[letter];
                html += `
                    <div class="city-group-preview">
                        <div class="city-group-title">${letter} (${citiesInGroup.length}个)</div>
                        ${citiesInGroup.map(city => `
                            <div class="city-item-preview">${city}</div>
                        `).join('')}
                    </div>
                `;
            });
            
            return html;
        }
        
        // 获取城市首字母（复制technicians.html的逻辑）
        function getCityFirstLetter(cityName) {
            const pinyinMap = {
                '阿': 'A', '安': 'A', '鞍': 'A',
                '北': 'B', '包': 'B', '保': 'B', '蚌': 'B', '本': 'B', '滨': 'B', '亳': 'B',
                '重': 'C', '成': 'C', '长': 'C', '常': 'C', '承': 'C', '沧': 'C', '滁': 'C', '池': 'C', '潮': 'C', '崇': 'C', '赤': 'C', '朝': 'C',
                '大': 'D', '东': 'D', '德': 'D', '丹': 'D', '定': 'D', '都': 'D', '儋': 'D', '达': 'D',
                '鄂': 'E', '恩': 'E',
                '福': 'F', '佛': 'F', '抚': 'F', '阜': 'F', '防': 'F', '丰': 'F',
                '广': 'G', '贵': 'G', '桂': 'G', '赣': 'G', '格': 'G', '固': 'G',
                '杭': 'H', '哈': 'H', '海': 'H', '合': 'H', '河': 'H', '衡': 'H', '呼': 'H', '黄': 'H', '惠': 'H', '湖': 'H', '怀': 'H', '邯': 'H', '汉': 'H', '菏': 'H', '鹤': 'H', '黑': 'H', '红': 'H', '淮': 'H', '华': 'H',
                '济': 'J', '金': 'J', '江': 'J', '嘉': 'J', '九': 'J', '吉': 'J', '锦': 'J', '焦': 'J', '荆': 'J', '景': 'J', '揭': 'J', '晋': 'J',
                '昆': 'K', '开': 'K', '克': 'K', '喀': 'K',
                '兰': 'L', '拉': 'L', '洛': 'L', '连': 'L', '柳': 'L', '六': 'L', '临': 'L', '廊': 'L', '聊': 'L', '辽': 'L', '丽': 'L', '娄': 'L', '漯': 'L', '吕': 'L', '龙': 'L', '陇': 'L', '林': 'L',
                '马': 'M', '梅': 'M', '绵': 'M', '牡': 'M', '茂': 'M', '眉': 'M',
                '南': 'N', '宁': 'N', '内': 'N', '那': 'N',
                '盘': 'P', '平': 'P', '莆': 'P', '濮': 'P', '攀': 'P', '普': 'P',
                '青': 'Q', '秦': 'Q', '齐': 'Q', '泉': 'Q', '衢': 'Q', '庆': 'Q', '钦': 'Q', '曲': 'Q', '清': 'Q',
                '日': 'R', '瑞': 'R',
                '上': 'S', '深': 'S', '苏': 'S', '沈': 'S', '石': 'S', '三': 'S', '绍': 'S', '汕': 'S', '韶': 'S', '商': 'S', '十': 'S', '随': 'S', '宿': 'S', '朔': 'S', '双': 'S', '松': 'S', '遂': 'S', '邵': 'S', '神': 'S', '山': 'S',
                '天': 'T', '太': 'T', '唐': 'T', '台': 'T', '泰': 'T', '通': 'T', '铜': 'T', '铁': 'T', '塔': 'T', '吐': 'T', '图': 'T',
                '乌': 'U', '无': 'W', '武': 'W', '温': 'W', '威': 'W', '潍': 'W', '芜': 'W', '梧': 'W', '万': 'W', '文': 'W', '渭': 'W', '吴': 'W', '五': 'W',
                '西': 'X', '厦': 'X', '徐': 'X', '新': 'X', '襄': 'X', '许': 'X', '宣': 'X', '咸': 'X', '湘': 'X', '孝': 'X', '信': 'X', '忻': 'X', '邢': 'X', '兴': 'X', '锡': 'X',
                '银': 'Y', '宜': 'Y', '扬': 'Y', '烟': 'Y', '盐': 'Y', '营': 'Y', '岳': 'Y', '运': 'Y', '玉': 'Y', '榆': 'Y', '永': 'Y', '益': 'Y', '阳': 'Y', '鹰': 'Y', '伊': 'Y', '延': 'Y', '雅': 'Y', '义': 'Y',
                '郑': 'Z', '珠': 'Z', '中': 'Z', '株': 'Z', '淄': 'Z', '枣': 'Z', '张': 'Z', '湛': 'Z', '肇': 'Z', '镇': 'Z', '周': 'Z', '驻': 'Z', '舟': 'Z', '漳': 'Z', '遵': 'Z', '资': 'Z', '自': 'Z', '昭': 'Z'
            };
            
            const firstChar = cityName.charAt(0);
            return pinyinMap[firstChar] || firstChar.toUpperCase();
        }
        
        // 显示字母分布
        function showAlphabetDistribution() {
            const container = document.getElementById('alphabet-distribution');
            const regularCities = cities.filter(city => city !== '全部城市');
            
            // 统计每个字母的城市数量
            const letterCounts = {};
            regularCities.forEach(city => {
                const letter = getCityFirstLetter(city);
                letterCounts[letter] = (letterCounts[letter] || 0) + 1;
            });
            
            const letters = Object.keys(letterCounts).sort();
            let html = `字母分布统计 (共 ${regularCities.length} 个城市):\n\n`;
            
            letters.forEach(letter => {
                const count = letterCounts[letter];
                const percentage = ((count / regularCities.length) * 100).toFixed(1);
                html += `${letter}: ${count}个 (${percentage}%)\n`;
            });
            
            container.textContent = html;
        }
        
        // 打开技师页面
        function openTechniciansPage() {
            window.open('technicians.html', '_blank');
        }
    </script>
</body>
</html>
