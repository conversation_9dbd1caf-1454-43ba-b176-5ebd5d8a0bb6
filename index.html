<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云顶会所</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet" onerror="this.remove()">
    <style>
        body {
            margin: 0;
            font-family: 'Roboto', 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            padding: 12px 16px 8px 16px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(255, 154, 158, 0.3);
        }
        .header-title {
            font-size: 22px;
            font-weight: bold;
            color: #fff;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .category-cards {
            display: flex;
            gap: 12px;
            padding: 16px 16px 0 16px;
        }
        .category-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(168, 237, 234, 0.3);
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 90px;
            padding: 10px 12px 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        .card-title {
            font-size: 16px;
            font-weight: bold;
            color: #2d3748;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        }
        .card-img {
            width: 32px;
            height: 32px;
            object-fit: contain;
        }
        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-top: 8px;
        }
        .card-desc {
            font-size: 13px;
            color: #888;
        }
        .card-arrow {
            font-size: 20px;
            color: #bbb;
        }
        .special-section {
            display: flex;
            gap: 12px;
            padding: 12px 16px 0 16px;
        }
        .special-box {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 12px;
            flex: 1;
            display: flex;
            align-items: center;
            padding: 10px 12px;
            box-shadow: 0 8px 25px rgba(252, 182, 159, 0.3);
            min-width: 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .special-img-wrap {
            flex-shrink: 0;
            width: 48px;
            height: 48px;
            border-radius: 8px;
            overflow: hidden;
            margin-right: 10px;
            background: #f7f8fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .special-img {
            width: 48px;
            height: 48px;
            object-fit: cover;
            border-radius: 8px;
        }
        .special-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .special-title {
            font-size: 15px;
            font-weight: bold;
            color: #222;
            margin-bottom: 2px;
        }
        .special-sub {
            font-size: 13px;
            color: #444;
            margin-bottom: 2px;
        }
        .special-highlight {
            color: #ff9800;
            font-weight: bold;
        }
        .special-tag {
            font-size: 12px;
            color: #bbb;
        }
        .tabs {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px 0 16px;
            font-size: 15px;
            color: #888;
            background: #fff;
        }
        .tab-active {
            color: #fff;
            font-weight: bold;
            background: #00c6a2;
            border-radius: 16px;
            padding: 6px 18px;
            box-shadow: 0 2px 8px rgba(0,198,162,0.08);
        }
        .tabs > div {
            flex: 1;
            text-align: center;
            padding: 6px 0;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
        }
        .product-list {
            padding: 0 16px;
            padding-bottom: 80px; /* 防止内容被底部导航遮挡 */
        }
        .product-item {
            background: #fff;
            border-radius: 12px;
            margin-top: 12px;
            padding: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
            display: flex;
            align-items: center;
        }
        .product-img {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            background: #e0e7ef;
            margin-right: 12px;
        }
        .product-info {
            flex: 1;
        }
        .product-title {
            font-size: 15px;
            font-weight: 500;
            margin-bottom: 4px;
        }
        .product-tags {
            display: inline-block;
            font-size: 12px;
            padding: 2px 10px;
            border-radius: 8px;
            margin-right: 8px;
            margin-bottom: 4px;
            background: #fff;
            border: 1px solid #ff9800;
            color: #ff9800;
            font-weight: 500;
        }
        .product-tags.red {
            border: 1px solid #f44336;
            color: #f44336;
            background: #fff;
        }
        .product-price {
            font-size: 16px;
            color: #00c6a2;
            font-weight: bold;
        }
        .product-meta {
            font-size: 12px;
            color: #888;
        }
        .bottom-nav {
            position: fixed;
            left: 16px;
            right: 16px;
            bottom: 16px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
            backdrop-filter: blur(20px);
            border-top: none;
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2), 0 2px 8px rgba(0,0,0,0.08);
            display: flex;
            justify-content: space-around;
            align-items: center;
            height: 64px;
            z-index: 120;
            transition: box-shadow 0.3s, bottom 0.3s;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .nav-item {
            text-align: center;
            font-size: 12px;
            color: #888;
            flex: 1;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: color 0.3s;
        }
        .nav-item.active {
            color: #00c6a2;
        }
        .nav-icon {
            display: block;
            font-size: 24px;
            margin-bottom: 2px;
        }
        }
        @keyframes navPlusFloat {
            0% { box-shadow: 0 8px 24px rgba(0,198,162,0.18), 0 2px 8px rgba(0,0,0,0.10); top: -24px; }
            100% { box-shadow: 0 16px 32px rgba(0,198,162,0.25), 0 4px 16px rgba(0,0,0,0.12); top: -32px; }
        }
        .nav-plus:hover {
            box-shadow: 0 16px 48px 0 rgba(0,198,162,0.35), 0 4px 16px rgba(0,0,0,0.15);
            transform: translateX(-50%) scale(1.08);
        }
        .top-nav {
            display: flex;
            align-items: center;
            background: #e0fff3;
            padding: 0 16px;
            height: 38px;
            border-bottom: 1px solid #f0f0f0;
        }
        .top-nav-item {
            font-size: 16px;
            color: #bbb;
            margin-right: 32px;
            position: relative;
            font-weight: 500;
            cursor: pointer;
        }
        .top-nav-item.active {
            color: #222;
            font-weight: bold;
        }
        .top-nav-item.active::after {
            content: '';
            display: block;
            position: absolute;
            left: 0;
            bottom: -8px;
            width: 100%;
            height: 3px;
            background: #222;
            border-radius: 2px;
        }

        /* 推荐技师样式 */
        .recommend-section {
            margin: 16px;
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 8px 30px rgba(210, 153, 194, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .recommend-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .recommend-techs {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 8px;
        }

        .recommend-techs::-webkit-scrollbar {
            height: 4px;
        }

        .recommend-techs::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }

        .recommend-techs::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }

        .recommend-tech-item {
            flex: 0 0 auto;
            width: 80px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s ease;
            position: relative;
        }

        .recommend-tech-item:hover {
            transform: translateY(-2px);
        }

        .recommend-tech-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #ff6b9d;
            margin-bottom: 8px;
            box-shadow: 0 4px 12px rgba(255, 107, 157, 0.4);
        }

        .recommend-tech-name {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .recommend-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(255, 107, 157, 0.4);
        }

        /* 首页公告样式 */
        .home-announcement {
            margin: 16px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px 16px;
            overflow: hidden;
        }

        .announcement-content {
            color: #856404;
            font-size: 14px;
            line-height: 1.5;
        }

        .announcement-content.scrolling {
            animation: scroll-text 15s linear infinite;
            white-space: nowrap;
        }

        @keyframes scroll-text {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-title">云顶会所Spa</div>
    </div>
    <div class="category-cards">
        <!-- 已删除：泰式按摩、海口专区、咨询客服 -->
    </div>
    <div class="special-section">
        <div class="special-box" id="project-article-link" style="cursor:pointer;">
            <div class="special-img-wrap">
                <svg class="special-img" width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="34" cy="18" r="6" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                  <path d="M12 32 Q18 28 28 32 Q36 34 38 40" stroke="#2196f3" stroke-width="2.2" fill="none" stroke-linecap="round"/>
                  <path d="M14 38 Q20 36 28 38" stroke="#2196f3" stroke-width="2" fill="none" stroke-linecap="round"/>
                  <path d="M10 40 Q18 42 24 40 Q30 38 38 40" stroke="#2196f3" stroke-width="2" fill="none" stroke-linecap="round"/>
                  <ellipse cx="24" cy="36" rx="12" ry="5" fill="#e3f2fd" stroke="#2196f3" stroke-width="1.5" />
                  <path d="M30 24 Q32 28 34 24" stroke="#2196f3" stroke-width="2" fill="none" stroke-linecap="round"/>
                </svg>
            </div>
            <div class="special-info">
                <div class="special-title">项目介绍</div>
                <div class="special-sub">项目价格表介绍</div>
                <div class="special-tag">查看</div>
            </div>
        </div>
        <div class="special-box" id="taxi-article-link">
            <div class="special-img-wrap">
                <img src="images/taxi.jpg" class="special-img" alt="3D建模师" />
            </div>
            <div class="special-info">
                <div class="special-title">路费说明</div>
                <div class="special-sub"><span class="special-highlight">路费说明</span></div>
                <div class="special-tag">立即查看</div>
            </div>
        </div>
    </div>


    <!-- 推荐技师区域 -->
    <div class="recommend-section" id="recommend-section" style="display: none;">
        <div class="recommend-header">
            <h3 style="margin: 0; color: #333; font-size: 18px; font-weight: 600;">⭐ 推荐技师</h3>
            <span style="color: #666; font-size: 14px;">为您精选优质技师</span>
        </div>
        <div class="recommend-techs" id="recommend-techs">
            <!-- 推荐技师将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 首页公告区域 -->
    <div class="home-announcement" id="home-announcement" style="display: none;">
        <div class="announcement-content" id="home-announcement-content">
            <!-- 公告内容将通过JavaScript动态加载 -->
        </div>
    </div>

    <div class="tabs">
        <div class="tab-active" data-type="spa" onclick="switchServiceType('spa', this)">会所项目</div>
        <div data-type="studio" onclick="switchServiceType('studio', this)">工作室项目</div>
        <div data-type="ts" onclick="switchServiceType('ts', this)">TS项目</div>
        <div data-type="cd" onclick="switchServiceType('cd', this)">CD项目</div>
    </div>
    <div class="product-list" id="product-list">
        <!-- 服务项目将通过JavaScript动态加载 -->
        <div style="text-align: center; padding: 40px 20px; color: #999;">
            <div style="font-size: 16px;">⏳ 正在加载服务项目...</div>
        </div>
    </div>
       <div class="bottom-nav">
    <div class="nav-item active" id="nav-home">
        <span class="nav-icon">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><path d="M3 10.5L12 4l9 6.5V20a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V10.5z" stroke="#667eea" stroke-width="2"/></svg>
        </span>
        首页
    </div>
    <div class="nav-item" id="nav-technicians" onclick="window.location.href='technicians.html'">
        <span class="nav-icon">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><rect x="4" y="4" width="16" height="16" rx="4" stroke="#888" stroke-width="2"/><path d="M8 8h8v8H8V8z" stroke="#888" stroke-width="2"/></svg>
        </span>
        技师
    </div>
    <div class="nav-item" id="nav-mine" onclick="window.location.href='profile.html'">
        <span class="nav-icon">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="8" r="4" stroke="#888" stroke-width="2"/><path d="M4 20c0-2.2 3.6-4 8-4s8 1.8 8 4" stroke="#888" stroke-width="2"/></svg>
        </span>
        我的
    </div>
</div>
<script>
// 首页按钮跳转主页
document.getElementById('nav-home').addEventListener('click', function() {
    window.location.href = 'index.html';
});
// 我的按钮判断登录
document.getElementById('nav-mine').addEventListener('click', function() {
    if (localStorage.getItem('isLogin') === '1') {
        window.location.href = 'profile.html';
    } else {
        window.location.href = 'login.html';
    }
});

// 自动高亮底部导航
var navMap = {
  'index.html': 'nav-home',
  'technicians.html': 'nav-technicians',
  'profile.html': 'nav-mine',
  'login.html': 'nav-mine'
};
var current = window.location.pathname.split('/').pop();
var navId = navMap[current];
if (navId) {
  var navEl = document.getElementById(navId);
  if (navEl) navEl.classList.add('active');
}

// 路费说明点击跳转
var taxiBox = document.getElementById('taxi-article-link');
if(taxiBox){
  taxiBox.style.cursor = 'pointer';
  taxiBox.onclick = function(){
    window.location.href = 'taxi_article.html';
  };
}

// 项目介绍点击跳转
var projectBox = document.getElementById('project-article-link');
if(projectBox){
  projectBox.onclick = function(){
    window.location.href = 'project_article.html';
  };
}

// 全局变量存储所有服务数据
let allServicesData = [];
let currentServiceType = 'spa'; // 默认显示会所项目

// 加载所有服务项目
function loadServices() {
  const productList = document.getElementById('product-list');

  fetch('service_list.php')
    .then(response => response.json())
    .then(data => {
      console.log('服务项目API响应:', data);

      if (data.success && Array.isArray(data.data)) {
        allServicesData = data.data;
        renderServicesByType(currentServiceType);
      } else {
        showServiceError('获取服务项目失败');
      }
    })
    .catch(error => {
      console.error('加载服务项目失败:', error);
      showServiceError('网络错误，请稍后再试');
    });
}

// 切换服务类型
function switchServiceType(serviceType, tabElement) {
  // 更新当前类型
  currentServiceType = serviceType;

  // 更新标签页样式
  document.querySelectorAll('.tabs > div').forEach(tab => {
    tab.classList.remove('tab-active');
  });
  tabElement.classList.add('tab-active');

  // 渲染对应类型的服务
  renderServicesByType(serviceType);
}

// 按类型渲染服务项目
function renderServicesByType(serviceType) {
  if (allServicesData.length === 0) {
    showServiceError('暂无服务数据，请稍后再试');
    return;
  }

  // 筛选指定类型的激活服务
  const filteredServices = allServicesData.filter(service =>
    service.service_type === serviceType && service.is_active == 1
  );

  renderServices(filteredServices, serviceType);
}

// 渲染服务项目
function renderServices(services, serviceType = '') {
  const productList = document.getElementById('product-list');

  // 获取类型名称
  const typeNames = {
    'spa': '会所项目',
    'studio': '工作室项目',
    'ts': 'TS项目',
    'cd': 'CD项目'
  };
  const typeName = typeNames[serviceType] || '服务项目';

  if (services.length === 0) {
    productList.innerHTML = `
      <div style="text-align: center; padding: 60px 20px; color: #999;">
        <div style="font-size: 48px; margin-bottom: 16px;">
          ${getTypeIcon(serviceType)}
        </div>
        <div style="font-size: 16px; margin-bottom: 8px;">暂无${typeName}</div>
        <div style="font-size: 14px;">敬请期待更多精彩服务</div>
      </div>
    `;
    return;
  }

  // 渲染服务项目卡片
  productList.innerHTML = services.map(service => `
    <div class="product-item" onclick="showServiceDetail(${service.id})">
      <div class="product-img">
        ${service.image ?
          `<img src="${service.image}" alt="${service.name}" style="width:100%;height:100%;object-fit:cover;border-radius:8px;">` :
          `<div style="width:100%;height:100%;background:${getTypeGradient(serviceType)};border-radius:8px;display:flex;align-items:center;justify-content:center;color:white;font-size:24px;">${getTypeIcon(serviceType)}</div>`
        }
      </div>
      <div class="product-info">
        <div class="product-title">${service.name}</div>
        <span class="product-tags">${service.service_code}</span>
        ${service.duration ? `<span class="product-tags">⏱️ ${service.duration}分钟</span>` : ''}
        <div class="product-price">¥${service.price}</div>
        <div class="product-meta">${service.virtual_sales}人购买</div>
      </div>
    </div>
  `).join('');
}

// 获取类型图标
function getTypeIcon(serviceType) {
  const icons = {
    'spa': '🏨',
    'studio': '🏠',
    'ts': '👩‍🦱',
    'cd': '👗'
  };
  return icons[serviceType] || '🛍️';
}

// 获取类型渐变色
function getTypeGradient(serviceType) {
  const gradients = {
    'spa': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'studio': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'ts': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'cd': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
  };
  return gradients[serviceType] || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
}

// 显示服务错误信息
function showServiceError(message) {
  const productList = document.getElementById('product-list');
  productList.innerHTML = `
    <div style="text-align: center; padding: 40px 20px; color: #f56c6c;">
      <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
      <div style="font-size: 16px; margin-bottom: 8px;">${message}</div>
      <button onclick="loadServices()" style="
        background: #00c6a2;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 14px;
        cursor: pointer;
        margin-top: 12px;
      ">🔄 重新加载</button>
    </div>
  `;
}

// 显示服务详情（可以后续扩展）
function showServiceDetail(serviceId) {
  console.log('点击服务项目:', serviceId);
  // 这里可以添加跳转到服务详情页面的逻辑
  // window.location.href = `service_detail.html?id=${serviceId}`;
  alert(`服务详情功能开发中... (服务ID: ${serviceId})`);
}

// 全局变量存储推荐设置
let recommendSettings = {
  enabled: false,
  systemEnabled: false,
  manualEnabled: false
};

// 加载推荐技师
function loadRecommendedTechs() {
  // 检查推荐功能是否开启
  loadRecommendSettings();

  if (!recommendSettings.enabled) {
    hideRecommendSection();
    return;
  }

  const recommendSection = document.getElementById('recommend-section');
  const recommendTechs = document.getElementById('recommend-techs');

  // 显示加载状态
  recommendTechs.innerHTML = '<div style="text-align: center; padding: 20px; color: #999;">正在加载推荐技师...</div>';

  // 获取技师数据
  fetch('tech_list.php')
    .then(response => response.json())
    .then(data => {
      if (data.success && Array.isArray(data.data)) {
        const recommendedTechs = getRecommendedTechs(data.data);
        renderRecommendedTechs(recommendedTechs);

        if (recommendedTechs.length > 0) {
          showRecommendSection();
        } else {
          hideRecommendSection();
        }
      } else {
        hideRecommendSection();
      }
    })
    .catch(error => {
      console.error('加载推荐技师失败:', error);
      hideRecommendSection();
    });
}

// 加载推荐设置
function loadRecommendSettings() {
  // 如果localStorage中没有设置，则使用默认值（启用）
  const recommendEnabled = localStorage.getItem('recommend_enabled');
  const systemEnabled = localStorage.getItem('system_recommend_enabled');
  const manualEnabled = localStorage.getItem('manual_recommend_enabled');

  recommendSettings.enabled = recommendEnabled === null ? true : recommendEnabled === 'true';
  recommendSettings.systemEnabled = systemEnabled === null ? true : systemEnabled === 'true';
  recommendSettings.manualEnabled = manualEnabled === null ? true : manualEnabled === 'true';
}

// 获取推荐技师列表
function getRecommendedTechs(allTechs) {
  let recommendedTechs = [];

  // 只显示已通过且非休息状态的技师
  const availableTechs = allTechs.filter(tech =>
    tech.status == 1 && tech.is_resting != 1
  );

  // 人工推荐优先
  if (recommendSettings.manualEnabled) {
    const manualRecommended = availableTechs.filter(tech => tech.is_recommended == 1);
    recommendedTechs = recommendedTechs.concat(manualRecommended);
  }

  // 如果人工推荐不足5个，用系统推荐补充
  if (recommendSettings.systemEnabled && recommendedTechs.length < 5) {
    const systemRecommended = availableTechs
      .filter(tech => tech.is_recommended != 1) // 排除已人工推荐的
      .sort((a, b) => (b.virtual_orders || 0) - (a.virtual_orders || 0)) // 按单量排序
      .slice(0, 5 - recommendedTechs.length);

    recommendedTechs = recommendedTechs.concat(systemRecommended);
  }

  // 最多显示5个
  return recommendedTechs.slice(0, 5);
}

// 渲染推荐技师
function renderRecommendedTechs(techs) {
  const recommendTechs = document.getElementById('recommend-techs');

  if (techs.length === 0) {
    recommendTechs.innerHTML = '<div style="text-align: center; padding: 20px; color: #999;">暂无推荐技师</div>';
    return;
  }

  recommendTechs.innerHTML = techs.map(tech => {
    const imagePath = getImagePath(tech.workimg);
    const isManualRecommended = tech.is_recommended == 1;

    return `
      <div class="recommend-tech-item" onclick="showTechnicianDetail(${tech.id})">
        ${isManualRecommended ? '<div class="recommend-badge">推荐</div>' : ''}
        <img src="${imagePath}" alt="${tech.nick}" class="recommend-tech-avatar" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDAiIGN5PSI0MCIgcj0iNDAiIGZpbGw9IiNmNWY1ZjUiLz4KPHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeD0iMjAiIHk9IjIwIj4KPHA+dGggZD0iTTEyIDEyYzIuMjEgMCA0LTEuNzkgNC00cy0xLjc5LTQtNC00LTQgMS43OS00IDQgMS43OSA0IDQgNHptMCAyYy0yLjY3IDAtOCAxLjM0LTggNHYyaDE2di0yYzAtMi42Ni01LjMzLTQtOC00eiIgZmlsbD0iIzk5OSIvPgo8L3N2Zz4KPC9zdmc+'">
        <div class="recommend-tech-name">${tech.nick || '技师' + tech.id}</div>
      </div>
    `;
  }).join('');
}

// 显示推荐区域
function showRecommendSection() {
  const recommendSection = document.getElementById('recommend-section');
  recommendSection.style.display = 'block';
}

// 隐藏推荐区域
function hideRecommendSection() {
  const recommendSection = document.getElementById('recommend-section');
  recommendSection.style.display = 'none';
}

// 处理图片路径
function getImagePath(imagePath) {
  if (!imagePath || !imagePath.trim()) {
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDAiIGN5PSI0MCIgcj0iNDAiIGZpbGw9IiNmNWY1ZjUiLz4KPHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeD0iMjAiIHk9IjIwIj4KPHA+dGggZD0iTTEyIDEyYzIuMjEgMCA0LTEuNzkgNC00cy0xLjc5LTQtNC00LTQgMS43OS00IDQgMS43OSA0IDQgNHptMCAyYy0yLjY3IDAtOCAxLjM0LTggNHYyaDE2di0yYzAtMi42Ni01LjMzLTQtOC00eiIgZmlsbD0iIzk5OSIvPgo8L3N2Zz4KPC9zdmc+';
  }

  const trimmedPath = imagePath.trim();
  if (trimmedPath.startsWith('http')) return trimmedPath;
  if (trimmedPath.startsWith('uploads/')) return trimmedPath;
  return 'uploads/' + trimmedPath;
}

// 显示技师详情（复用technicians.html的函数逻辑）
function showTechnicianDetail(techId) {
  // 获取技师数据
  fetch('tech_list.php')
    .then(response => response.json())
    .then(data => {
      if (data.success && Array.isArray(data.data)) {
        const tech = data.data.find(t => t.id == techId);
        if (tech) {
          createTechDetailModal(tech);
        } else {
          alert('未找到技师信息');
        }
      }
    })
    .catch(error => {
      console.error('获取技师信息失败:', error);
      alert('获取技师信息失败');
    });
}

// 创建技师详情模态框（简化版）
function createTechDetailModal(tech) {
  // 移除已存在的模态框
  const existingModal = document.getElementById('tech-detail-modal');
  if (existingModal) {
    existingModal.remove();
  }

  // 处理生活照片列表
  function getLifeImages(lifeimg) {
    if (!lifeimg || !lifeimg.trim()) return [];
    return lifeimg.split(',')
      .map(img => img.trim())
      .filter(img => img)
      .map(img => getImagePath(img));
  }

  const lifeImages = getLifeImages(tech.lifeimg);
  const workImage = getImagePath(tech.workimg);
  const videoPath = getImagePath(tech.video);

  // 创建模态框HTML
  const modalHTML = `
    <div id="tech-detail-modal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.8); display: flex; justify-content: center; align-items: center; z-index: 2000; padding: 20px;">
      <div style="background: white; border-radius: 16px; max-width: 600px; width: 100%; max-height: 90vh; overflow-y: auto; position: relative;">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px 24px; border-bottom: 1px solid #eee; position: sticky; top: 0; background: white; border-radius: 16px 16px 0 0;">
          <h2 style="margin: 0; color: #333; font-size: 20px;">${tech.nick || '技师' + tech.id}</h2>
          <button onclick="closeTechDetailModal()" style="background: none; border: none; font-size: 28px; color: #999; cursor: pointer; padding: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 50%; transition: all 0.2s ease;">&times;</button>
        </div>
        <div style="padding: 24px;">
          <!-- 基本信息 -->
          <div style="margin-bottom: 32px;">
            <h3 style="margin: 0 0 16px 0; color: #333; font-size: 18px; font-weight: 600; border-bottom: 2px solid #a8e6cf; padding-bottom: 8px;">基本信息</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
              <div style="display: flex; align-items: center; gap: 8px; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                <span style="color: #666; font-weight: 500; min-width: 60px;">昵称:</span>
                <span style="color: #333; font-weight: 600;">${tech.nick || '未填写'}</span>
              </div>
              <div style="display: flex; align-items: center; gap: 8px; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                <span style="color: #666; font-weight: 500; min-width: 60px;">年龄:</span>
                <span style="color: #333; font-weight: 600;">${tech.age || '未知'}</span>
              </div>
              <div style="display: flex; align-items: center; gap: 8px; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                <span style="color: #666; font-weight: 500; min-width: 60px;">身高:</span>
                <span style="color: #333; font-weight: 600;">${tech.height ? tech.height + 'cm' : '未知'}</span>
              </div>
              <div style="display: flex; align-items: center; gap: 8px; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                <span style="color: #666; font-weight: 500; min-width: 60px;">体重:</span>
                <span style="color: #333; font-weight: 600;">${tech.weight ? tech.weight + 'kg' : '未知'}</span>
              </div>
              <div style="display: flex; align-items: center; gap: 8px; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                <span style="color: #666; font-weight: 500; min-width: 60px;">尺寸:</span>
                <span style="color: #333; font-weight: 600;">${tech.size || '未知'}</span>
              </div>
              <div style="display: flex; align-items: center; gap: 8px; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                <span style="color: #666; font-weight: 500; min-width: 60px;">城市:</span>
                <span style="color: #333; font-weight: 600;">${tech.city || '未知'}</span>
              </div>
              <div style="display: flex; align-items: center; gap: 8px; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                <span style="color: #666; font-weight: 500; min-width: 60px;">单量:</span>
                <span style="color: #333; font-weight: 600;">${tech.virtual_orders || 0}</span>
              </div>
              ${tech.is_recommended == 1 ? '<div style="display: flex; align-items: center; gap: 8px; padding: 12px; background: #f8f9fa; border-radius: 8px;"><span style="color: #666; font-weight: 500; min-width: 60px;">特色:</span><span style="background: #ff6b35; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">特别推荐</span></div>' : ''}
            </div>
          </div>

          ${workImage ? `
          <div style="margin-bottom: 32px;">
            <h3 style="margin: 0 0 16px 0; color: #333; font-size: 18px; font-weight: 600; border-bottom: 2px solid #a8e6cf; padding-bottom: 8px;">工作照</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); gap: 12px;">
              <img src="${workImage}" alt="工作照" style="width: 100%; height: 120px; object-fit: cover; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;" onclick="showImageModal('${workImage}')" onerror="this.style.display='none'">
            </div>
          </div>
          ` : ''}

          ${lifeImages.length > 0 ? `
          <div style="margin-bottom: 32px;">
            <h3 style="margin: 0 0 16px 0; color: #333; font-size: 18px; font-weight: 600; border-bottom: 2px solid #a8e6cf; padding-bottom: 8px;">生活照</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); gap: 12px;">
              ${lifeImages.map(img => `
                <img src="${img}" alt="生活照" style="width: 100%; height: 120px; object-fit: cover; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;" onclick="showImageModal('${img}')" onerror="this.style.display='none'">
              `).join('')}
            </div>
          </div>
          ` : ''}

          ${videoPath ? `
          <div style="margin-bottom: 32px;">
            <h3 style="margin: 0 0 16px 0; color: #333; font-size: 18px; font-weight: 600; border-bottom: 2px solid #a8e6cf; padding-bottom: 8px;">视频</h3>
            <div style="display: flex; justify-content: center;">
              <video controls style="max-width: 100%; height: auto; border-radius: 8px; cursor: pointer;" onclick="showVideoModal('${videoPath}')">
                <source src="${videoPath}" type="video/mp4">
                您的浏览器不支持视频播放
              </video>
            </div>
          </div>
          ` : ''}
        </div>
        <div style="padding: 20px 24px; border-top: 1px solid #eee; display: flex; justify-content: center; position: sticky; bottom: 0; background: white; border-radius: 0 0 16px 16px;">
          <button onclick="contactTech(${tech.id})" style="background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%); color: #2e7d32; border: none; padding: 12px 32px; border-radius: 25px; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease;">联系技师</button>
        </div>
      </div>
    </div>
  `;

  // 添加到页面
  document.body.insertAdjacentHTML('beforeend', modalHTML);

  // 点击背景关闭模态框
  const modal = document.getElementById('tech-detail-modal');
  modal.addEventListener('click', function(e) {
    if (e.target === modal) {
      closeTechDetailModal();
    }
  });
}

// 关闭技师详情模态框
function closeTechDetailModal() {
  const modal = document.getElementById('tech-detail-modal');
  if (modal) {
    modal.remove();
  }
}

// 显示图片放大模态框
function showImageModal(imageSrc) {
  const existingModal = document.getElementById('image-modal');
  if (existingModal) {
    existingModal.remove();
  }

  const modalHTML = `
    <div id="image-modal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.9); display: flex; justify-content: center; align-items: center; z-index: 3000; padding: 20px;">
      <div style="position: relative; max-width: 90vw; max-height: 90vh;">
        <button onclick="closeImageModal()" style="position: absolute; top: -40px; right: 0; background: rgba(255, 255, 255, 0.9); border: none; font-size: 24px; color: #333; cursor: pointer; padding: 8px 12px; border-radius: 50%; transition: all 0.2s ease;">&times;</button>
        <img src="${imageSrc}" alt="查看图片" style="max-width: 100%; max-height: 100%; object-fit: contain; border-radius: 8px;">
      </div>
    </div>
  `;

  document.body.insertAdjacentHTML('beforeend', modalHTML);
  const modal = document.getElementById('image-modal');
  modal.addEventListener('click', function(e) {
    if (e.target === modal) {
      closeImageModal();
    }
  });
}

// 关闭图片模态框
function closeImageModal() {
  const modal = document.getElementById('image-modal');
  if (modal) {
    modal.remove();
  }
}

// 显示视频放大模态框
function showVideoModal(videoSrc) {
  const existingModal = document.getElementById('video-modal');
  if (existingModal) {
    existingModal.remove();
  }

  const modalHTML = `
    <div id="video-modal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.9); display: flex; justify-content: center; align-items: center; z-index: 3000; padding: 20px;">
      <div style="position: relative; max-width: 90vw; max-height: 90vh;">
        <button onclick="closeVideoModal()" style="position: absolute; top: -40px; right: 0; background: rgba(255, 255, 255, 0.9); border: none; font-size: 24px; color: #333; cursor: pointer; padding: 8px 12px; border-radius: 50%; transition: all 0.2s ease;">&times;</button>
        <video controls autoplay style="max-width: 100%; max-height: 100%; border-radius: 8px;">
          <source src="${videoSrc}" type="video/mp4">
          您的浏览器不支持视频播放
        </video>
      </div>
    </div>
  `;

  document.body.insertAdjacentHTML('beforeend', modalHTML);
  const modal = document.getElementById('video-modal');
  modal.addEventListener('click', function(e) {
    if (e.target === modal) {
      closeVideoModal();
    }
  });
}

// 关闭视频模态框
function closeVideoModal() {
  const modal = document.getElementById('video-modal');
  if (modal) {
    modal.remove();
  }
}

// 联系技师
function contactTech(techId) {
  alert(`下单找客服 ruyancd (技师ID: ${techId})`);
}

// 加载首页公告
function loadHomeAnnouncement() {
  const announcementEnabled = localStorage.getItem('home_announcement_enabled') === 'true';
  const scrollEnabled = localStorage.getItem('home_scroll_enabled') === 'true';
  const scrollSpeed = parseInt(localStorage.getItem('home_scroll_speed')) || 15;
  const announcementText = localStorage.getItem('home_announcement_text') || '';

  const announcementDiv = document.getElementById('home-announcement');
  const contentDiv = document.getElementById('home-announcement-content');

  if (announcementEnabled && announcementText.trim()) {
    contentDiv.innerHTML = announcementText;

    if (scrollEnabled) {
      contentDiv.className = 'announcement-content scrolling';
      // 动态设置滚动速度
      contentDiv.style.animationDuration = scrollSpeed + 's';
    } else {
      contentDiv.className = 'announcement-content';
      contentDiv.style.animationDuration = '';
    }

    announcementDiv.style.display = 'block';
  } else {
    announcementDiv.style.display = 'none';
  }
}

// 页面加载完成后自动加载服务项目、推荐技师和公告
document.addEventListener('DOMContentLoaded', function() {
  loadServices();
  loadRecommendedTechs();
  loadHomeAnnouncement();

  // 确保默认选中会所项目标签
  const defaultTab = document.querySelector('.tabs > div[data-type="spa"]');
  if (defaultTab) {
    defaultTab.classList.add('tab-active');
  }
});
</script>
</body>
</html>
