<?php
// 调试版技师编辑 - 专门用于排查生活照上传问题
ini_set('display_errors', 1);
ini_set('log_errors', 1);
error_reporting(E_ALL);

session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

try {
    require_once '../db.php';
    
    if (!$pdo) {
        echo json_encode(['success' => false, 'msg' => '数据库连接失败']);
        exit;
    }

    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    if ($id <= 0) {
        echo json_encode(['success' => false, 'msg' => '无效的技师ID']);
        exit;
    }

    // 查询现有技师信息
    $stmt = $pdo->prepare("SELECT * FROM technician WHERE id = ?");
    $stmt->execute([$id]);
    $existingTech = $stmt->fetch();
    
    if (!$existingTech) {
        echo json_encode(['success' => false, 'msg' => '技师不存在']);
        exit;
    }

    // 调试信息
    $debugInfo = [
        'POST' => $_POST,
        'FILES' => $_FILES,
        'existing_lifeimg' => $existingTech['lifeimg']
    ];

    // 只处理生活照上传，简化逻辑
    $lifeimg_str = $existingTech['lifeimg']; // 保持原有
    
    if (isset($_FILES['lifeimg'])) {
        $debugInfo['lifeimg_structure'] = $_FILES['lifeimg'];
        
        // 检查是否是多文件上传格式
        if (is_array($_FILES['lifeimg']['name'])) {
            $debugInfo['is_multiple'] = true;
            $debugInfo['file_count'] = count($_FILES['lifeimg']['name']);
            
            // 简单处理：只处理第一个文件
            if (!empty($_FILES['lifeimg']['name'][0]) && $_FILES['lifeimg']['error'][0] === UPLOAD_ERR_OK) {
                $uploadDir = __DIR__ . '/../uploads/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                $file = [
                    'name' => $_FILES['lifeimg']['name'][0],
                    'tmp_name' => $_FILES['lifeimg']['tmp_name'][0],
                    'size' => $_FILES['lifeimg']['size'][0],
                    'type' => $_FILES['lifeimg']['type'][0],
                    'error' => $_FILES['lifeimg']['error'][0]
                ];
                
                $debugInfo['processing_file'] = $file;
                
                // 简单的文件类型检查
                $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                $allowedExts = ['jpg', 'jpeg', 'png', 'gif'];
                
                if (in_array($extension, $allowedExts) && $file['size'] <= 5 * 1024 * 1024) {
                    $filename = 'life_debug_' . date('Ymd_His') . '_' . rand(1000, 9999) . '.' . $extension;
                    $filepath = $uploadDir . $filename;
                    
                    if (move_uploaded_file($file['tmp_name'], $filepath)) {
                        $lifeimg_str = 'uploads/' . $filename;
                        $debugInfo['upload_success'] = true;
                        $debugInfo['new_path'] = $lifeimg_str;
                    } else {
                        $debugInfo['upload_error'] = 'move_uploaded_file failed';
                    }
                } else {
                    $debugInfo['validation_error'] = 'File type or size invalid';
                }
            } else {
                $debugInfo['no_valid_file'] = true;
            }
        } else {
            $debugInfo['is_multiple'] = false;
            $debugInfo['single_file'] = $_FILES['lifeimg'];
        }
    }

    // 更新数据库（只更新生活照）
    $stmt = $pdo->prepare("UPDATE technician SET lifeimg=? WHERE id=?");
    $result = $stmt->execute([$lifeimg_str, $id]);

    echo json_encode([
        'success' => $result,
        'msg' => $result ? '调试更新成功' : '调试更新失败',
        'debug' => $debugInfo
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'msg' => '调试错误: ' . $e->getMessage(),
        'debug' => isset($debugInfo) ? $debugInfo : []
    ]);
}
?>
