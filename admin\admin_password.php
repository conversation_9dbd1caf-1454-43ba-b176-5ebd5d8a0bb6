<?php
// admin_password.php - 管理员密码管理
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

$passwordFile = __DIR__ . '/admin_password.txt';

// 默认密码（如果文件不存在）
$defaultPassword = 'liyuchun110';

// 获取当前密码
function getCurrentPassword() {
    global $passwordFile, $defaultPassword;
    if (file_exists($passwordFile)) {
        return trim(file_get_contents($passwordFile));
    }
    return $defaultPassword;
}

// 保存新密码
function savePassword($newPassword) {
    global $passwordFile;
    return file_put_contents($passwordFile, $newPassword) !== false;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $currentPassword = isset($_POST['current_password']) ? trim($_POST['current_password']) : '';
    $newPassword = isset($_POST['new_password']) ? trim($_POST['new_password']) : '';
    $confirmPassword = isset($_POST['confirm_password']) ? trim($_POST['confirm_password']) : '';
    
    // 验证输入
    if (empty($currentPassword)) {
        echo json_encode(['success' => false, 'msg' => '请输入当前密码']);
        exit;
    }
    
    if (empty($newPassword)) {
        echo json_encode(['success' => false, 'msg' => '请输入新密码']);
        exit;
    }
    
    if (strlen($newPassword) < 6) {
        echo json_encode(['success' => false, 'msg' => '新密码长度不能少于6位']);
        exit;
    }
    
    if ($newPassword !== $confirmPassword) {
        echo json_encode(['success' => false, 'msg' => '两次输入的新密码不一致']);
        exit;
    }
    
    // 验证当前密码
    $storedPassword = getCurrentPassword();
    if ($currentPassword !== $storedPassword) {
        echo json_encode(['success' => false, 'msg' => '当前密码错误']);
        exit;
    }
    
    // 保存新密码
    if (savePassword($newPassword)) {
        echo json_encode(['success' => true, 'msg' => '密码修改成功']);
    } else {
        echo json_encode(['success' => false, 'msg' => '密码保存失败，请检查文件权限']);
    }
    exit;
}

// GET请求返回成功（用于检查登录状态）
echo json_encode(['success' => true, 'msg' => '已登录']);
?>
