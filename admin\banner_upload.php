<?php
// banner_upload.php - 上传轮播图
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

$bannerFile = __DIR__ . '/banner.json';
$uploadDir = __DIR__ . '/../uploads/';

// 确保上传目录存在
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

try {
    // 检查是否有文件上传
    if (!isset($_FILES['banner']) || $_FILES['banner']['error'] !== UPLOAD_ERR_OK) {
        echo json_encode(['success' => false, 'msg' => '请选择要上传的图片']);
        exit;
    }

    $file = $_FILES['banner'];
    $link = isset($_POST['link']) ? trim($_POST['link']) : '';

    // 验证文件类型
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!in_array($file['type'], $allowedTypes)) {
        echo json_encode(['success' => false, 'msg' => '只支持 JPG、PNG、GIF 格式的图片']);
        exit;
    }

    // 验证文件大小 (5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        echo json_encode(['success' => false, 'msg' => '图片大小不能超过5MB']);
        exit;
    }

    // 生成唯一文件名
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'banner_' . date('Ymd_His') . '_' . rand(1000, 9999) . '.' . $extension;
    $filepath = $uploadDir . $filename;

    // 移动上传的文件
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        echo json_encode(['success' => false, 'msg' => '文件上传失败']);
        exit;
    }

    // 读取现有轮播图数据
    $banners = [];
    if (file_exists($bannerFile)) {
        $banners = json_decode(file_get_contents($bannerFile), true);
        if ($banners === null) {
            $banners = [];
        }
    }

    // 生成新的ID
    $newId = 1;
    if (!empty($banners)) {
        $maxId = max(array_column($banners, 'id'));
        $newId = $maxId + 1;
    }

    // 添加新轮播图
    $newBanner = [
        'id' => $newId,
        'img' => 'uploads/' . $filename,
        'link' => $link
    ];

    array_unshift($banners, $newBanner); // 添加到开头

    // 保存到文件
    if (file_put_contents($bannerFile, json_encode($banners, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES)) === false) {
        // 如果保存失败，删除已上传的文件
        unlink($filepath);
        echo json_encode(['success' => false, 'msg' => '保存轮播图数据失败']);
        exit;
    }

    echo json_encode(['success' => true, 'msg' => '轮播图上传成功', 'data' => $newBanner]);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'msg' => '上传失败: ' . $e->getMessage()]);
}
?>