<?php
// service_add.php - 添加服务项目
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

try {
    // 获取表单数据
    $name = isset($_POST['name']) ? trim($_POST['name']) : '';
    $service_code = isset($_POST['service_code']) ? trim($_POST['service_code']) : '';
    $duration = isset($_POST['duration']) ? intval($_POST['duration']) : 0;
    $price = isset($_POST['price']) ? floatval($_POST['price']) : 0;
    $virtual_sales = isset($_POST['virtual_sales']) ? intval($_POST['virtual_sales']) : 0;
    $service_type = isset($_POST['service_type']) ? trim($_POST['service_type']) : 'spa';
    $is_active = isset($_POST['is_active']) ? intval($_POST['is_active']) : 1;

    // 基本验证
    if (!$name) {
        echo json_encode(['success' => false, 'msg' => '请输入项目名称']);
        exit;
    }

    if (!$service_code) {
        echo json_encode(['success' => false, 'msg' => '请输入项目编号']);
        exit;
    }

    if ($duration <= 0) {
        echo json_encode(['success' => false, 'msg' => '请输入有效的项目时长']);
        exit;
    }

    if ($price <= 0) {
        echo json_encode(['success' => false, 'msg' => '请输入有效的项目价格']);
        exit;
    }

    // 验证项目类型
    $allowedTypes = ['spa', 'studio', 'ts', 'cd'];
    if (!in_array($service_type, $allowedTypes)) {
        echo json_encode(['success' => false, 'msg' => '无效的项目类型']);
        exit;
    }

    // 检查同一项目类型下的编号是否已存在
    $stmt = $pdo->prepare("SELECT id FROM services WHERE service_code = ? AND service_type = ?");
    $stmt->execute([$service_code, $service_type]);
    if ($stmt->fetch()) {
        $typeNames = [
            'spa' => '会所项目',
            'studio' => '工作室项目',
            'ts' => 'TS项目',
            'cd' => 'CD项目'
        ];
        $typeName = $typeNames[$service_type] ?? '该项目类型';
        echo json_encode(['success' => false, 'msg' => "编号「{$service_code}」在{$typeName}中已存在，请使用其他编号"]);
        exit;
    }

    // 处理图片上传
    $image_path = null;
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['image'];
        
        // 验证文件类型
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!in_array($file['type'], $allowedTypes)) {
            echo json_encode(['success' => false, 'msg' => '项目图片只支持 JPG、PNG、GIF 格式']);
            exit;
        }

        // 验证文件大小 (5MB)
        if ($file['size'] > 5 * 1024 * 1024) {
            echo json_encode(['success' => false, 'msg' => '项目图片大小不能超过5MB']);
            exit;
        }

        // 创建上传目录
        $uploadDir = __DIR__ . '/../uploads/services/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // 生成唯一文件名
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'service_' . date('Ymd_His') . '_' . rand(1000, 9999) . '.' . $extension;
        $filepath = $uploadDir . $filename;

        // 移动上传的文件
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            echo json_encode(['success' => false, 'msg' => '图片上传失败']);
            exit;
        }

        $image_path = 'uploads/services/' . $filename;
    }

    // 获取当前最大排序值
    $stmt = $pdo->query("SELECT MAX(sort_order) as max_sort FROM services");
    $maxSort = $stmt->fetchColumn() ?: 0;
    $newSortOrder = $maxSort + 1;

    // 插入数据库
    $stmt = $pdo->prepare("INSERT INTO services (name, image, service_code, duration, price, virtual_sales, service_type, sort_order, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $result = $stmt->execute([$name, $image_path, $service_code, $duration, $price, $virtual_sales, $service_type, $newSortOrder, $is_active]);
    
    if ($result) {
        $serviceId = $pdo->lastInsertId();
        echo json_encode([
            'success' => true, 
            'msg' => "服务项目「{$name}」添加成功",
            'data' => ['id' => $serviceId]
        ]);
    } else {
        // 如果数据库插入失败，删除已上传的图片
        if ($image_path && file_exists($uploadDir . basename($image_path))) {
            unlink($uploadDir . basename($image_path));
        }
        echo json_encode(['success' => false, 'msg' => '添加失败']);
    }

} catch (Exception $e) {
    error_log("Service add error: " . $e->getMessage());
    echo json_encode(['success' => false, 'msg' => '添加失败: ' . $e->getMessage()]);
}
?>
