<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统自动技师动态演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        .demo-section:hover {
            transform: translateY(-5px);
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
            font-size: 1.4em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-icon {
            margin-right: 10px;
            font-size: 1.2em;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        .btn.success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        .btn.warning {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
        }
        .workflow {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 30px;
        }
        .workflow h3 {
            text-align: center;
            color: #333;
            margin-bottom: 25px;
            font-size: 1.5em;
        }
        .workflow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }
        .workflow-step {
            flex: 1;
            min-width: 200px;
            text-align: center;
            position: relative;
        }
        .workflow-step::after {
            content: '→';
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 24px;
            color: #667eea;
            font-weight: bold;
        }
        .workflow-step:last-child::after {
            display: none;
        }
        .step-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .step-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .step-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
        .live-demo {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .live-demo h3 {
            text-align: center;
            color: #333;
            margin-bottom: 25px;
            font-size: 1.5em;
        }
        .demo-controls {
            text-align: center;
            margin-bottom: 25px;
        }
        .feed-preview {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            background: #f9f9f9;
        }
        .feed-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        .feed-item:hover {
            transform: translateY(-2px);
        }
        .feed-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }
        .feed-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .feed-info {
            flex: 1;
        }
        .feed-name {
            font-weight: bold;
            color: #333;
        }
        .feed-time {
            font-size: 12px;
            color: #999;
        }
        .system-badge {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
        }
        .feed-content {
            color: #333;
            margin: 12px 0;
            font-size: 14px;
        }
        .feed-details {
            background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            color: #1976d2;
        }
        .action-badge {
            background: #ff9800;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: bold;
            margin-right: 8px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #999;
            font-style: italic;
        }
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
            .workflow-steps {
                flex-direction: column;
            }
            .workflow-step::after {
                content: '↓';
                right: auto;
                bottom: -15px;
                top: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 系统自动技师动态</h1>
            <p>智能检测技师位置变化，自动生成实时动态</p>
        </div>
        
        <div class="demo-grid">
            <div class="demo-section">
                <h3>🎯 核心功能</h3>
                <ul class="feature-list">
                    <li><span class="feature-icon">🔄</span>自动检测技师城市变更</li>
                    <li><span class="feature-icon">📝</span>智能生成标准化动态内容</li>
                    <li><span class="feature-icon">⚡</span>实时发布到动态流</li>
                    <li><span class="feature-icon">📱</span>前端即时显示更新</li>
                    <li><span class="feature-icon">🎨</span>美观的系统动态样式</li>
                </ul>
            </div>
            
            <div class="demo-section">
                <h3>🚀 触发场景</h3>
                <ul class="feature-list">
                    <li><span class="feature-icon">🏠</span>技师首次入住城市</li>
                    <li><span class="feature-icon">🔄</span>技师更换服务城市</li>
                    <li><span class="feature-icon">👨‍💼</span>管理员修改技师信息</li>
                    <li><span class="feature-icon">📊</span>系统数据同步更新</li>
                    <li><span class="feature-icon">🔍</span>实时监控位置变化</li>
                </ul>
            </div>
        </div>
        
        <div class="workflow">
            <h3>🔄 自动化工作流程</h3>
            <div class="workflow-steps">
                <div class="workflow-step">
                    <div class="step-icon">🏠</div>
                    <div class="step-title">技师变更</div>
                    <div class="step-desc">技师入住或更换城市</div>
                </div>
                <div class="workflow-step">
                    <div class="step-icon">🔍</div>
                    <div class="step-title">系统检测</div>
                    <div class="step-desc">自动检测数据变化</div>
                </div>
                <div class="workflow-step">
                    <div class="step-icon">📝</div>
                    <div class="step-title">生成动态</div>
                    <div class="step-desc">智能生成动态内容</div>
                </div>
                <div class="workflow-step">
                    <div class="step-icon">📱</div>
                    <div class="step-title">实时显示</div>
                    <div class="step-desc">前端即时更新显示</div>
                </div>
            </div>
        </div>
        
        <div class="live-demo">
            <h3>🎮 实时演示</h3>
            <div class="demo-controls">
                <button class="btn" onclick="loadSystemFeeds()">📱 加载系统动态</button>
                <button class="btn success" onclick="simulateCityChange()">🔄 模拟城市变更</button>
                <button class="btn warning" onclick="openFeedPage()">🔗 查看动态页面</button>
                <button class="btn" onclick="openAdminPanel()">👨‍💼 后台管理</button>
            </div>
            
            <div id="feed-preview" class="feed-preview">
                <div class="loading">点击"加载系统动态"查看实时技师动态</div>
            </div>
        </div>
    </div>

    <script>
        // 加载系统技师动态
        async function loadSystemFeeds() {
            const previewContainer = document.getElementById('feed-preview');
            previewContainer.innerHTML = '<div class="loading">正在加载系统动态...</div>';
            
            try {
                const response = await fetch('technician_feed_list.php');
                const data = await response.json();
                
                if (data.success && data.feeds && data.feeds.length > 0) {
                    renderSystemFeeds(data.feeds);
                } else {
                    previewContainer.innerHTML = '<div class="loading">暂无系统动态，请先模拟城市变更</div>';
                }
            } catch (error) {
                previewContainer.innerHTML = `<div class="loading">加载失败: ${error.message}</div>`;
            }
        }
        
        // 渲染系统动态
        function renderSystemFeeds(feeds) {
            const previewContainer = document.getElementById('feed-preview');
            
            previewContainer.innerHTML = feeds.map(feed => `
                <div class="feed-item">
                    <div class="feed-header">
                        <div class="feed-avatar">${feed.nickname ? feed.nickname.charAt(0) : '技'}</div>
                        <div class="feed-info">
                            <div class="feed-name">${feed.nickname || '技师'}</div>
                            <div class="feed-time">${feed.time || ''} · ${feed.location || ''}</div>
                        </div>
                        <div class="system-badge">系统动态</div>
                    </div>
                    <div class="feed-content">${feed.content || ''}</div>
                    ${feed.technician_info ? `
                        <div class="feed-details">
                            <span class="action-badge">${feed.technician_info.action_type === 'move' ? '更换城市' : '新入住'}</span>
                            <strong>${feed.technician_info.city}</strong>
                            ${feed.technician_info.previous_city ? ` (从${feed.technician_info.previous_city})` : ''}<br>
                            📊 年龄:${feed.technician_info.age} | 身高:${feed.technician_info.height}cm | 
                            体重:${feed.technician_info.weight}kg | 服务年限:${feed.technician_info.service_years}年
                        </div>
                    ` : ''}
                </div>
            `).join('');
        }
        
        // 模拟城市变更
        async function simulateCityChange() {
            try {
                const cities = ['三亚市', '海口市', '儋州市', '琼海市', '文昌市', '万宁市'];
                const randomCity = cities[Math.floor(Math.random() * cities.length)];
                
                const response = await fetch('test_tech_city_change_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        new_city: randomCity
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(`✅ 模拟成功！技师${result.technician_name}从${result.old_city}变更到${result.new_city}`);
                    // 自动刷新动态列表
                    setTimeout(loadSystemFeeds, 1000);
                } else {
                    alert(`ℹ️ ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 模拟失败: ${error.message}`);
            }
        }
        
        // 打开动态页面
        function openFeedPage() {
            window.open('feed.html', '_blank');
        }
        
        // 打开后台管理
        function openAdminPanel() {
            window.open('admin/admin_login.html', '_blank');
        }
        
        // 页面加载时自动加载动态
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(loadSystemFeeds, 1000);
        });
        
        // 添加一些动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.demo-section, .workflow, .live-demo');
            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    section.style.transition = 'all 0.6s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
