<?php
// amap_key.php
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    echo json_encode(['success'=>false, 'msg'=>'未登录']);
    exit;
}
$keyFile = __DIR__ . '/../amap_key.txt';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $key = isset($_POST['amap_key']) ? trim($_POST['amap_key']) : '';
    if ($key) {
        // 允许填写时自动引用之前的key（如果没填则用旧值）
        $oldKey = file_exists($keyFile) ? trim(file_get_contents($keyFile)) : '';
        if ($key === '__USE_OLD__' && $oldKey) {
            $key = $oldKey;
        }
        file_put_contents($keyFile, $key);
        echo json_encode(['success'=>true, 'msg'=>'保存成功', 'amap_key'=>$key]);
    } else {
        // 如果没填，自动引用旧值
        $oldKey = file_exists($keyFile) ? trim(file_get_contents($keyFile)) : '';
        if ($oldKey) {
            echo json_encode(['success'=>true, 'msg'=>'已引用之前的key', 'amap_key'=>$oldKey]);
        } else {
            echo json_encode(['success'=>false, 'msg'=>'Key不能为空']);
        }
    }
    exit;
}
// GET请求返回当前key
$key = file_exists($keyFile) ? trim(file_get_contents($keyFile)) : '';
echo json_encode(['success'=>true, 'amap_key'=>$key]);
