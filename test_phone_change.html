<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试技师手机号修改功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .btn.warning:hover {
            background: #e0a800;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .feature-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .feature-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }
        .flow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            flex-wrap: wrap;
            gap: 15px;
        }
        .flow-step {
            flex: 1;
            min-width: 150px;
            text-align: center;
            position: relative;
        }
        .flow-step::after {
            content: '→';
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 20px;
            color: #007bff;
            font-weight: bold;
        }
        .flow-step:last-child::after {
            display: none;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-weight: bold;
        }
        .step-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .step-desc {
            font-size: 12px;
            color: #666;
        }
        .test-scenario {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .test-scenario h4 {
            margin-top: 0;
            color: #333;
        }
        .test-steps {
            list-style: none;
            padding: 0;
        }
        .test-steps li {
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .test-steps li:last-child {
            border-bottom: none;
        }
        .highlight {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .highlight strong {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 技师手机号修改功能测试</h1>
        
        <div class="section">
            <h3>🎯 功能概述</h3>
            <p>技师现在可以在修改资料页面更改手机号，修改后的手机号将成为新的登录账号，密码保持不变。</p>
            
            <div class="highlight">
                <strong>🔑 核心功能：</strong><br>
                • 允许技师修改手机号<br>
                • 修改后手机号成为新的登录账号<br>
                • 密码保持不变<br>
                • 防止手机号重复使用<br>
                • 自动更新本地登录状态
            </div>
        </div>
        
        <div class="section">
            <h3>✨ 功能特性</h3>
            <div class="feature-grid">
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <div class="feature-title">手机号可编辑</div>
                    <div class="feature-desc">技师修改资料时可以更改手机号，不再是只读字段</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔐</div>
                    <div class="feature-title">账号自动更新</div>
                    <div class="feature-desc">修改手机号后，登录账号自动更新为新手机号</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔑</div>
                    <div class="feature-title">密码保持不变</div>
                    <div class="feature-desc">只更新手机号，原密码继续有效</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🛡️</div>
                    <div class="feature-title">重复检查</div>
                    <div class="feature-desc">防止使用已被其他技师占用的手机号</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">⚠️</div>
                    <div class="feature-title">确认提示</div>
                    <div class="feature-desc">修改手机号前显示确认对话框</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔄</div>
                    <div class="feature-title">状态同步</div>
                    <div class="feature-desc">自动更新本地存储的登录信息</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>🔄 修改流程</h3>
            <div class="flow-steps">
                <div class="flow-step">
                    <div class="step-number">1</div>
                    <div class="step-title">进入修改页面</div>
                    <div class="step-desc">技师点击修改资料</div>
                </div>
                <div class="flow-step">
                    <div class="step-number">2</div>
                    <div class="step-title">修改手机号</div>
                    <div class="step-desc">在表单中输入新手机号</div>
                </div>
                <div class="flow-step">
                    <div class="step-number">3</div>
                    <div class="step-title">确认修改</div>
                    <div class="step-desc">系统显示确认对话框</div>
                </div>
                <div class="flow-step">
                    <div class="step-number">4</div>
                    <div class="step-title">验证处理</div>
                    <div class="step-desc">后端验证并更新数据</div>
                </div>
                <div class="flow-step">
                    <div class="step-number">5</div>
                    <div class="step-title">账号更新</div>
                    <div class="step-desc">登录账号更新为新手机号</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>🧪 测试场景</h3>
            
            <div class="test-scenario">
                <h4>📱 场景1：正常手机号修改</h4>
                <ul class="test-steps">
                    <li>✅ 技师登录系统</li>
                    <li>✅ 进入修改资料页面</li>
                    <li>✅ 修改手机号为新的有效号码</li>
                    <li>✅ 确认修改操作</li>
                    <li>✅ 系统更新账号信息</li>
                    <li>✅ 使用新手机号登录验证</li>
                </ul>
            </div>
            
            <div class="test-scenario">
                <h4>🚫 场景2：手机号重复检查</h4>
                <ul class="test-steps">
                    <li>✅ 技师尝试修改为已存在的手机号</li>
                    <li>✅ 系统检测到手机号重复</li>
                    <li>✅ 显示错误提示：该手机号已被其他技师使用</li>
                    <li>✅ 阻止修改操作</li>
                </ul>
            </div>
            
            <div class="test-scenario">
                <h4>📝 场景3：格式验证</h4>
                <ul class="test-steps">
                    <li>✅ 技师输入格式错误的手机号</li>
                    <li>✅ 前端实时验证格式</li>
                    <li>✅ 后端二次验证格式</li>
                    <li>✅ 显示格式错误提示</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h3>🔧 测试操作</h3>
            <div style="margin: 15px 0;">
                <button class="btn success" onclick="openProfilePage()">📱 打开个人中心</button>
                <button class="btn" onclick="openAdminPanel()">👨‍💼 打开后台管理</button>
                <button class="btn warning" onclick="showTestGuide()">📋 查看测试指南</button>
            </div>
            
            <div id="test-result">
                <div class="info">点击上方按钮开始测试手机号修改功能</div>
            </div>
        </div>
        
        <div class="section">
            <h3>⚠️ 注意事项</h3>
            <div class="warning">
                <strong>重要提醒：</strong><br>
                • 修改手机号后，必须使用新手机号登录<br>
                • 原手机号将无法再用于登录<br>
                • 密码保持不变，无需重新设置<br>
                • 建议修改前记录好新手机号<br>
                • 确保新手机号未被其他技师使用
            </div>
        </div>
    </div>

    <script>
        // 打开个人中心页面
        function openProfilePage() {
            window.open('profile.html', '_blank');
            
            document.getElementById('test-result').innerHTML = `
                <div class="success">
                    📱 已打开个人中心页面<br><br>
                    
                    <strong>测试步骤：</strong><br>
                    1. 确保已登录技师账号<br>
                    2. 在个人中心找到并点击"修改资料"<br>
                    3. 在手机号字段输入新的手机号<br>
                    4. 填写其他必要信息<br>
                    5. 点击保存，观察确认对话框<br>
                    6. 确认修改，查看成功提示<br>
                    7. 退出登录，使用新手机号重新登录验证
                </div>
            `;
        }
        
        // 打开后台管理
        function openAdminPanel() {
            window.open('admin/admin_login.html', '_blank');
            
            document.getElementById('test-result').innerHTML = `
                <div class="info">
                    👨‍💼 已打开后台管理页面<br><br>
                    
                    <strong>管理员验证：</strong><br>
                    1. 登录后台管理系统<br>
                    2. 进入技师管理页面<br>
                    3. 查看技师列表中的手机号信息<br>
                    4. 验证手机号修改是否正确保存<br>
                    5. 检查技师详情中的手机号显示
                </div>
            `;
        }
        
        // 显示测试指南
        function showTestGuide() {
            document.getElementById('test-result').innerHTML = `
                <div class="info">
                    📋 <strong>完整测试指南</strong><br><br>
                    
                    <strong>准备工作：</strong><br>
                    • 准备2个不同的手机号用于测试<br>
                    • 确保有已注册的技师账号<br>
                    • 记录当前登录的手机号<br><br>
                    
                    <strong>测试步骤：</strong><br>
                    1️⃣ <strong>正常修改测试</strong><br>
                    &nbsp;&nbsp;&nbsp;• 登录技师账号<br>
                    &nbsp;&nbsp;&nbsp;• 修改手机号为新号码<br>
                    &nbsp;&nbsp;&nbsp;• 验证修改成功<br>
                    &nbsp;&nbsp;&nbsp;• 使用新手机号重新登录<br><br>
                    
                    2️⃣ <strong>重复号码测试</strong><br>
                    &nbsp;&nbsp;&nbsp;• 尝试修改为已存在的手机号<br>
                    &nbsp;&nbsp;&nbsp;• 验证系统阻止修改<br>
                    &nbsp;&nbsp;&nbsp;• 检查错误提示信息<br><br>
                    
                    3️⃣ <strong>格式验证测试</strong><br>
                    &nbsp;&nbsp;&nbsp;• 输入格式错误的手机号<br>
                    &nbsp;&nbsp;&nbsp;• 验证前端和后端验证<br>
                    &nbsp;&nbsp;&nbsp;• 检查提示信息准确性<br><br>
                    
                    <strong>验证要点：</strong><br>
                    ✅ 手机号字段可编辑<br>
                    ✅ 修改前显示确认对话框<br>
                    ✅ 重复手机号被阻止<br>
                    ✅ 格式验证正常工作<br>
                    ✅ 修改后账号自动更新<br>
                    ✅ 密码保持不变<br>
                    ✅ 新手机号可正常登录
                </div>
            `;
        }
        
        // 页面加载时显示功能说明
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                document.getElementById('test-result').innerHTML = `
                    <div class="success">
                        🎉 技师手机号修改功能已实现！<br><br>
                        
                        <strong>主要改进：</strong><br>
                        ✅ 手机号字段从只读改为可编辑<br>
                        ✅ 添加手机号格式验证<br>
                        ✅ 实现手机号重复检查<br>
                        ✅ 修改前显示确认提示<br>
                        ✅ 自动更新登录账号<br>
                        ✅ 保持原密码不变<br>
                        ✅ 同步本地登录状态<br><br>
                        
                        现在可以开始测试功能了！
                    </div>
                `;
            }, 1000);
        });
    </script>
</body>
</html>
