<?php
// 城市热门切换接口，POST参数 id, is_hot，返回JSON
require_once '../db.php';
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    echo json_encode(['success'=>false,'msg'=>'未登录']);
    exit;
}
$id = isset($_POST['id']) ? intval($_POST['id']) : 0;
$is_hot = isset($_POST['is_hot']) ? intval($_POST['is_hot']) : 0;
if ($id <= 0) {
    echo json_encode(['success'=>false,'msg'=>'参数错误']);
    exit;
}
try {
    $stmt = $pdo->prepare('UPDATE city SET is_hot = ? WHERE id = ?');
    $stmt->execute([$is_hot, $id]);
    echo json_encode(['success'=>true]);
} catch (Exception $e) {
    echo json_encode(['success'=>false,'msg'=>'数据库错误']);
}
