<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建城市表</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
            color: #856404;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .btn-create {
            background: #28a745;
        }
        .btn-create:hover {
            background: #1e7e34;
        }
        .btn-test {
            background: #ffc107;
            color: #212529;
        }
        .btn-test:hover {
            background: #e0a800;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ 创建城市表</h1>
        
        <div class="warning">
            <h4>⚠️ 重要说明</h4>
            <p>检测到数据库中缺少city表，这是热门城市功能正常工作所必需的。</p>
            <p>此操作将创建city表并插入基础城市数据。</p>
        </div>
        
        <div class="test-section">
            <h3>🔍 检查当前状态</h3>
            <button onclick="checkCurrentState()">检查数据库状态</button>
            <div id="check-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🏗️ 创建城市表</h3>
            <p style="color: #666; font-size: 14px;">
                将创建包含以下字段的city表：<br>
                - id (主键)<br>
                - name (城市名称)<br>
                - lng, lat (经纬度)<br>
                - is_hot (是否热门)<br>
                - sort_order (排序)<br>
                - created_at, updated_at (时间戳)
            </p>
            <button class="btn-create" onclick="createCityTable()">创建城市表</button>
            <div id="create-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>✅ 验证创建结果</h3>
            <button onclick="verifyCityTable()">验证表结构</button>
            <button class="btn-test" onclick="testHotCityFunction()">测试热门城市功能</button>
            <div id="verify-result" class="result"></div>
        </div>
    </div>

    <script>
        function checkCurrentState() {
            const resultDiv = document.getElementById('check-result');
            resultDiv.textContent = '正在检查数据库状态...';
            resultDiv.className = 'result';
            
            // 尝试获取城市列表来检查表是否存在
            fetch('admin/city_list.php')
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data)) {
                        resultDiv.textContent = `✅ city表已存在

当前状态:
- 城市数量: ${data.length}
- 热门城市: ${data.filter(city => city.is_hot == 1).length}

前5个城市:
${data.slice(0, 5).map(city => `- ${city.name} ${city.is_hot == 1 ? '⭐' : ''}`).join('\n')}

city表已存在，可以直接使用热门城市功能。`;
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.textContent = `❌ city表不存在或有问题

错误信息:
${JSON.stringify(data, null, 2)}

需要创建city表才能使用热门城市功能。`;
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    resultDiv.textContent = `❌ 检查失败

错误信息: ${error.message}

可能原因:
1. city表不存在
2. 数据库连接问题
3. 权限问题

建议创建city表。`;
                    resultDiv.className = 'result error';
                });
        }
        
        function createCityTable() {
            const resultDiv = document.getElementById('create-result');
            resultDiv.textContent = '正在创建城市表...';
            resultDiv.className = 'result';
            
            fetch('admin/create_city_table.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.action === 'table_exists') {
                            resultDiv.textContent = `ℹ️ 表已存在

${data.msg}

city表已经存在，无需重复创建。`;
                            resultDiv.className = 'result success';
                        } else {
                            const tableData = data.data;
                            resultDiv.textContent = `✅ 城市表创建成功！

创建结果:
- 表结构: 已创建
- 插入城市: ${tableData.cities_inserted}/${tableData.total_cities}
- 包含字段: id, name, lng, lat, is_hot, sort_order, created_at, updated_at

基础城市数据已插入，包括：
- 11个热门城市（直辖市+主要省会）
- 9个普通城市（重要地级市）

现在可以使用热门城市功能了！`;
                            resultDiv.className = 'result success';
                        }
                    } else {
                        resultDiv.textContent = `❌ 创建失败

错误信息: ${data.msg}

请检查：
1. 数据库连接是否正常
2. 是否有创建表的权限
3. 数据库空间是否足够`;
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    resultDiv.textContent = `❌ 创建过程中出错

网络错误: ${error.message}

请检查：
1. 是否已登录后台管理系统
2. 网络连接是否正常
3. 服务器是否正常运行`;
                    resultDiv.className = 'result error';
                });
        }
        
        function verifyCityTable() {
            const resultDiv = document.getElementById('verify-result');
            resultDiv.textContent = '正在验证表结构...';
            resultDiv.className = 'result';
            
            fetch('admin/city_list.php')
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data)) {
                        const hotCities = data.filter(city => city.is_hot == 1);
                        const normalCities = data.filter(city => city.is_hot != 1);
                        
                        resultDiv.textContent = `✅ 表结构验证成功

城市表统计:
- 总城市数: ${data.length}
- 热门城市: ${hotCities.length}
- 普通城市: ${normalCities.length}

热门城市列表:
${hotCities.map(city => `- ${city.name} (ID: ${city.id})`).join('\n')}

表结构正常，可以进行热门城市操作。`;
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.textContent = `❌ 验证失败

返回数据: ${JSON.stringify(data, null, 2)}`;
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    resultDiv.textContent = `❌ 验证错误: ${error.message}`;
                    resultDiv.className = 'result error';
                });
        }
        
        function testHotCityFunction() {
            const resultDiv = document.getElementById('verify-result');
            resultDiv.textContent = '正在测试热门城市功能...';
            resultDiv.className = 'result';
            
            // 先获取城市列表
            fetch('admin/city_list.php')
                .then(response => response.json())
                .then(cities => {
                    if (!Array.isArray(cities) || cities.length === 0) {
                        throw new Error('无法获取城市列表');
                    }
                    
                    // 找一个普通城市来测试设为热门
                    const testCity = cities.find(city => city.is_hot != 1) || cities[0];
                    const newHotStatus = testCity.is_hot == 1 ? 0 : 1;
                    
                    return fetch('admin/city_set_hot.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            id: testCity.id,
                            is_hot: newHotStatus
                        })
                    })
                    .then(response => response.json())
                    .then(result => {
                        resultDiv.textContent = `热门城市功能测试结果:

测试城市: ${testCity.name} (ID: ${testCity.id})
操作: ${newHotStatus ? '设为热门' : '取消热门'}

API响应:
${JSON.stringify(result, null, 2)}

${result.success ? '✅ 热门城市功能正常工作！' : '❌ 热门城市功能有问题'}

${result.success ? '现在可以在后台管理中正常使用热门城市功能了。' : '请检查API和数据库配置。'}`;
                        resultDiv.className = result.success ? 'result success' : 'result error';
                    });
                })
                .catch(error => {
                    resultDiv.textContent = `❌ 测试失败: ${error.message}`;
                    resultDiv.className = 'result error';
                });
        }
        
        // 页面加载时自动检查状态
        window.onload = function() {
            checkCurrentState();
        };
    </script>
</body>
</html>
