<?php
// feed_like.php
header('Content-Type: application/json; charset=utf-8');
require_once 'db.php';

$user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
$feed_id = isset($_POST['feed_id']) ? intval($_POST['feed_id']) : 0;
if (!$user_id || !$feed_id) {
    echo json_encode(['success'=>false, 'msg'=>'缺少参数']);
    exit;
}
// 点赞表结构: feed_like(id, user_id, feed_id, created_at)
$sql = "INSERT IGNORE INTO feed_like (user_id, feed_id, created_at) VALUES (?, ?, NOW())";
$stmt = $pdo->prepare($sql);
$stmt->execute([$user_id, $feed_id]);
// 更新 feed 表点赞数
$pdo->query("UPDATE feed SET likes = likes + 1 WHERE id = $feed_id");
echo json_encode(['success'=>true, 'msg'=>'点赞成功']);
