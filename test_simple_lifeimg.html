<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版生活照上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 简化版生活照上传测试</h1>
        
        <div class="info result">
这是一个简化版的生活照上传测试，用于排查多文件上传的问题。
请选择一个存在的技师ID，然后上传1-2张生活照片进行测试。
        </div>

        <form id="testForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="id">技师ID:</label>
                <input type="number" id="id" name="id" value="1" required min="1">
                <small style="color: #666;">请输入一个存在的技师ID</small>
            </div>

            <div class="form-group">
                <label for="nick">昵称:</label>
                <input type="text" id="nick" name="nick" value="测试技师" required>
            </div>

            <div class="form-group">
                <label for="phone">手机号:</label>
                <input type="text" id="phone" name="phone" value="13800138000" required>
            </div>

            <div class="form-group">
                <label for="lifeimg">生活照片 (可选择多张):</label>
                <input type="file" id="lifeimg" name="lifeimg" accept="image/*" multiple>
                <small style="color: #666;">支持 JPG、PNG、GIF 格式，每张最大5MB</small>
            </div>

            <button type="submit" class="btn">测试上传</button>
        </form>

        <div id="result" class="result" style="display: none;"></div>

        <div style="margin-top: 30px;">
            <h3>测试步骤:</h3>
            <ol>
                <li>确保技师ID存在（可以先去后台查看）</li>
                <li>选择1-2张图片文件</li>
                <li>点击"测试上传"</li>
                <li>查看返回的调试信息</li>
                <li>如果成功，检查 uploads 目录中的文件</li>
            </ol>
        </div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            const submitBtn = this.querySelector('button[type="submit"]');
            
            // 显示上传状态
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在上传，请稍候...';
            submitBtn.disabled = true;
            submitBtn.textContent = '上传中...';
            
            // 显示选择的文件信息
            const fileInput = document.getElementById('lifeimg');
            if (fileInput.files.length > 0) {
                let fileInfo = '选择的文件:\n';
                for (let i = 0; i < fileInput.files.length; i++) {
                    const file = fileInput.files[i];
                    fileInfo += `${i + 1}. ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)\n`;
                }
                resultDiv.textContent = fileInfo + '\n正在上传...';
            }
            
            fetch('admin/tech_edit_simple_lifeimg.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = data.success ? 'result success' : 'result error';
                
                if (data.success) {
                    // 如果成功，显示一些有用的信息
                    let summary = '\n\n=== 上传总结 ===\n';
                    if (data.data && data.data.final_paths) {
                        summary += '✅ 生活照上传成功\n';
                        summary += '📁 文件路径: ' + data.data.final_paths + '\n';
                    }
                    if (data.data) {
                        const fileCount = Object.keys(data.data).filter(key => key.startsWith('file_')).length;
                        summary += '📊 处理文件数: ' + fileCount + '\n';
                    }
                    resultDiv.textContent += summary;
                }
            })
            .catch(error => {
                resultDiv.textContent = '网络错误: ' + error.message;
                resultDiv.className = 'result error';
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = '测试上传';
            });
        });

        // 文件选择时显示信息
        document.getElementById('lifeimg').addEventListener('change', function() {
            const files = this.files;
            if (files.length > 0) {
                let info = `已选择 ${files.length} 个文件:\n`;
                for (let i = 0; i < files.length; i++) {
                    info += `${i + 1}. ${files[i].name} (${(files[i].size / 1024 / 1024).toFixed(2)}MB)\n`;
                }
                console.log(info);
            }
        });
    </script>
</body>
</html>
