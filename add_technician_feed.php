<?php
// add_technician_feed.php - 添加技师动态
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'db.php';

try {
    // 只允许POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只允许POST请求');
    }
    
    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    // 验证必需参数
    $requiredFields = ['technician_id', 'technician_name', 'city', 'action_type'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            throw new Exception("缺少必需参数: {$field}");
        }
    }
    
    $technician_id = intval($input['technician_id']);
    $technician_name = trim($input['technician_name']);
    $city = trim($input['city']);
    $action_type = trim($input['action_type']);
    
    // 验证action_type
    if (!in_array($action_type, ['join', 'move'])) {
        throw new Exception('action_type必须是join或move');
    }
    
    // 可选参数
    $age = isset($input['age']) ? intval($input['age']) : null;
    $height = isset($input['height']) ? intval($input['height']) : null;
    $weight = isset($input['weight']) ? intval($input['weight']) : null;
    $service_years = isset($input['service_years']) ? intval($input['service_years']) : null;
    $previous_city = isset($input['previous_city']) ? trim($input['previous_city']) : null;
    $avatar = isset($input['avatar']) ? trim($input['avatar']) : 'images/tx.jpg';
    
    // 生成动态内容
    $specs = [];
    if ($age) $specs[] = $age;
    if ($height) $specs[] = $height;
    if ($weight) $specs[] = $weight;
    if ($service_years) $specs[] = $service_years;
    
    $specsText = !empty($specs) ? ' ' . implode('/', $specs) : '';
    $content = "技师{$technician_name}{$specsText} 到{$city}了";
    
    // 创建技师动态表（如果不存在）
    $createTableSql = "CREATE TABLE IF NOT EXISTS technician_feeds (
        id INT AUTO_INCREMENT PRIMARY KEY,
        technician_id INT NOT NULL,
        technician_name VARCHAR(100) NOT NULL,
        age INT DEFAULT NULL,
        height INT DEFAULT NULL,
        weight INT DEFAULT NULL,
        service_years INT DEFAULT NULL,
        city VARCHAR(100) NOT NULL,
        previous_city VARCHAR(100) DEFAULT NULL,
        action_type ENUM('join', 'move') NOT NULL DEFAULT 'join',
        content TEXT,
        avatar VARCHAR(255) DEFAULT 'images/tx.jpg',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        is_active TINYINT(1) DEFAULT 1,
        INDEX idx_technician_id (technician_id),
        INDEX idx_city (city),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createTableSql);
    
    // 插入新的技师动态
    $insertSql = "INSERT INTO technician_feeds 
                  (technician_id, technician_name, age, height, weight, service_years, 
                   city, previous_city, action_type, content, avatar) 
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $pdo->prepare($insertSql);
    $result = $stmt->execute([
        $technician_id,
        $technician_name,
        $age,
        $height,
        $weight,
        $service_years,
        $city,
        $previous_city,
        $action_type,
        $content,
        $avatar
    ]);
    
    if ($result) {
        $feedId = $pdo->lastInsertId();
        
        // 返回成功结果
        echo json_encode([
            'success' => true,
            'message' => '技师动态添加成功',
            'data' => [
                'feed_id' => $feedId,
                'technician_id' => $technician_id,
                'technician_name' => $technician_name,
                'city' => $city,
                'action_type' => $action_type,
                'content' => $content,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ]);
    } else {
        throw new Exception('添加技师动态失败');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>

<!-- 使用示例：

POST /add_technician_feed.php
Content-Type: application/json

{
    "technician_id": 1,
    "technician_name": "李大宝",
    "age": 22,
    "height": 175,
    "weight": 60,
    "service_years": 16,
    "city": "三亚市",
    "action_type": "join"
}

或者更换城市：

{
    "technician_id": 1,
    "technician_name": "李大宝",
    "age": 22,
    "height": 175,
    "weight": 60,
    "service_years": 16,
    "city": "海口市",
    "previous_city": "三亚市",
    "action_type": "move"
}

-->
