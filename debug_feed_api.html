<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技师动态API调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success {
            background: #28a745;
        }
        .status-error {
            background: #dc3545;
        }
        .status-warning {
            background: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 技师动态API调试</h1>
        
        <div class="section">
            <h3>📋 API状态检查</h3>
            <div id="api-status">
                <div class="info">点击"检查API状态"开始检测</div>
            </div>
            <div style="margin-top: 15px;">
                <button class="btn" onclick="checkAPIStatus()">🔍 检查API状态</button>
                <button class="btn" onclick="testDirectAPI()">🌐 直接测试API</button>
                <button class="btn success" onclick="addTestData()">➕ 添加测试数据</button>
            </div>
        </div>
        
        <div class="section">
            <h3>📊 API响应数据</h3>
            <div id="api-response">
                <div class="info">等待API调用...</div>
            </div>
        </div>
        
        <div class="section">
            <h3>🔧 数据库检查</h3>
            <div id="db-check">
                <div class="info">点击"检查数据库"开始检测</div>
            </div>
            <div style="margin-top: 15px;">
                <button class="btn" onclick="checkDatabase()">🗄️ 检查数据库</button>
                <button class="btn" onclick="checkTables()">📋 检查表结构</button>
            </div>
        </div>
        
        <div class="section">
            <h3>🐛 调试日志</h3>
            <div id="debug-log" class="code-block">
                调试日志将显示在这里...
            </div>
            <div style="margin-top: 15px;">
                <button class="btn" onclick="clearLog()">🗑️ 清空日志</button>
                <button class="btn" onclick="exportLog()">📤 导出日志</button>
            </div>
        </div>
    </div>

    <script>
        let debugLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            debugLog.push(logEntry);
            
            const logContainer = document.getElementById('debug-log');
            logContainer.textContent = debugLog.join('\n');
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(logEntry);
        }
        
        // 检查API状态
        async function checkAPIStatus() {
            log('开始检查API状态...');
            const statusContainer = document.getElementById('api-status');
            
            try {
                log('发送请求到 technician_feed_list.php');
                const response = await fetch('technician_feed_list.php');
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`API响应成功，数据类型: ${typeof data}`);
                    log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                    
                    statusContainer.innerHTML = `
                        <div class="success">
                            <span class="status-indicator status-success"></span>
                            API状态正常<br>
                            • 响应状态: ${response.status}<br>
                            • 数据格式: ${data.success ? '正确' : '错误'}<br>
                            • 动态数量: ${data.feeds ? data.feeds.length : 0}<br>
                            • 分页信息: ${data.pagination ? '有' : '无'}
                        </div>
                    `;
                    
                    document.getElementById('api-response').innerHTML = `
                        <div class="code-block">${JSON.stringify(data, null, 2)}</div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                log(`API检查失败: ${error.message}`, 'error');
                statusContainer.innerHTML = `
                    <div class="error">
                        <span class="status-indicator status-error"></span>
                        API状态异常<br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }
        
        // 直接测试API
        function testDirectAPI() {
            log('打开API直接访问页面');
            window.open('technician_feed_list.php', '_blank');
        }
        
        // 添加测试数据
        async function addTestData() {
            log('开始添加测试数据...');
            
            try {
                const testData = {
                    technician_id: 999,
                    technician_name: '测试技师',
                    age: 25,
                    height: 180,
                    weight: 70,
                    service_years: 20,
                    city: '测试城市',
                    action_type: 'join'
                };
                
                log(`发送测试数据: ${JSON.stringify(testData)}`);
                
                const response = await fetch('add_technician_feed.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                log(`添加测试数据结果: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    alert('测试数据添加成功！');
                    checkAPIStatus(); // 重新检查API
                } else {
                    alert('测试数据添加失败: ' + result.error);
                }
            } catch (error) {
                log(`添加测试数据失败: ${error.message}`, 'error');
                alert('添加测试数据失败: ' + error.message);
            }
        }
        
        // 检查数据库
        async function checkDatabase() {
            log('开始检查数据库连接...');
            const dbContainer = document.getElementById('db-check');
            
            try {
                // 通过API间接检查数据库
                const response = await fetch('technician_feed_list.php');
                const data = await response.json();
                
                if (data.success !== undefined) {
                    log('数据库连接正常');
                    dbContainer.innerHTML = `
                        <div class="success">
                            <span class="status-indicator status-success"></span>
                            数据库连接正常<br>
                            • API可以正常访问数据库<br>
                            • 返回数据格式正确
                        </div>
                    `;
                } else {
                    throw new Error('API返回数据格式异常');
                }
            } catch (error) {
                log(`数据库检查失败: ${error.message}`, 'error');
                dbContainer.innerHTML = `
                    <div class="error">
                        <span class="status-indicator status-error"></span>
                        数据库连接异常<br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }
        
        // 检查表结构
        function checkTables() {
            log('检查表结构...');
            alert('表结构检查功能需要在服务器端实现，请检查 technician_feeds 表是否存在');
        }
        
        // 清空日志
        function clearLog() {
            debugLog = [];
            document.getElementById('debug-log').textContent = '调试日志已清空...';
        }
        
        // 导出日志
        function exportLog() {
            const logText = debugLog.join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'debug_log.txt';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', function() {
            log('调试页面加载完成');
            setTimeout(checkAPIStatus, 1000);
        });
        
        // 模拟feed.html的调用
        function simulateFeedPageCall() {
            log('模拟feed.html页面的API调用...');
            
            fetch('technician_feed_list.php')
                .then(async res => {
                    log(`模拟调用 - 响应状态: ${res.status}`);
                    let data;
                    try {
                        data = await res.json();
                    } catch(e) {
                        log(`模拟调用 - JSON解析失败: ${e.message}`, 'error');
                        try {
                            const txt = await res.text();
                            log(`模拟调用 - 原始响应: ${txt}`);
                            data = JSON.parse(txt);
                        } catch(e2) {
                            log(`模拟调用 - 文本解析也失败: ${e2.message}`, 'error');
                            data = { feeds: [] };
                        }
                    }
                    log(`模拟调用 - 最终数据: ${JSON.stringify(data)}`);
                    
                    // 模拟渲染
                    if (data.feeds && data.feeds.length > 0) {
                        log(`模拟调用 - 将渲染 ${data.feeds.length} 条动态`);
                    } else {
                        log('模拟调用 - 没有动态数据，将显示空状态');
                    }
                })
                .catch((error) => {
                    log(`模拟调用 - 请求失败: ${error.message}`, 'error');
                });
        }
        
        // 添加模拟按钮
        document.addEventListener('DOMContentLoaded', function() {
            const firstSection = document.querySelector('.section');
            const simulateBtn = document.createElement('button');
            simulateBtn.className = 'btn';
            simulateBtn.textContent = '🎭 模拟feed.html调用';
            simulateBtn.onclick = simulateFeedPageCall;
            firstSection.querySelector('div[style*="margin-top"]').appendChild(simulateBtn);
        });
    </script>
</body>
</html>
