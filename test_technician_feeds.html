<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技师动态功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 15px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .btn.warning:hover {
            background: #e0a800;
        }
        .feed-preview {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .feed-item {
            padding: 12px;
            border-bottom: 1px solid #eee;
            margin-bottom: 10px;
        }
        .feed-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .feed-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        .feed-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
        }
        .feed-info {
            flex: 1;
        }
        .feed-name {
            font-weight: bold;
            color: #333;
        }
        .feed-time {
            font-size: 12px;
            color: #999;
        }
        .feed-content {
            margin: 8px 0;
            color: #333;
        }
        .feed-details {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .quick-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏃‍♂️ 技师动态功能测试</h1>
        
        <div class="section">
            <h3>📝 添加技师动态</h3>
            <form id="add-feed-form">
                <div class="form-group">
                    <label>技师姓名</label>
                    <input type="text" id="technician_name" value="李大宝" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>技师ID</label>
                        <input type="number" id="technician_id" value="1" required>
                    </div>
                    <div class="form-group">
                        <label>年龄</label>
                        <input type="number" id="age" value="22">
                    </div>
                    <div class="form-group">
                        <label>身高(cm)</label>
                        <input type="number" id="height" value="175">
                    </div>
                    <div class="form-group">
                        <label>体重(kg)</label>
                        <input type="number" id="weight" value="60">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>服务年限</label>
                        <input type="number" id="service_years" value="16">
                    </div>
                    <div class="form-group">
                        <label>目标城市</label>
                        <select id="city" required>
                            <option value="三亚市">三亚市</option>
                            <option value="海口市">海口市</option>
                            <option value="儋州市">儋州市</option>
                            <option value="琼海市">琼海市</option>
                            <option value="文昌市">文昌市</option>
                            <option value="万宁市">万宁市</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>动作类型</label>
                        <select id="action_type" required onchange="togglePreviousCity()">
                            <option value="join">新加入</option>
                            <option value="move">更换城市</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>原城市</label>
                        <select id="previous_city" disabled>
                            <option value="">无</option>
                            <option value="三亚市">三亚市</option>
                            <option value="海口市">海口市</option>
                            <option value="儋州市">儋州市</option>
                            <option value="琼海市">琼海市</option>
                            <option value="文昌市">文昌市</option>
                            <option value="万宁市">万宁市</option>
                        </select>
                    </div>
                </div>
                
                <div style="margin-top: 20px;">
                    <button type="submit" class="btn success">➕ 添加动态</button>
                    <button type="button" class="btn" onclick="loadFeeds()">🔄 刷新列表</button>
                    <button type="button" class="btn warning" onclick="openFeedPage()">📱 查看动态页面</button>
                </div>
            </form>
            
            <div class="quick-actions">
                <h4 style="width: 100%; margin: 15px 0 10px 0;">快速测试：</h4>
                <button class="btn" onclick="quickAddJoin()">李大宝到三亚</button>
                <button class="btn" onclick="quickAddMove()">李大宝到海口</button>
                <button class="btn" onclick="quickAddNewTech()">王小明到儋州</button>
                <button class="btn" onclick="quickAddMultiple()">批量添加</button>
            </div>
            
            <div id="add-result"></div>
        </div>
        
        <div class="section">
            <h3>📱 技师动态列表</h3>
            <div id="feeds-container" class="feed-preview">
                <div class="loading">点击"刷新列表"加载动态</div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时自动加载动态
        document.addEventListener('DOMContentLoaded', function() {
            loadFeeds();
        });
        
        // 切换原城市字段的启用状态
        function togglePreviousCity() {
            const actionType = document.getElementById('action_type').value;
            const previousCity = document.getElementById('previous_city');
            
            if (actionType === 'move') {
                previousCity.disabled = false;
                previousCity.required = true;
            } else {
                previousCity.disabled = true;
                previousCity.required = false;
                previousCity.value = '';
            }
        }
        
        // 添加技师动态
        document.getElementById('add-feed-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                technician_id: parseInt(document.getElementById('technician_id').value),
                technician_name: document.getElementById('technician_name').value,
                age: parseInt(document.getElementById('age').value) || null,
                height: parseInt(document.getElementById('height').value) || null,
                weight: parseInt(document.getElementById('weight').value) || null,
                service_years: parseInt(document.getElementById('service_years').value) || null,
                city: document.getElementById('city').value,
                action_type: document.getElementById('action_type').value
            };
            
            if (formData.action_type === 'move') {
                formData.previous_city = document.getElementById('previous_city').value;
            }
            
            try {
                const response = await fetch('add_technician_feed.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('add-result').innerHTML = `
                        <div class="success">
                            ✅ 动态添加成功！<br>
                            内容：${result.data.content}<br>
                            时间：${result.data.created_at}
                        </div>
                    `;
                    
                    // 自动刷新动态列表
                    setTimeout(() => {
                        loadFeeds();
                    }, 500);
                } else {
                    document.getElementById('add-result').innerHTML = `
                        <div class="error">❌ 添加失败：${result.error}</div>
                    `;
                }
            } catch (error) {
                document.getElementById('add-result').innerHTML = `
                    <div class="error">❌ 请求失败：${error.message}</div>
                `;
            }
        });
        
        // 加载技师动态列表
        async function loadFeeds() {
            const container = document.getElementById('feeds-container');
            container.innerHTML = '<div class="loading">正在加载动态...</div>';
            
            try {
                const response = await fetch('technician_feed_list.php');
                const data = await response.json();
                
                if (data.success && data.feeds && data.feeds.length > 0) {
                    container.innerHTML = data.feeds.map(feed => `
                        <div class="feed-item">
                            <div class="feed-header">
                                <img src="${feed.avatar}" alt="头像" class="feed-avatar" onerror="this.style.background='#ddd'">
                                <div class="feed-info">
                                    <div class="feed-name">${feed.nickname}</div>
                                    <div class="feed-time">${feed.time} · ${feed.location}</div>
                                </div>
                            </div>
                            <div class="feed-content">${feed.content}</div>
                            ${feed.technician_info ? `
                                <div class="feed-details">
                                    <strong>${feed.technician_info.action_type === 'move' ? '更换城市' : '新加入'}</strong> 
                                    ${feed.technician_info.city}
                                    ${feed.technician_info.previous_city ? ` (从${feed.technician_info.previous_city})` : ''}<br>
                                    年龄:${feed.technician_info.age} | 身高:${feed.technician_info.height}cm | 
                                    体重:${feed.technician_info.weight}kg | 服务年限:${feed.technician_info.service_years}年
                                </div>
                            ` : ''}
                        </div>
                    `).join('');
                } else {
                    container.innerHTML = '<div class="loading">暂无技师动态</div>';
                }
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败：${error.message}</div>`;
            }
        }
        
        // 快速添加 - 李大宝到三亚
        async function quickAddJoin() {
            await addQuickFeed({
                technician_id: 1,
                technician_name: '李大宝',
                age: 22,
                height: 175,
                weight: 60,
                service_years: 16,
                city: '三亚市',
                action_type: 'join'
            });
        }
        
        // 快速添加 - 李大宝到海口
        async function quickAddMove() {
            await addQuickFeed({
                technician_id: 1,
                technician_name: '李大宝',
                age: 22,
                height: 175,
                weight: 60,
                service_years: 16,
                city: '海口市',
                previous_city: '三亚市',
                action_type: 'move'
            });
        }
        
        // 快速添加 - 王小明到儋州
        async function quickAddNewTech() {
            await addQuickFeed({
                technician_id: 2,
                technician_name: '王小明',
                age: 25,
                height: 180,
                weight: 70,
                service_years: 20,
                city: '儋州市',
                action_type: 'join'
            });
        }
        
        // 批量添加多个动态
        async function quickAddMultiple() {
            const feeds = [
                {
                    technician_id: 3,
                    technician_name: '张伟',
                    age: 28,
                    height: 178,
                    weight: 65,
                    service_years: 25,
                    city: '琼海市',
                    action_type: 'join'
                },
                {
                    technician_id: 4,
                    technician_name: '刘强',
                    age: 24,
                    height: 172,
                    weight: 58,
                    service_years: 18,
                    city: '文昌市',
                    action_type: 'join'
                },
                {
                    technician_id: 2,
                    technician_name: '王小明',
                    age: 25,
                    height: 180,
                    weight: 70,
                    service_years: 20,
                    city: '万宁市',
                    previous_city: '儋州市',
                    action_type: 'move'
                }
            ];
            
            for (const feed of feeds) {
                await addQuickFeed(feed);
                await new Promise(resolve => setTimeout(resolve, 500)); // 间隔500ms
            }
        }
        
        // 快速添加动态的通用函数
        async function addQuickFeed(feedData) {
            try {
                const response = await fetch('add_technician_feed.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(feedData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('add-result').innerHTML = `
                        <div class="success">✅ ${result.data.content}</div>
                    `;
                    loadFeeds();
                } else {
                    document.getElementById('add-result').innerHTML = `
                        <div class="error">❌ 添加失败：${result.error}</div>
                    `;
                }
            } catch (error) {
                document.getElementById('add-result').innerHTML = `
                    <div class="error">❌ 请求失败：${error.message}</div>
                `;
            }
        }
        
        // 打开动态页面
        function openFeedPage() {
            window.open('feed.html', '_blank');
        }
    </script>
</body>
</html>
