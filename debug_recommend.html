<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推荐功能调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .enable-btn {
            background: #28a745;
        }
        .enable-btn:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>推荐功能调试页面</h1>
        
        <div class="section">
            <h3>当前推荐设置</h3>
            <button onclick="checkSettings()">检查当前设置</button>
            <div id="settings-result" class="result"></div>
        </div>
        
        <div class="section">
            <h3>启用推荐功能</h3>
            <button class="enable-btn" onclick="enableRecommend()">启用所有推荐功能</button>
            <button onclick="disableRecommend()">禁用推荐功能</button>
            <div id="enable-result" class="result"></div>
        </div>
        
        <div class="section">
            <h3>技师数据检查</h3>
            <button onclick="checkTechData()">检查技师数据</button>
            <div id="tech-result" class="result"></div>
        </div>
        
        <div class="section">
            <h3>推荐算法测试</h3>
            <button onclick="testRecommendAlgorithm()">测试推荐算法</button>
            <div id="algorithm-result" class="result"></div>
        </div>
    </div>

    <script>
        function checkSettings() {
            const resultDiv = document.getElementById('settings-result');
            const settings = {
                recommend_enabled: localStorage.getItem('recommend_enabled'),
                system_recommend_enabled: localStorage.getItem('system_recommend_enabled'),
                manual_recommend_enabled: localStorage.getItem('manual_recommend_enabled')
            };
            
            resultDiv.textContent = '当前localStorage设置:\n' + JSON.stringify(settings, null, 2);
        }
        
        function enableRecommend() {
            localStorage.setItem('recommend_enabled', 'true');
            localStorage.setItem('system_recommend_enabled', 'true');
            localStorage.setItem('manual_recommend_enabled', 'true');
            
            const resultDiv = document.getElementById('enable-result');
            resultDiv.textContent = '推荐功能已启用！\n请刷新首页查看效果。';
            
            checkSettings();
        }
        
        function disableRecommend() {
            localStorage.setItem('recommend_enabled', 'false');
            localStorage.setItem('system_recommend_enabled', 'false');
            localStorage.setItem('manual_recommend_enabled', 'false');
            
            const resultDiv = document.getElementById('enable-result');
            resultDiv.textContent = '推荐功能已禁用！';
            
            checkSettings();
        }
        
        function checkTechData() {
            const resultDiv = document.getElementById('tech-result');
            resultDiv.textContent = '正在获取技师数据...';
            
            fetch('tech_list.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success && Array.isArray(data.data)) {
                        const availableTechs = data.data.filter(tech =>
                            tech.status == 1 && tech.is_resting != 1
                        );
                        
                        const manualRecommended = availableTechs.filter(tech => tech.is_recommended == 1);
                        
                        resultDiv.textContent = `技师数据统计:
总技师数量: ${data.data.length}
可用技师数量: ${availableTechs.length}
人工推荐技师数量: ${manualRecommended.length}

可用技师列表:
${availableTechs.map(tech => `- ${tech.nick || '技师' + tech.id} (ID: ${tech.id}, 推荐: ${tech.is_recommended ? '是' : '否'})`).join('\n')}`;
                    } else {
                        resultDiv.textContent = '获取技师数据失败: ' + JSON.stringify(data, null, 2);
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '错误: ' + error.message;
                });
        }
        
        function testRecommendAlgorithm() {
            const resultDiv = document.getElementById('algorithm-result');
            resultDiv.textContent = '正在测试推荐算法...';
            
            // 模拟推荐设置
            const recommendSettings = {
                enabled: localStorage.getItem('recommend_enabled') === 'true',
                systemEnabled: localStorage.getItem('system_recommend_enabled') === 'true',
                manualEnabled: localStorage.getItem('manual_recommend_enabled') === 'true'
            };
            
            fetch('tech_list.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success && Array.isArray(data.data)) {
                        const allTechs = data.data;
                        
                        // 复制推荐算法逻辑
                        let recommendedTechs = [];
                        
                        const availableTechs = allTechs.filter(tech =>
                            tech.status == 1 && tech.is_resting != 1
                        );
                        
                        if (recommendSettings.manualEnabled) {
                            const manualRecommended = availableTechs.filter(tech => tech.is_recommended == 1);
                            recommendedTechs = recommendedTechs.concat(manualRecommended);
                        }
                        
                        if (recommendSettings.systemEnabled && recommendedTechs.length < 5) {
                            const systemRecommended = availableTechs
                                .filter(tech => tech.is_recommended != 1)
                                .sort((a, b) => (b.virtual_orders || 0) - (a.virtual_orders || 0))
                                .slice(0, 5 - recommendedTechs.length);
                            
                            recommendedTechs = recommendedTechs.concat(systemRecommended);
                        }
                        
                        const finalRecommended = recommendedTechs.slice(0, 5);
                        
                        resultDiv.textContent = `推荐算法测试结果:

推荐设置:
- 总开关: ${recommendSettings.enabled}
- 系统推荐: ${recommendSettings.systemEnabled}
- 人工推荐: ${recommendSettings.manualEnabled}

算法执行结果:
- 可用技师数量: ${availableTechs.length}
- 最终推荐数量: ${finalRecommended.length}

推荐技师列表:
${finalRecommended.map((tech, index) => `${index + 1}. ${tech.nick || '技师' + tech.id} (ID: ${tech.id}, 类型: ${tech.is_recommended == 1 ? '人工推荐' : '系统推荐'})`).join('\n')}`;
                    } else {
                        resultDiv.textContent = '获取技师数据失败';
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '错误: ' + error.message;
                });
        }
        
        // 页面加载时自动检查设置
        window.onload = function() {
            checkSettings();
        };
    </script>
</body>
</html>
