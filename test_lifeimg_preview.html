<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生活照预览问题调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h3 {
            color: #2196F3;
            margin-top: 0;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .preview-area {
            border: 2px dashed #ddd;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            background: #fafafa;
        }
        .preview-area img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            margin: 2px;
            border: 1px solid #ddd;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 生活照预览问题调试</h1>
        
        <div class="section">
            <h3>🐛 问题描述</h3>
            <p>后台编辑技师页面中，生活照预览图看不到。可能的原因：</p>
            <ul>
                <li>数据库中的生活照路径为空或格式错误</li>
                <li>图片文件不存在</li>
                <li>路径解析函数有问题</li>
                <li>CSS样式问题导致图片不可见</li>
            </ul>
        </div>

        <div class="section">
            <h3>🔍 步骤1: 检查技师数据</h3>
            <div class="form-group">
                <label>技师ID (可选，不填则查看所有有生活照的技师):</label>
                <input type="number" id="techId" placeholder="输入技师ID">
                <button class="btn" onclick="checkTechData()">检查数据</button>
            </div>
            <div id="techDataResult" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>🖼️ 步骤2: 测试图片路径解析</h3>
            <div class="form-group">
                <label>图片路径:</label>
                <input type="text" id="imagePath" placeholder="例如: uploads/life_20250725_123456.jpg">
                <button class="btn" onclick="testImagePath()">测试路径</button>
            </div>
            <div class="preview-area" id="imagePreview">
                <p>在上面输入图片路径，然后点击"测试路径"查看预览</p>
            </div>
        </div>

        <div class="section">
            <h3>🧪 步骤3: 模拟编辑页面预览</h3>
            <div class="form-group">
                <label>生活照路径 (多个用逗号分隔):</label>
                <input type="text" id="lifeimgPaths" placeholder="例如: uploads/life1.jpg,uploads/life2.jpg" style="width: 400px;">
                <button class="btn" onclick="simulatePreview()">模拟预览</button>
            </div>
            <div class="preview-area" id="simulateResult">
                <p>输入生活照路径，模拟编辑页面的预览效果</p>
            </div>
        </div>

        <div class="section">
            <h3>🔧 可能的解决方案</h3>
            <div id="solutions" style="display: none;">
                <h4>根据检查结果，建议的解决方案：</h4>
                <ul id="solutionList"></ul>
            </div>
        </div>
    </div>

    <script>
        // 复制 AdminCommon.format.imagePath 函数
        function formatImagePath(path, defaultPath = 'images/tx.jpg') {
            if (!path || !path.trim()) {
                return '../' + defaultPath;
            }
            
            const trimmedPath = path.trim();
            if (trimmedPath.startsWith('images/')) {
                return '../' + trimmedPath;
            } else if (trimmedPath.startsWith('/w7/images/')) {
                return '..' + trimmedPath.substring(3);
            } else if (trimmedPath.startsWith('/images/')) {
                return '..' + trimmedPath;
            } else if (trimmedPath.startsWith('uploads/')) {
                return '../' + trimmedPath;
            } else if (trimmedPath.startsWith('/w7/uploads/')) {
                return '..' + trimmedPath.substring(3);
            } else if (trimmedPath.startsWith('/uploads/')) {
                return '..' + trimmedPath;
            } else {
                return '../images/' + trimmedPath;
            }
        }

        function checkTechData() {
            const techId = document.getElementById('techId').value;
            const resultDiv = document.getElementById('techDataResult');
            
            resultDiv.style.display = 'block';
            resultDiv.textContent = '检查中...';
            
            const url = techId ? `admin/debug_tech_data.php?id=${techId}` : 'admin/debug_tech_data.php';
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    resultDiv.textContent = JSON.stringify(data, null, 2);
                    analyzeTechData(data);
                })
                .catch(error => {
                    resultDiv.textContent = '错误: ' + error.message;
                });
        }

        function testImagePath() {
            const path = document.getElementById('imagePath').value;
            const previewDiv = document.getElementById('imagePreview');
            
            if (!path) {
                previewDiv.innerHTML = '<p style="color: red;">请输入图片路径</p>';
                return;
            }
            
            const formattedPath = formatImagePath(path);
            
            previewDiv.innerHTML = `
                <p><strong>原始路径:</strong> ${path}</p>
                <p><strong>格式化后:</strong> ${formattedPath}</p>
                <p><strong>预览:</strong></p>
                <img src="${formattedPath}" style="width: 100px; height: 100px; object-fit: cover; border: 1px solid #ddd;" 
                     onerror="this.style.border='2px solid red'; this.alt='图片加载失败';" 
                     onload="this.style.border='2px solid green';">
            `;
        }

        function simulatePreview() {
            const paths = document.getElementById('lifeimgPaths').value;
            const resultDiv = document.getElementById('simulateResult');
            
            if (!paths) {
                resultDiv.innerHTML = '<p style="color: red;">请输入生活照路径</p>';
                return;
            }
            
            const pathArray = paths.split(',').filter(img => img.trim());
            
            if (pathArray.length === 0) {
                resultDiv.innerHTML = '<p style="color: red;">没有有效的路径</p>';
                return;
            }
            
            let html = '<p><strong>模拟编辑页面预览效果:</strong></p>';
            html += '<div style="margin-bottom: 10px;">';
            
            pathArray.forEach(img => {
                const trimmedImg = img.trim();
                const formattedPath = formatImagePath(trimmedImg);
                html += `<img src="${formattedPath}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px; margin: 2px; border: 1px solid #ddd;" 
                         onerror="this.style.border='2px solid red'; this.title='加载失败: ${formattedPath}';" 
                         onload="this.style.border='1px solid green'; this.title='加载成功: ${formattedPath}';">`;
            });
            
            html += '</div>';
            html += `<p><strong>路径数量:</strong> ${pathArray.length}</p>`;
            html += '<p><strong>路径详情:</strong></p><ul>';
            
            pathArray.forEach((img, index) => {
                const trimmedImg = img.trim();
                const formattedPath = formatImagePath(trimmedImg);
                html += `<li>${index + 1}. ${trimmedImg} → ${formattedPath}</li>`;
            });
            
            html += '</ul>';
            resultDiv.innerHTML = html;
        }

        function analyzeTechData(data) {
            const solutions = document.getElementById('solutions');
            const solutionList = document.getElementById('solutionList');
            
            solutions.style.display = 'block';
            solutionList.innerHTML = '';

            if (!data.success) {
                solutionList.innerHTML += '<li style="color: red;">数据查询失败，请检查数据库连接</li>';
                return;
            }

            if (data.tech) {
                // 单个技师分析
                if (!data.tech.lifeimg || data.tech.lifeimg.trim() === '') {
                    solutionList.innerHTML += '<li>该技师没有生活照数据，需要先上传生活照</li>';
                } else if (data.lifeimg_analysis) {
                    let hasValidImages = false;
                    data.lifeimg_analysis.forEach(analysis => {
                        if (analysis.file_exists) {
                            hasValidImages = true;
                        } else {
                            solutionList.innerHTML += `<li style="color: red;">图片文件不存在: ${analysis.full_path}</li>`;
                        }
                    });
                    
                    if (hasValidImages) {
                        solutionList.innerHTML += '<li style="color: green;">找到有效的图片文件，问题可能在前端显示逻辑</li>';
                    }
                }
            } else if (data.analysis) {
                // 多个技师分析
                let totalTechs = data.analysis.length;
                let techsWithValidImages = 0;
                
                data.analysis.forEach(tech => {
                    let hasValid = tech.lifeimg_paths.some(path => path.exists);
                    if (hasValid) techsWithValidImages++;
                });
                
                solutionList.innerHTML += `<li>共找到 ${totalTechs} 个有生活照记录的技师</li>`;
                solutionList.innerHTML += `<li>其中 ${techsWithValidImages} 个技师的图片文件存在</li>`;
                
                if (techsWithValidImages === 0) {
                    solutionList.innerHTML += '<li style="color: red;">所有技师的生活照文件都不存在，需要重新上传</li>';
                } else if (techsWithValidImages < totalTechs) {
                    solutionList.innerHTML += '<li style="color: orange;">部分技师的图片文件丢失</li>';
                } else {
                    solutionList.innerHTML += '<li style="color: green;">图片文件都存在，问题在前端显示</li>';
                }
            }

            // 通用建议
            solutionList.innerHTML += '<li>检查浏览器开发者工具的网络面板，查看图片请求是否成功</li>';
            solutionList.innerHTML += '<li>检查浏览器控制台是否有JavaScript错误</li>';
            solutionList.innerHTML += '<li>尝试直接访问图片URL确认文件是否存在</li>';
        }
    </script>
</body>
</html>
