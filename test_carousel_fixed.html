<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复后的轮播图测试</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 400px;
            margin: 20px auto;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        /* 复制feed.html的轮播图样式 */
        .carousel-container {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
            border-radius: 0 0 18px 18px;
            background: #f5f5f5;
        }
        
        .carousel-wrapper {
            display: flex;
            width: 100%;
            height: 100%;
            transition: transform 0.5s ease-in-out;
        }
        
        .carousel-slide {
            min-width: 100%;
            width: 100%;
            height: 100%;
            position: relative;
            flex-shrink: 0;
        }
        
        .carousel-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
            border: none;
            outline: none;
        }
        
        .carousel-indicators {
            position: absolute;
            bottom: 12px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
            z-index: 10;
        }
        
        .carousel-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .carousel-indicator.active {
            background: rgba(255, 255, 255, 0.9);
            transform: scale(1.2);
        }
        
        .carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.3);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10;
        }
        
        .carousel-nav:hover {
            background: rgba(0, 0, 0, 0.6);
            transform: translateY(-50%) scale(1.1);
        }
        
        .carousel-nav.prev {
            left: 12px;
        }
        
        .carousel-nav.next {
            right: 12px;
        }
        
        .carousel-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            background: #f5f5f5;
            color: #999;
            font-size: 14px;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .controls {
            padding: 20px;
            text-align: center;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.success:hover {
            background: #1e7e34;
        }
        
        .info {
            padding: 15px;
            background: #e3f2fd;
            margin: 10px;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .status {
            padding: 10px;
            margin: 10px;
            border-radius: 4px;
            font-weight: bold;
            text-align: center;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 style="text-align: center; margin: 20px 0; color: #333;">🎠 轮播图显示测试</h2>
        
        <div id="status" class="status loading">正在加载轮播图...</div>
        
        <!-- 轮播图容器 -->
        <div id="carousel" class="carousel-container">
            <div class="carousel-loading">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div style="width: 16px; height: 16px; border: 2px solid #ddd; border-top: 2px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                    正在加载轮播图...
                </div>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="loadCarousel()">🔄 重新加载</button>
            <button class="btn" onclick="prevSlide()">⬅️ 上一张</button>
            <button class="btn" onclick="nextSlide()">➡️ 下一张</button>
            <button class="btn success" onclick="toggleAutoPlay()">⏸️ 暂停/播放</button>
        </div>
        
        <div class="info">
            <strong>测试说明：</strong><br>
            • 轮播图高度：200px<br>
            • 图片适配：cover模式，完整显示<br>
            • 自动播放：4秒切换一次<br>
            • 手动控制：支持按钮和指示器切换<br>
            • 错误处理：加载失败时显示默认图片
        </div>
        
        <div id="debug-info" style="padding: 10px; background: #f8f9fa; margin: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; display: none;"></div>
    </div>

    <script>
        let currentSlideIndex = 0;
        let totalSlides = 0;
        let autoPlayInterval = null;
        let isAutoPlaying = true;
        
        // 加载轮播图
        async function loadCarousel() {
            updateStatus('正在加载轮播图...', 'loading');
            
            try {
                const response = await fetch('banner_list.php');
                const data = await response.json();
                
                console.log('API响应:', data);
                
                if (data.success && data.data && data.data.length > 0) {
                    renderCarousel(data.data);
                    updateStatus(`轮播图加载成功，共 ${data.data.length} 张`, 'success');
                } else {
                    showEmptyState();
                    updateStatus('没有轮播图数据', 'error');
                }
            } catch (error) {
                console.error('加载失败:', error);
                showErrorState();
                updateStatus(`加载失败: ${error.message}`, 'error');
            }
        }
        
        // 渲染轮播图
        function renderCarousel(slides) {
            const carousel = document.getElementById('carousel');
            totalSlides = slides.length;
            currentSlideIndex = 0;
            
            const html = `
                <div class="carousel-wrapper" style="display: flex; width: 100%; height: 100%;">
                    ${slides.map((slide, index) => `
                        <div class="carousel-slide" style="min-width: 100%; height: 100%; flex-shrink: 0;">
                            <img src="${slide.img}" alt="轮播图${index + 1}" 
                                 style="width: 100%; height: 100%; object-fit: cover; display: block;"
                                 onerror="this.src='images/lbt.png'"
                                 onload="console.log('图片${index + 1}加载成功:', '${slide.img}')" />
                        </div>
                    `).join('')}
                </div>
                
                ${slides.length > 1 ? `
                    <button class="carousel-nav prev" onclick="prevSlide()">‹</button>
                    <button class="carousel-nav next" onclick="nextSlide()">›</button>
                    
                    <div class="carousel-indicators">
                        ${slides.map((_, index) => `
                            <div class="carousel-indicator ${index === 0 ? 'active' : ''}"
                                 onclick="goToSlide(${index})"></div>
                        `).join('')}
                    </div>
                ` : ''}
            `;
            
            carousel.innerHTML = html;
            
            if (slides.length > 1) {
                startAutoPlay();
            }
            
            updateDebugInfo(slides);
        }
        
        // 显示空状态
        function showEmptyState() {
            const carousel = document.getElementById('carousel');
            carousel.innerHTML = `
                <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 200px; color: #999;">
                    <div style="font-size: 48px; margin-bottom: 12px;">🖼️</div>
                    <div>暂无轮播图</div>
                </div>
            `;
        }
        
        // 显示错误状态
        function showErrorState() {
            const carousel = document.getElementById('carousel');
            carousel.innerHTML = `
                <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 200px; color: #999;">
                    <div style="font-size: 48px; margin-bottom: 12px;">⚠️</div>
                    <div>轮播图加载失败</div>
                    <button onclick="loadCarousel()" style="margin-top: 12px; padding: 6px 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">重新加载</button>
                </div>
            `;
        }
        
        // 切换到指定幻灯片
        function goToSlide(index) {
            if (index < 0 || index >= totalSlides) return;
            
            currentSlideIndex = index;
            updateSlidePosition();
            updateIndicators();
            
            // 重启自动播放
            if (isAutoPlaying) {
                startAutoPlay();
            }
        }
        
        // 下一张
        function nextSlide() {
            if (totalSlides <= 1) return;
            const nextIndex = (currentSlideIndex + 1) % totalSlides;
            goToSlide(nextIndex);
        }
        
        // 上一张
        function prevSlide() {
            if (totalSlides <= 1) return;
            const prevIndex = (currentSlideIndex - 1 + totalSlides) % totalSlides;
            goToSlide(prevIndex);
        }
        
        // 更新幻灯片位置
        function updateSlidePosition() {
            const wrapper = document.querySelector('.carousel-wrapper');
            if (wrapper) {
                const translateX = -currentSlideIndex * 100;
                wrapper.style.transform = `translateX(${translateX}%)`;
            }
        }
        
        // 更新指示器
        function updateIndicators() {
            const indicators = document.querySelectorAll('.carousel-indicator');
            indicators.forEach((indicator, index) => {
                indicator.classList.toggle('active', index === currentSlideIndex);
            });
        }
        
        // 开始自动播放
        function startAutoPlay() {
            if (totalSlides <= 1) return;
            
            stopAutoPlay();
            autoPlayInterval = setInterval(() => {
                nextSlide();
            }, 4000);
        }
        
        // 停止自动播放
        function stopAutoPlay() {
            if (autoPlayInterval) {
                clearInterval(autoPlayInterval);
                autoPlayInterval = null;
            }
        }
        
        // 切换自动播放
        function toggleAutoPlay() {
            isAutoPlaying = !isAutoPlaying;
            
            if (isAutoPlaying) {
                startAutoPlay();
                updateStatus('自动播放已开启', 'success');
            } else {
                stopAutoPlay();
                updateStatus('自动播放已暂停', 'loading');
            }
        }
        
        // 更新状态
        function updateStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // 更新调试信息
        function updateDebugInfo(slides) {
            const debugInfo = document.getElementById('debug-info');
            debugInfo.style.display = 'block';
            debugInfo.innerHTML = `
调试信息:
• 轮播图数量: ${slides.length}
• 当前显示: 第 ${currentSlideIndex + 1} 张
• 自动播放: ${isAutoPlaying ? '开启' : '关闭'}
• 容器尺寸: ${document.getElementById('carousel').offsetWidth}x${document.getElementById('carousel').offsetHeight}px

图片列表:
${slides.map((slide, index) => `${index + 1}. ${slide.img}`).join('\n')}
            `;
        }
        
        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            loadCarousel();
        });
    </script>
</body>
</html>
