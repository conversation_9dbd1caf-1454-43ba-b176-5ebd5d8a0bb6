<?php
// create_city_table.php - 创建城市表
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

try {
    // 检查city表是否已存在
    $checkTable = $pdo->query("SHOW TABLES LIKE 'city'");
    if ($checkTable->fetch()) {
        echo json_encode([
            'success' => true,
            'msg' => 'city表已存在',
            'action' => 'table_exists'
        ]);
        exit;
    }
    
    // 创建city表
    $createTableSQL = "
        CREATE TABLE `city` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL COMMENT '城市名称',
            `lng` decimal(10,6) DEFAULT NULL COMMENT '经度',
            `lat` decimal(10,6) DEFAULT NULL COMMENT '纬度',
            `is_hot` tinyint(1) DEFAULT 0 COMMENT '是否热门城市(1=热门,0=普通)',
            `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `name` (`name`),
            KEY `is_hot` (`is_hot`),
            KEY `sort_order` (`sort_order`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='城市表'
    ";
    
    $pdo->exec($createTableSQL);
    
    // 插入一些基础城市数据
    $basicCities = [
        ['北京市', 116.4074, 39.9042, 1],
        ['上海市', 121.4737, 31.2304, 1],
        ['广州市', 113.2644, 23.1291, 1],
        ['深圳市', 114.0579, 22.5431, 1],
        ['杭州市', 120.1551, 30.2741, 1],
        ['南京市', 118.7969, 32.0603, 1],
        ['武汉市', 114.2985, 30.5844, 1],
        ['成都市', 104.0665, 30.5723, 1],
        ['西安市', 108.9398, 34.3416, 1],
        ['重庆市', 106.5516, 29.5630, 1],
        ['天津市', 117.1901, 39.1084, 1],
        ['苏州市', 120.6519, 31.3989, 0],
        ['无锡市', 120.3019, 31.5747, 0],
        ['宁波市', 121.5440, 29.8683, 0],
        ['青岛市', 120.3826, 36.0671, 0],
        ['大连市', 121.6147, 38.9140, 0],
        ['厦门市', 118.0894, 24.4798, 0],
        ['长沙市', 112.9388, 28.2282, 0],
        ['郑州市', 113.6254, 34.7466, 0],
        ['济南市', 117.0009, 36.6758, 0]
    ];
    
    $insertStmt = $pdo->prepare("INSERT INTO city (name, lng, lat, is_hot) VALUES (?, ?, ?, ?)");
    $insertedCount = 0;
    
    foreach ($basicCities as $cityData) {
        if ($insertStmt->execute($cityData)) {
            $insertedCount++;
        }
    }
    
    echo json_encode([
        'success' => true,
        'msg' => 'city表创建成功',
        'data' => [
            'table_created' => true,
            'cities_inserted' => $insertedCount,
            'total_cities' => count($basicCities)
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'msg' => '创建city表失败: ' . $e->getMessage()
    ]);
}
?>
