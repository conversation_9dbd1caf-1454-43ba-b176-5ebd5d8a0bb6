<?php
// get_amap_key.php
error_reporting(E_ALL);
ini_set('display_errors', 1);
$keyFile = __DIR__ . '/amap_key.txt';
if (!file_exists($keyFile)) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['amap_key'=>'', 'error'=>'amap_key.txt 文件不存在']);
    exit;
}
$key = trim(file_get_contents($keyFile));
if ($key === '') {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['amap_key'=>'', 'error'=>'amap_key.txt 内容为空']);
    exit;
}
header('Content-Type: application/json; charset=utf-8');
echo json_encode(['amap_key'=>$key]);
