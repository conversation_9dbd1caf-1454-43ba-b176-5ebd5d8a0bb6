<?php
// banner_edit.php - 编辑轮播图
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

$bannerFile = __DIR__ . '/banner.json';
$uploadDir = __DIR__ . '/../uploads/';

// 确保上传目录存在
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

try {
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $link = isset($_POST['link']) ? trim($_POST['link']) : '';
    
    if ($id <= 0) {
        echo json_encode(['success' => false, 'msg' => '无效的轮播图ID']);
        exit;
    }

    // 读取现有轮播图数据
    if (!file_exists($bannerFile)) {
        echo json_encode(['success' => false, 'msg' => '轮播图数据文件不存在']);
        exit;
    }

    $banners = json_decode(file_get_contents($bannerFile), true);
    if ($banners === null) {
        echo json_encode(['success' => false, 'msg' => '轮播图数据格式错误']);
        exit;
    }

    // 查找要编辑的轮播图
    $targetIndex = -1;
    $targetBanner = null;
    
    foreach ($banners as $index => $banner) {
        if ($banner['id'] == $id) {
            $targetIndex = $index;
            $targetBanner = $banner;
            break;
        }
    }

    if ($targetIndex === -1) {
        echo json_encode(['success' => false, 'msg' => '轮播图不存在']);
        exit;
    }

    // 处理图片上传（如果有新图片）
    $newImagePath = null;
    if (isset($_FILES['banner']) && $_FILES['banner']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['banner'];
        
        // 验证文件类型
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!in_array($file['type'], $allowedTypes)) {
            echo json_encode(['success' => false, 'msg' => '只支持 JPG、PNG、GIF 格式的图片']);
            exit;
        }

        // 验证文件大小 (5MB)
        if ($file['size'] > 5 * 1024 * 1024) {
            echo json_encode(['success' => false, 'msg' => '图片大小不能超过5MB']);
            exit;
        }

        // 生成唯一文件名
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'banner_' . date('Ymd_His') . '_' . rand(1000, 9999) . '.' . $extension;
        $filepath = $uploadDir . $filename;

        // 移动上传的文件
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            echo json_encode(['success' => false, 'msg' => '文件上传失败']);
            exit;
        }

        $newImagePath = 'uploads/' . $filename;
        
        // 删除旧图片文件
        $oldImagePath = $targetBanner['img'];
        if ($oldImagePath) {
            // 处理新旧两种路径格式
            if (strpos($oldImagePath, '/w7/') === 0) {
                // 旧格式：/w7/uploads/xxx.jpg
                $oldFilePath = __DIR__ . '/../' . ltrim($oldImagePath, '/w7/');
            } else {
                // 新格式：uploads/xxx.jpg
                $oldFilePath = __DIR__ . '/../' . $oldImagePath;
            }
            if (file_exists($oldFilePath)) {
                unlink($oldFilePath);
            }
        }
    }

    // 更新轮播图数据
    $banners[$targetIndex]['link'] = $link;
    if ($newImagePath) {
        $banners[$targetIndex]['img'] = $newImagePath;
    }

    // 保存到文件
    if (file_put_contents($bannerFile, json_encode($banners, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES)) === false) {
        // 如果保存失败且上传了新图片，删除新上传的文件
        if ($newImagePath && file_exists($uploadDir . basename($newImagePath))) {
            unlink($uploadDir . basename($newImagePath));
        }
        echo json_encode(['success' => false, 'msg' => '保存轮播图数据失败']);
        exit;
    }

    $responseMsg = $newImagePath ? '轮播图更新成功（图片已更换）' : '轮播图链接更新成功';
    echo json_encode([
        'success' => true, 
        'msg' => $responseMsg,
        'data' => $banners[$targetIndex]
    ]);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'msg' => '更新失败: ' . $e->getMessage()]);
}
?>
