<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技师列表</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 顶部导航 */
        .top-nav {
            background: linear-gradient(135deg, rgba(255, 154, 158, 0.9) 0%, rgba(254, 207, 239, 0.9) 100%);
            backdrop-filter: blur(20px);
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            position: relative;
            box-shadow: 0 4px 20px rgba(255, 154, 158, 0.3);
        }

        .page-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            font-size: 20px;
            font-weight: bold;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .city-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 18px;
            font-weight: 500;
        }

        .location-icon {
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #88d8a3;
            font-size: 12px;
        }



        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* 搜索栏 */
        .search-container {
            padding: 20px;
            margin-bottom: 20px;
        }

        .search-box {
            background: white;
            border-radius: 25px;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .search-icon {
            color: #999;
            font-size: 18px;
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            color: #333;
        }

        .search-input::placeholder {
            color: #999;
        }

        /* 技师列表 */
        .technician-list {
            padding: 0 20px 20px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .technician-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 8px 30px rgba(168, 237, 234, 0.3);
            display: flex;
            gap: 16px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .technician-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(168, 237, 234, 0.4);
            background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.95) 100%);
        }

        .tech-avatar {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            flex-shrink: 0;
        }

        .tech-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .tech-badge {
            position: absolute;
            bottom: -2px;
            left: -2px;
            background: #ff6b35;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: bold;
        }

        .tech-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .tech-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .tech-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .tech-distance {
            color: #999;
            font-size: 14px;
        }

        .tech-info-details {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 8px;
        }

        .tech-detail-item {
            display: flex;
            align-items: center;
            gap: 4px;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 12px;
        }

        .detail-label {
            color: #666;
            font-weight: 500;
        }

        .detail-value {
            color: #333;
            font-weight: bold;
        }

        .tech-tags {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .tech-tag {
            background: #f0f0f0;
            color: #666;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .tech-orders-tag {
            background: #e8f5e8;
            color: #2e7d32;
            font-weight: 500;
        }

        .tech-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .tech-status {
            display: flex;
            align-items: center;
        }

        .status-available {
            background: #a8e6cf;
            color: #2e7d32;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }



        .service-btn {
            background: #333;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .service-btn:hover {
            background: #555;
        }

        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 40px;
            color: white;
            font-size: 16px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: white;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        /* 推荐标识 */
        .recommended {
            border: 2px solid #ffa726;
            position: relative;
        }

        .recommended::before {
            content: "推荐";
            position: absolute;
            top: -1px;
            right: -1px;
            background: #ffa726;
            color: white;
            font-size: 10px;
            padding: 2px 8px;
            border-radius: 0 14px 0 8px;
            font-weight: bold;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 8px 0 20px 0;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            color: #888;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 8px;
        }

        .nav-item.active {
            color: #00c6a2;
        }

        .nav-item.active .nav-icon svg {
            stroke: #00c6a2;
        }

        .nav-item.center {
            position: relative;
            top: -10px;
        }

        .nav-plus {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 技师详情模态框样式 */
        .tech-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            padding: 20px;
        }

        .tech-modal-content {
            background: white;
            border-radius: 16px;
            max-width: 600px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }

        .tech-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid #eee;
            position: sticky;
            top: 0;
            background: white;
            border-radius: 16px 16px 0 0;
        }

        .tech-modal-header h2 {
            margin: 0;
            color: #333;
            font-size: 20px;
        }

        .tech-modal-close {
            background: none;
            border: none;
            font-size: 28px;
            color: #999;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .tech-modal-close:hover {
            background: #f5f5f5;
            color: #333;
        }

        .tech-modal-body {
            padding: 24px;
        }

        .tech-info-section {
            margin-bottom: 32px;
        }

        .tech-info-section h3 {
            margin: 0 0 16px 0;
            color: #333;
            font-size: 18px;
            font-weight: 600;
            border-bottom: 2px solid #a8e6cf;
            padding-bottom: 8px;
        }

        .tech-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .info-label {
            color: #666;
            font-weight: 500;
            min-width: 60px;
        }

        .info-value {
            color: #333;
            font-weight: 600;
        }

        .recommended-tag {
            background: #ff6b35;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .tech-images {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 12px;
        }

        .tech-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .tech-image:hover {
            transform: scale(1.05);
            border-color: #a8e6cf;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .tech-video {
            display: flex;
            justify-content: center;
        }

        .tech-video-player {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            cursor: pointer;
        }

        .tech-modal-footer {
            padding: 20px 24px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: center;
            position: sticky;
            bottom: 0;
            background: white;
            border-radius: 0 0 16px 16px;
        }

        .tech-contact-btn {
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
            color: #2e7d32;
            border: none;
            padding: 12px 32px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tech-contact-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(168, 230, 207, 0.4);
        }

        /* 图片放大模态框样式 */
        .image-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 3000;
            padding: 20px;
        }

        .image-modal-content {
            position: relative;
            max-width: 90vw;
            max-height: 90vh;
        }

        .image-modal-close {
            position: absolute;
            top: -40px;
            right: 0;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            font-size: 24px;
            color: #333;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .image-modal-close:hover {
            background: white;
        }

        .image-modal-img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        /* 视频放大模态框样式 */
        .video-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 3000;
            padding: 20px;
        }

        .video-modal-content {
            position: relative;
            max-width: 90vw;
            max-height: 90vh;
        }

        .video-modal-close {
            position: absolute;
            top: -40px;
            right: 0;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            font-size: 24px;
            color: #333;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .video-modal-close:hover {
            background: white;
        }

        .video-modal-player {
            max-width: 100%;
            max-height: 100%;
            border-radius: 8px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .tech-modal-content {
                margin: 10px;
                max-height: 95vh;
            }

            .tech-info-grid {
                grid-template-columns: 1fr;
            }

            .tech-images {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            }

            .tech-image {
                height: 100px;
            }
        }

        /* 技师页公告样式 */
        .tech-announcement {
            margin: 16px 20px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px 16px;
            overflow: hidden;
        }

        .announcement-content {
            color: #856404;
            font-size: 14px;
            line-height: 1.5;
        }

        .announcement-content.scrolling {
            animation: scroll-text 15s linear infinite;
            white-space: nowrap;
        }

        @keyframes scroll-text {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="top-nav">
        <div style="display: flex; align-items: center; position: relative;">
            <div class="city-selector" onclick="showCitySelector()">
                <div class="location-icon">📍</div>
                <span id="current-city">三亚市</span>
                <span>▼</span>
            </div>
            <div id="city-tip" style="margin-left: 10px; position: relative; display: flex; align-items: center;">
                <span style="font-size: 14px; color: #22c55e; font-weight: 500; background: #e0ffe0; border-radius: 8px; padding: 2px 10px; margin-right: 4px; display: flex; align-items: center;">
                  <svg width="28" height="28" viewBox="0 0 32 32" style="margin-right: 6px; vertical-align: middle;" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 16L24 6V26L4 16Z" fill="#22c55e"/>
                  </svg>
  在这里选择你所在的城市
</span>
                </span>
            </div>
        </div>

        <div class="page-title">技师列表</div>

    </div>

    <!-- 搜索栏 -->
    <div class="search-container">
        <div class="search-box">
            <div class="search-icon">🔍</div>
            <input type="text" class="search-input" placeholder="请输入技师姓名" id="search-input">
        </div>
    </div>

    <!-- 技师页公告区域 -->
    <div class="tech-announcement" id="tech-announcement" style="display: none;">
        <div class="announcement-content" id="tech-announcement-content">
            <!-- 公告内容将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 技师列表 -->
    <div class="technician-list" id="technician-list" style="padding-bottom: 100px;">
        <div class="loading">正在加载技师列表...</div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item" id="nav-home" onclick="window.location.href='index.html'">
            <span class="nav-icon">
                <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><path d="M3 10.5L12 4l9 6.5V20a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V10.5z" stroke="#888" stroke-width="2"/></svg>
            </span>
            首页
        </div>
        <div class="nav-item active" id="nav-technicians">
            <span class="nav-icon">
                <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><rect x="4" y="4" width="16" height="16" rx="4" stroke="#00c6a2" stroke-width="2"/><path d="M8 8h8v8H8V8z" stroke="#00c6a2" stroke-width="2"/></svg>
            </span>
            技师
        </div>
        <div class="nav-item center">
            <span class="nav-plus">
                <svg width="38" height="38" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="12" r="10" fill="#00c6a2"/><path d="M12 8v8M8 12h8" stroke="#fff" stroke-width="2"/></svg>
            </span>
        </div>
        <div class="nav-item" id="nav-feed" onclick="window.location.href='feed.html'">
            <span class="nav-icon">
                <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g stroke="#888" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M14 14L22 7.5C20.5 5.5 17.5 5.5 16 7.5C14.5 9.5 14 14 14 14Z"/>
                        <path d="M14 14L6 7.5C7.5 5.5 10.5 5.5 12 7.5C13.5 9.5 14 14 14 14Z"/>
                        <path d="M14 14L6 20.5C7.5 22.5 10.5 22.5 12 20.5C13.5 18.5 14 14 14 14Z"/>
                        <path d="M14 14L22 20.5C20.5 22.5 17.5 22.5 16 20.5C14.5 18.5 14 14 14 14Z"/>
                        <circle cx="14" cy="14" r="2.5" fill="#888" stroke="none"/>
                    </g>
                </svg>
            </span>
            动态
        </div>
        <div class="nav-item" id="nav-mine" onclick="window.location.href='profile.html'">
            <span class="nav-icon">
                <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="8" r="4" stroke="#888" stroke-width="2"/><path d="M4 20c0-2.2 3.6-4 8-4s8 1.8 8 4" stroke="#888" stroke-width="2"/></svg>
            </span>
            我的
        </div>
    </div>

    <script>
        let allTechnicians = [];
        let filteredTechnicians = [];
        let availableCities = [];

        // 页面加载完成后获取技师列表
        document.addEventListener('DOMContentLoaded', function() {
            loadTechnicians();
            loadCities();

            // 搜索功能
            document.getElementById('search-input').addEventListener('input', function(e) {
                const keyword = e.target.value.trim().toLowerCase();
                filterTechnicians(keyword);
            });

            // 更新当前城市显示
            updateCurrentCity();
        });

        // 加载城市列表
        function loadCities() {
            fetch('admin/city_list.php')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text(); // 先获取原始文本
            })
            .then(text => {
                console.log('城市列表原始响应:', text.substring(0, 200));

                let data;
                try {
                    data = JSON.parse(text);
                } catch (jsonError) {
                    console.error('城市列表JSON解析失败:', jsonError.message);
                    console.error('原始响应:', text);
                    throw new Error('JSON解析失败');
                }
                console.log('城市列表API响应:', data);

                // city_list.php 返回已按拼音排序的城市数组
                if (Array.isArray(data) && data.length > 0) {
                    // 城市数据已经在后台按拼音排序，直接使用
                    // 提取城市名称，保持原有排序
                    const cityNames = data
                        .map(city => city.name)
                        .filter(name => name && name.trim())
                        .map(name => name.trim());

                    // 去重但保持排序（后台已按热门+拼音排序）
                    const uniqueCities = [];
                    const seen = new Set();
                    cityNames.forEach(city => {
                        if (!seen.has(city)) {
                            seen.add(city);
                            uniqueCities.push(city);
                        }
                    });

                    availableCities = uniqueCities;

                    // 添加"全部城市"选项到开头
                    availableCities.unshift('全部城市');

                    console.log('可用城市列表(按拼音A-Z排序):', availableCities);
                } else {
                    console.warn('获取城市列表失败或为空，使用默认城市');
                    availableCities = ['全部城市', '三亚市', '海口市', '儋州市', '琼海市', '文昌市', '万宁市'];
                }
            })
            .catch(error => {
                console.error('加载城市列表失败:', error);
                availableCities = ['全部城市', '三亚市', '海口市', '儋州市', '琼海市', '文昌市', '万宁市'];
            });
        }

        // 更新当前城市显示
        function updateCurrentCity() {
            // 从localStorage获取用户选择的城市
            const savedCity = localStorage.getItem('selectedCity');
            if (savedCity && availableCities.includes(savedCity)) {
                document.getElementById('current-city').textContent = savedCity;
                // 应用城市筛选
                filterTechniciansByCity(savedCity);
            } else {
                // 默认显示"全部城市"
                document.getElementById('current-city').textContent = '全部城市';
                localStorage.setItem('selectedCity', '全部城市');
                // 显示所有技师
                filteredTechnicians = [...allTechnicians];
            }
        }

        // 加载技师列表
        function loadTechnicians() {
            fetch('tech_list.php')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text(); // 先获取原始文本
            })
            .then(text => {
                console.log('技师列表原始响应:', text.substring(0, 200));

                let data;
                try {
                    data = JSON.parse(text);
                } catch (jsonError) {
                    console.error('技师列表JSON解析失败:', jsonError.message);
                    console.error('原始响应:', text);
                    throw new Error('JSON解析失败');
                }
                console.log('技师列表API响应:', data);
                
                if (data.success && Array.isArray(data.data)) {
                    // 只显示已通过审核且非休息状态的技师
                    allTechnicians = data.data.filter(tech =>
                        tech.status == 1 && tech.is_resting != 1
                    );

                    console.log('获取到技师数量:', allTechnicians.length);
                    console.log('过滤条件: status=1 且 is_resting!=1');
                    // 按推荐状态和虚拟单量排序
                    allTechnicians.sort((a, b) => {
                        if (a.is_recommended !== b.is_recommended) {
                            return b.is_recommended - a.is_recommended; // 推荐的在前
                        }
                        return (b.virtual_orders || 0) - (a.virtual_orders || 0); // 虚拟单量高的在前
                    });
                    
                    filteredTechnicians = [...allTechnicians];
                    renderTechnicians();

                    // 技师加载完成后更新城市显示
                    updateCurrentCity();
                } else {
                    showError('获取技师列表失败');
                }
            })
            .catch(error => {
                console.error('加载技师列表失败:', error);
                showError('网络错误，请稍后再试');
            });
        }

        // 渲染技师列表
        function renderTechnicians() {
            const container = document.getElementById('technician-list');
            
            if (filteredTechnicians.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">👥</div>
                        <div style="font-size: 18px; margin-bottom: 8px;">暂无技师</div>
                        <div style="font-size: 14px; opacity: 0.8;">请稍后再试或更换搜索条件</div>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredTechnicians.map(tech => `
                <div class="technician-card ${tech.is_recommended == 1 ? 'recommended' : ''}" onclick="showTechnicianDetail(${tech.id})">
                    <div class="tech-avatar">
                        ${tech.workimg ?
                            `<img src="${tech.workimg.startsWith('http') ? tech.workimg : (tech.workimg.startsWith('uploads/') ? tech.workimg : 'uploads/' + tech.workimg)}" alt="${tech.nick}" onerror="this.parentElement.innerHTML='<div style=\\'width:100%;height:100%;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);display:flex;align-items:center;justify-content:center;color:white;font-size:24px;\\'>👤</div>'">` :
                            `<div style="width:100%;height:100%;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);display:flex;align-items:center;justify-content:center;color:white;font-size:24px;">👤</div>`
                        }
                        ${tech.is_recommended == 1 ? '<div class="tech-badge">特别推荐</div>' : ''}
                    </div>
                    <div class="tech-info">
                        <div class="tech-header">
                            <div>
                                <div class="tech-name">${tech.nick || '技师' + tech.id}</div>
                                <div class="tech-info-details">
                                    <div class="tech-detail-item">
                                        <span class="detail-label">年纪:</span>
                                        <span class="detail-value">${tech.age || '未知'}</span>
                                    </div>
                                    <div class="tech-detail-item">
                                        <span class="detail-label">身高:</span>
                                        <span class="detail-value">${tech.height || '未知'}</span>
                                    </div>
                                    <div class="tech-detail-item">
                                        <span class="detail-label">体重:</span>
                                        <span class="detail-value">${tech.weight || '未知'}</span>
                                    </div>
                                    <div class="tech-detail-item">
                                        <span class="detail-label">尺寸:</span>
                                        <span class="detail-value">${tech.size || '未知'}</span>
                                    </div>
                                    <div class="tech-detail-item">
                                        <span class="detail-label">型号:</span>
                                        <span class="detail-value">${tech.model || '未知'}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="tech-distance">&lt;1km</div>
                        </div>
                        
                        <div class="tech-tags">
                            <div class="tech-tag">
                                <span>📍</span>
                                <span>${tech.city || '未知城市'}</span>
                            </div>
                            <div class="tech-tag tech-orders-tag">
                                <span>📊</span>
                                <span>单量 ${tech.virtual_orders || 0}</span>
                            </div>
                        </div>
                        
                        <div class="tech-footer">
                            <div class="tech-status">
                                <div class="status-available">最早可约 今${getCurrentTime()}</div>
                            </div>
                            <button class="service-btn" onclick="event.stopPropagation(); bookService(${tech.id})">可服务</button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 筛选技师
        function filterTechnicians(keyword) {
            const currentCity = document.getElementById('current-city').textContent;
            let baseList = allTechnicians;

            // 先按城市筛选
            if (currentCity && currentCity !== '全部城市') {
                baseList = allTechnicians.filter(tech =>
                    tech.city && tech.city.trim() === currentCity.trim()
                );
            }

            // 再按关键词筛选
            if (!keyword) {
                filteredTechnicians = [...baseList];
            } else {
                filteredTechnicians = baseList.filter(tech =>
                    (tech.nick && tech.nick.toLowerCase().includes(keyword)) ||
                    (tech.phone && tech.phone.includes(keyword)) ||
                    (tech.city && tech.city.toLowerCase().includes(keyword))
                );
            }
            renderTechnicians();
        }

        // 获取当前时间
        function getCurrentTime() {
            const now = new Date();
            const hours = now.getHours().toString().padStart(2, '0');
            const minutes = now.getMinutes().toString().padStart(2, '0');
            return `${hours}:${minutes}`;
        }

        // 显示技师详情
        function showTechnicianDetail(techId) {
            console.log('查看技师详情:', techId);

            // 查找技师信息
            const tech = allTechnicians.find(t => t.id == techId);
            if (!tech) {
                alert('未找到技师信息');
                return;
            }

            // 创建详情模态框
            createTechDetailModal(tech);
        }

        // 创建技师详情模态框
        function createTechDetailModal(tech) {
            // 移除已存在的模态框
            const existingModal = document.getElementById('tech-detail-modal');
            if (existingModal) {
                existingModal.remove();
            }

            // 处理图片路径
            function getImagePath(imagePath) {
                if (!imagePath || !imagePath.trim()) return '';
                const trimmedPath = imagePath.trim();
                if (trimmedPath.startsWith('http')) return trimmedPath;
                if (trimmedPath.startsWith('uploads/')) return trimmedPath;
                return 'uploads/' + trimmedPath;
            }

            // 处理生活照片列表
            function getLifeImages(lifeimg) {
                if (!lifeimg || !lifeimg.trim()) return [];
                return lifeimg.split(',')
                    .map(img => img.trim())
                    .filter(img => img)
                    .map(img => getImagePath(img));
            }

            const lifeImages = getLifeImages(tech.lifeimg);
            const workImage = getImagePath(tech.workimg);
            const videoPath = getImagePath(tech.video);

            // 创建模态框HTML
            const modalHTML = `
                <div id="tech-detail-modal" class="tech-modal">
                    <div class="tech-modal-content">
                        <div class="tech-modal-header">
                            <h2>${tech.nick || '技师' + tech.id}</h2>
                            <button class="tech-modal-close" onclick="closeTechDetailModal()">&times;</button>
                        </div>
                        <div class="tech-modal-body">
                            <!-- 基本信息 -->
                            <div class="tech-info-section">
                                <h3>基本信息</h3>
                                <div class="tech-info-grid">
                                    <div class="info-item">
                                        <span class="info-label">昵称:</span>
                                        <span class="info-value">${tech.nick || '未填写'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">年龄:</span>
                                        <span class="info-value">${tech.age || '未知'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">身高:</span>
                                        <span class="info-value">${tech.height ? tech.height + 'cm' : '未知'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">体重:</span>
                                        <span class="info-value">${tech.weight ? tech.weight + 'kg' : '未知'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">尺寸:</span>
                                        <span class="info-value">${tech.size || '未知'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">型号:</span>
                                        <span class="info-value">${tech.model || '未知'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">城市:</span>
                                        <span class="info-value">${tech.city || '未知'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">住址:</span>
                                        <span class="info-value">${tech.hotel || '未填写'}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">单量:</span>
                                        <span class="info-value">${tech.virtual_orders || 0}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">状态:</span>
                                        <span class="info-value">${tech.is_resting == 1 ? '休息中' : '工作中'}</span>
                                    </div>
                                    ${tech.is_recommended == 1 ? '<div class="info-item"><span class="info-label">特色:</span><span class="info-value recommended-tag">特别推荐</span></div>' : ''}
                                </div>
                            </div>

                            <!-- 工作照 -->
                            ${workImage ? `
                            <div class="tech-info-section">
                                <h3>工作照</h3>
                                <div class="tech-images">
                                    <img src="${workImage}" alt="工作照" class="tech-image" onclick="showImageModal('${workImage}')" onerror="this.style.display='none'">
                                </div>
                            </div>
                            ` : ''}

                            <!-- 生活照 -->
                            ${lifeImages.length > 0 ? `
                            <div class="tech-info-section">
                                <h3>生活照</h3>
                                <div class="tech-images">
                                    ${lifeImages.map(img => `
                                        <img src="${img}" alt="生活照" class="tech-image" onclick="showImageModal('${img}')" onerror="this.style.display='none'">
                                    `).join('')}
                                </div>
                            </div>
                            ` : ''}

                            <!-- 视频 -->
                            ${videoPath ? `
                            <div class="tech-info-section">
                                <h3>视频</h3>
                                <div class="tech-video">
                                    <video controls class="tech-video-player" onclick="showVideoModal('${videoPath}')">
                                        <source src="${videoPath}" type="video/mp4">
                                        您的浏览器不支持视频播放
                                    </video>
                                </div>
                            </div>
                            ` : ''}
                        </div>
                        <div class="tech-modal-footer">
                            <button class="tech-contact-btn" onclick="bookService(${tech.id})">联系技师</button>
                        </div>
                    </div>
                </div>
            `;

            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // 显示模态框
            const modal = document.getElementById('tech-detail-modal');
            modal.style.display = 'flex';

            // 点击背景关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeTechDetailModal();
                }
            });
        }

        // 关闭技师详情模态框
        function closeTechDetailModal() {
            const modal = document.getElementById('tech-detail-modal');
            if (modal) {
                modal.remove();
            }
        }

        // 显示图片放大模态框
        function showImageModal(imageSrc) {
            // 移除已存在的图片模态框
            const existingModal = document.getElementById('image-modal');
            if (existingModal) {
                existingModal.remove();
            }

            const modalHTML = `
                <div id="image-modal" class="image-modal">
                    <div class="image-modal-content">
                        <button class="image-modal-close" onclick="closeImageModal()">&times;</button>
                        <img src="${imageSrc}" alt="查看图片" class="image-modal-img">
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);
            const modal = document.getElementById('image-modal');
            modal.style.display = 'flex';

            // 点击背景关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeImageModal();
                }
            });
        }

        // 关闭图片模态框
        function closeImageModal() {
            const modal = document.getElementById('image-modal');
            if (modal) {
                modal.remove();
            }
        }

        // 显示视频放大模态框
        function showVideoModal(videoSrc) {
            // 移除已存在的视频模态框
            const existingModal = document.getElementById('video-modal');
            if (existingModal) {
                existingModal.remove();
            }

            const modalHTML = `
                <div id="video-modal" class="video-modal">
                    <div class="video-modal-content">
                        <button class="video-modal-close" onclick="closeVideoModal()">&times;</button>
                        <video controls class="video-modal-player" autoplay>
                            <source src="${videoSrc}" type="video/mp4">
                            您的浏览器不支持视频播放
                        </video>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);
            const modal = document.getElementById('video-modal');
            modal.style.display = 'flex';

            // 点击背景关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeVideoModal();
                }
            });
        }

        // 关闭视频模态框
        function closeVideoModal() {
            const modal = document.getElementById('video-modal');
            if (modal) {
                modal.remove();
            }
        }

        // 预约服务
        function bookService(techId) {
            console.log('预约服务:', techId);
            alert(`下单找客服 ruyancd (技师ID: ${techId})`);
        }







        // 显示城市选择器
        function showCitySelector() {
            // 获取城市列表和热门城市
            const cities = availableCities.length > 0 ? availableCities : ['全部城市', '三亚市', '海口市', '儋州市', '琼海市', '文昌市', '万宁市'];
            const currentCity = document.getElementById('current-city').textContent;

            // 获取热门城市列表
            getHotCities().then(hotCities => {
                createCitySelectorModal(cities, hotCities, currentCity);
            });
        }

        // 获取热门城市列表
        function getHotCities() {
            return fetch('admin/city_list.php')
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data)) {
                        return data.filter(city => city.is_hot == 1).map(city => city.name);
                    }
                    return ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市'];
                })
                .catch(() => ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市']);
        }

        // 创建城市选择器模态框
        function createCitySelectorModal(cities, hotCities, currentCity) {
            const modal = document.createElement('div');
            modal.className = 'city-selector-modal';
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0,0,0,0.5); z-index: 10000;
                display: flex; align-items: center; justify-content: center;
                padding: 20px;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 16px; width: 100%; max-width: 500px; max-height: 80vh; overflow: hidden; display: flex; flex-direction: column;">
                    <!-- 头部 -->
                    <div style="padding: 20px; text-align: center; font-size: 18px; font-weight: bold; border-bottom: 1px solid #eee; position: relative;">
                        选择城市
                        <button onclick="this.closest('.city-selector-modal').remove()" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); background: none; border: none; font-size: 20px; cursor: pointer; color: #999;">×</button>
                    </div>

                    <!-- 搜索框和定位按钮 -->
                    <div style="padding: 15px; border-bottom: 1px solid #eee;">
                        <div style="display: flex; gap: 8px; margin-bottom: 10px;">
                            <div style="position: relative; flex: 1;">
                                <input type="text" id="city-search-input" placeholder="搜索城市..." style="
                                    width: 100%; padding: 12px 40px 12px 12px; border: 1px solid #ddd;
                                    border-radius: 8px; font-size: 16px; outline: none;
                                    box-sizing: border-box;
                                ">
                                <span style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #999;">🔍</span>
                            </div>
                            <button onclick="requestLocationPermission()" style="
                                background: linear-gradient(135deg, #00c6a2 0%, #00a085 100%);
                                color: white; border: none; padding: 12px 16px; border-radius: 8px;
                                cursor: pointer; font-size: 14px; white-space: nowrap;
                                transition: all 0.2s ease;
                            " onmouseover="this.style.transform='scale(1.05)'"
                               onmouseout="this.style.transform='scale(1)'"
                               title="重新获取当前位置">
                                📍 定位
                            </button>
                        </div>
                    </div>

                    <!-- 主体内容 -->
                    <div style="flex: 1; display: flex; overflow: hidden;">
                        <!-- 左侧城市列表 -->
                        <div style="flex: 1; display: flex; flex-direction: column;">
                            <!-- 热门城市 -->
                            <div id="hot-cities-section" style="border-bottom: 1px solid #eee;">
                                <div style="padding: 12px 15px; background: #f8f9fa; font-size: 14px; font-weight: bold; color: #666;">
                                    🔥 热门城市
                                </div>
                                <div id="hot-cities-list" style="padding: 8px;">
                                    ${hotCities.map(city => `
                                        <span onclick="selectCity('${city}')" style="
                                            display: inline-block; margin: 4px; padding: 6px 12px;
                                            background: ${city === currentCity ? '#00c6a2' : '#f0f0f0'};
                                            color: ${city === currentCity ? 'white' : '#333'};
                                            border-radius: 16px; cursor: pointer; font-size: 14px;
                                            transition: all 0.2s ease;
                                        " onmouseover="if('${city}' !== '${currentCity}') this.style.background='#e0e0e0'"
                                           onmouseout="if('${city}' !== '${currentCity}') this.style.background='#f0f0f0'">
                                            ${city}
                                        </span>
                                    `).join('')}
                                </div>
                            </div>

                            <!-- 所有城市列表 -->
                            <div style="flex: 1; overflow-y: auto;" id="cities-container">
                                <div style="padding: 12px 15px; background: #f8f9fa; font-size: 14px; font-weight: bold; color: #666;">
                                    📍 所有城市 (按拼音A-Z排序)
                                </div>
                                <div id="all-cities-list">
                                    ${renderCitiesByPinyin(cities, currentCity)}
                                </div>
                            </div>
                        </div>

                        <!-- 右侧A-Z索引 -->
                        <div id="alphabet-index" style="
                            width: 40px; background: #f8f9fa; border-left: 1px solid #eee;
                            display: flex; flex-direction: column; align-items: center;
                            padding: 10px 0; overflow-y: auto;
                        ">
                            ${generateAlphabetIndex()}
                        </div>
                    </div>
                </div>
            `;

            modal.onclick = function(e) {
                if (e.target === modal) modal.remove();
            };

            document.body.appendChild(modal);

            // 初始化搜索功能
            initCitySearch(cities, hotCities, currentCity);
        }

        // 选择城市
        function selectCity(city) {
            document.getElementById('current-city').textContent = city;
            localStorage.setItem('selectedCity', city);
            const modal = document.querySelector('.city-selector-modal');
            if (modal) modal.remove();

            // 根据城市筛选技师
            filterTechniciansByCity(city);
        }

        // 根据城市筛选技师
        function filterTechniciansByCity(city) {
            console.log('筛选城市:', city);

            if (!city || city === '全部城市') {
                // 显示所有技师
                filteredTechnicians = [...allTechnicians];
            } else {
                // 筛选指定城市的技师
                filteredTechnicians = allTechnicians.filter(tech =>
                    tech.city && tech.city.trim() === city.trim()
                );
            }

            renderTechnicians();

            // 显示筛选结果提示
            const container = document.getElementById('technician-list');
            if (filteredTechnicians.length === 0 && city !== '全部城市') {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">🏙️</div>
                        <div style="font-size: 18px; margin-bottom: 8px;">该城市暂无技师</div>
                        <div style="font-size: 14px; opacity: 0.8;">请选择其他城市或稍后再试</div>
                        <button class="nav-btn" onclick="selectCity('全部城市')" style="margin-top: 16px;">查看全部技师</button>
                    </div>
                `;
            }
        }

        // 显示错误信息
        function showError(message) {
            const container = document.getElementById('technician-list');
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">⚠️</div>
                    <div style="font-size: 18px; margin-bottom: 8px;">${message}</div>
                    <button class="nav-btn" onclick="loadTechnicians()" style="margin-top: 16px;">🔄 重新加载</button>
                </div>
            `;
        }

        // 加载技师页公告
        function loadTechAnnouncement() {
            const announcementEnabled = localStorage.getItem('tech_announcement_enabled') === 'true';
            const scrollEnabled = localStorage.getItem('tech_scroll_enabled') === 'true';
            const scrollSpeed = parseInt(localStorage.getItem('tech_scroll_speed')) || 15;
            const announcementText = localStorage.getItem('tech_announcement_text') || '';

            const announcementDiv = document.getElementById('tech-announcement');
            const contentDiv = document.getElementById('tech-announcement-content');

            if (announcementEnabled && announcementText.trim()) {
                contentDiv.innerHTML = announcementText;

                if (scrollEnabled) {
                    contentDiv.className = 'announcement-content scrolling';
                    // 动态设置滚动速度
                    contentDiv.style.animationDuration = scrollSpeed + 's';
                } else {
                    contentDiv.className = 'announcement-content';
                    contentDiv.style.animationDuration = '';
                }

                announcementDiv.style.display = 'block';
            } else {
                announcementDiv.style.display = 'none';
            }
        }

        // 页面加载完成后初始化
        // 生成A-Z索引
        function generateAlphabetIndex() {
            const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
            return letters.map(letter => `
                <div onclick="scrollToLetter('${letter}')" style="
                    width: 24px; height: 24px; display: flex; align-items: center;
                    justify-content: center; cursor: pointer; font-size: 12px;
                    font-weight: bold; color: #666; margin: 1px 0;
                    border-radius: 50%; transition: all 0.2s ease;
                " onmouseover="this.style.background='#00c6a2'; this.style.color='white'"
                   onmouseout="this.style.background='transparent'; this.style.color='#666'">
                    ${letter}
                </div>
            `).join('');
        }

        // 初始化城市搜索功能
        function initCitySearch(cities, hotCities, currentCity) {
            const searchInput = document.getElementById('city-search-input');
            const allCitiesList = document.getElementById('all-cities-list');
            const hotCitiesSection = document.getElementById('hot-cities-section');

            searchInput.addEventListener('input', function(e) {
                const keyword = e.target.value.trim().toLowerCase();

                if (!keyword) {
                    // 显示所有城市和热门城市，使用拼音分组
                    hotCitiesSection.style.display = 'block';
                    allCitiesList.innerHTML = renderCitiesByPinyin(cities, currentCity);
                } else {
                    // 隐藏热门城市，只显示搜索结果
                    hotCitiesSection.style.display = 'none';
                    const filteredCities = cities.filter(city =>
                        city.toLowerCase().includes(keyword)
                    );

                    if (filteredCities.length > 0) {
                        allCitiesList.innerHTML = filteredCities.map(city => `
                            <div onclick="selectCity('${city}')" style="
                                padding: 12px 20px; border-bottom: 1px solid #f0f0f0;
                                cursor: pointer; transition: background 0.2s ease;
                                ${city === currentCity ? 'background: #f0f8ff; color: #00c6a2; font-weight: bold;' : ''}
                            " onmouseover="if('${city}' !== '${currentCity}') this.style.background='#f8f9fa'"
                               onmouseout="if('${city}' !== '${currentCity}') this.style.background='transparent'">
                                ${highlightSearchKeyword(city, keyword)}
                            </div>
                        `).join('');
                    } else {
                        allCitiesList.innerHTML = `
                            <div style="padding: 40px 20px; text-align: center; color: #999;">
                                <div style="font-size: 48px; margin-bottom: 16px;">🔍</div>
                                <div>未找到匹配的城市</div>
                                <div style="font-size: 14px; margin-top: 8px;">请尝试其他关键词</div>
                            </div>
                        `;
                    }
                }
            });
        }

        // 高亮搜索关键词
        function highlightSearchKeyword(text, keyword) {
            if (!keyword) return text;
            const regex = new RegExp(`(${keyword})`, 'gi');
            return text.replace(regex, '<span style="background: #ffeb3b; color: #333;">$1</span>');
        }

        // 按拼音分组渲染城市列表
        function renderCitiesByPinyin(cities, currentCity) {
            // 跳过"全部城市"选项，按拼音分组
            const regularCities = cities.filter(city => city !== '全部城市');

            // 按拼音首字母分组
            const groupedCities = {};
            regularCities.forEach(city => {
                const letter = getCityFirstLetter(city);
                if (!groupedCities[letter]) {
                    groupedCities[letter] = [];
                }
                groupedCities[letter].push(city);
            });

            // 按字母顺序渲染分组
            const letters = Object.keys(groupedCities).sort();
            let html = '';

            // 首先添加"全部城市"选项
            if (cities.includes('全部城市')) {
                html += `
                    <div onclick="selectCity('全部城市')" style="
                        padding: 12px 20px; border-bottom: 1px solid #f0f0f0;
                        cursor: pointer; transition: background 0.2s ease;
                        background: #f0f8ff; color: #00c6a2; font-weight: bold;
                        border-left: 3px solid #00c6a2;
                        ${currentCity === '全部城市' ? 'background: #e8f5e8; color: #00c6a2;' : ''}
                    " onmouseover="if('全部城市' !== '${currentCity}') this.style.background='#f8f9fa'"
                       onmouseout="if('全部城市' !== '${currentCity}') this.style.background='#f0f8ff'">
                        🌐 全部城市
                    </div>
                `;
            }

            // 渲染按字母分组的城市
            letters.forEach(letter => {
                const citiesInGroup = groupedCities[letter];

                // 分组标题
                html += `
                    <div style="
                        padding: 8px 20px; background: #f8f9fa;
                        font-size: 12px; font-weight: bold; color: #666;
                        border-top: 1px solid #eee; position: sticky; top: 0; z-index: 1;
                    " id="group-${letter}">
                        ${letter} (${citiesInGroup.length}个)
                    </div>
                `;

                // 分组内的城市
                citiesInGroup.forEach(city => {
                    html += `
                        <div onclick="selectCity('${city}')" style="
                            padding: 12px 20px; border-bottom: 1px solid #f0f0f0;
                            cursor: pointer; transition: background 0.2s ease;
                            ${city === currentCity ? 'background: #f0f8ff; color: #00c6a2; font-weight: bold;' : ''}
                        " onmouseover="if('${city}' !== '${currentCity}') this.style.background='#f8f9fa'"
                           onmouseout="if('${city}' !== '${currentCity}') this.style.background='transparent'"
                           data-letter="${letter}">
                            ${city}
                        </div>
                    `;
                });
            });

            return html;
        }

        // 获取城市名称的首字母
        function getCityFirstLetter(cityName) {
            // 简单的拼音首字母映射
            const pinyinMap = {
                '阿': 'A', '安': 'A', '鞍': 'A',
                '北': 'B', '包': 'B', '保': 'B', '蚌': 'B', '本': 'B', '滨': 'B', '亳': 'B',
                '重': 'C', '成': 'C', '长': 'C', '常': 'C', '承': 'C', '沧': 'C', '滁': 'C', '池': 'C', '潮': 'C', '崇': 'C', '赤': 'C', '朝': 'C',
                '大': 'D', '东': 'D', '德': 'D', '丹': 'D', '定': 'D', '都': 'D', '儋': 'D', '达': 'D',
                '鄂': 'E', '恩': 'E',
                '福': 'F', '佛': 'F', '抚': 'F', '阜': 'F', '防': 'F', '丰': 'F',
                '广': 'G', '贵': 'G', '桂': 'G', '赣': 'G', '格': 'G', '固': 'G',
                '杭': 'H', '哈': 'H', '海': 'H', '合': 'H', '河': 'H', '衡': 'H', '呼': 'H', '黄': 'H', '惠': 'H', '湖': 'H', '怀': 'H', '邯': 'H', '汉': 'H', '菏': 'H', '鹤': 'H', '黑': 'H', '红': 'H', '淮': 'H', '华': 'H',
                '济': 'J', '金': 'J', '江': 'J', '嘉': 'J', '九': 'J', '吉': 'J', '锦': 'J', '焦': 'J', '荆': 'J', '景': 'J', '揭': 'J', '晋': 'J',
                '昆': 'K', '开': 'K', '克': 'K', '喀': 'K',
                '兰': 'L', '拉': 'L', '洛': 'L', '连': 'L', '柳': 'L', '六': 'L', '临': 'L', '廊': 'L', '聊': 'L', '辽': 'L', '丽': 'L', '娄': 'L', '漯': 'L', '吕': 'L', '龙': 'L', '陇': 'L', '林': 'L',
                '马': 'M', '梅': 'M', '绵': 'M', '牡': 'M', '茂': 'M', '眉': 'M',
                '南': 'N', '宁': 'N', '内': 'N', '那': 'N',
                '盘': 'P', '平': 'P', '莆': 'P', '濮': 'P', '攀': 'P', '普': 'P',
                '青': 'Q', '秦': 'Q', '齐': 'Q', '泉': 'Q', '衢': 'Q', '庆': 'Q', '钦': 'Q', '曲': 'Q', '清': 'Q',
                '日': 'R', '瑞': 'R',
                '上': 'S', '深': 'S', '苏': 'S', '沈': 'S', '石': 'S', '三': 'S', '绍': 'S', '汕': 'S', '韶': 'S', '商': 'S', '十': 'S', '随': 'S', '宿': 'S', '朔': 'S', '双': 'S', '松': 'S', '遂': 'S', '邵': 'S', '神': 'S', '山': 'S',
                '天': 'T', '太': 'T', '唐': 'T', '台': 'T', '泰': 'T', '通': 'T', '铜': 'T', '铁': 'T', '塔': 'T', '吐': 'T', '图': 'T',
                '乌': 'U', '无': 'W', '武': 'W', '温': 'W', '威': 'W', '潍': 'W', '芜': 'W', '梧': 'W', '万': 'W', '文': 'W', '渭': 'W', '吴': 'W', '五': 'W',
                '西': 'X', '厦': 'X', '徐': 'X', '新': 'X', '襄': 'X', '许': 'X', '宣': 'X', '咸': 'X', '湘': 'X', '孝': 'X', '信': 'X', '忻': 'X', '邢': 'X', '兴': 'X', '锡': 'X',
                '银': 'Y', '宜': 'Y', '扬': 'Y', '烟': 'Y', '盐': 'Y', '营': 'Y', '岳': 'Y', '运': 'Y', '玉': 'Y', '榆': 'Y', '永': 'Y', '益': 'Y', '阳': 'Y', '鹰': 'Y', '伊': 'Y', '延': 'Y', '雅': 'Y', '义': 'Y',
                '郑': 'Z', '珠': 'Z', '中': 'Z', '株': 'Z', '淄': 'Z', '枣': 'Z', '张': 'Z', '湛': 'Z', '肇': 'Z', '镇': 'Z', '周': 'Z', '驻': 'Z', '舟': 'Z', '漳': 'Z', '遵': 'Z', '资': 'Z', '自': 'Z', '昭': 'Z'
            };

            const firstChar = cityName.charAt(0);
            return pinyinMap[firstChar] || firstChar.toUpperCase();
        }

        // 滚动到指定字母的城市
        function scrollToLetter(letter) {
            const citiesContainer = document.getElementById('cities-container');

            // 首先尝试找到分组标题
            let targetElement = citiesContainer.querySelector(`#group-${letter}`);

            // 如果没有分组标题，尝试找到第一个该字母的城市
            if (!targetElement) {
                targetElement = citiesContainer.querySelector(`[data-letter="${letter}"]`);
            }

            if (targetElement) {
                targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                // 高亮显示
                const originalBackground = targetElement.style.background;
                targetElement.style.background = '#e3f2fd';
                setTimeout(() => {
                    targetElement.style.background = originalBackground;
                }, 1000);
            } else {
                // 如果找不到对应字母的城市，显示提示
                console.log(`没有找到字母 ${letter} 开头的城市`);
            }
        }

        // 初始化城市设置
        function initLocationAndCity() {
            // 从localStorage恢复选中的城市
            const savedCity = localStorage.getItem('selectedCity');
            if (savedCity) {
                document.getElementById('current-city').textContent = savedCity;
                filterTechniciansByCity(savedCity);
            }
        }

        // 查找匹配的城市
        function findMatchingCity(locationCityName) {
            // 移除"市"字符进行匹配
            const cleanLocationName = locationCityName.replace(/市$/, '');

            // 在可用城市列表中查找匹配项
            for (const city of availableCities) {
                const cleanCityName = city.replace(/市$/, '');
                if (cleanCityName === cleanLocationName ||
                    city === locationCityName ||
                    cleanLocationName.includes(cleanCityName) ||
                    cleanCityName.includes(cleanLocationName)) {
                    return city;
                }
            }

            return null;
        }



        document.addEventListener('DOMContentLoaded', function() {
            loadTechnicians();
            loadTechAnnouncement();

            // 初始化城市设置
            initLocationAndCity();
        });
    </script>
</body>
</html>
