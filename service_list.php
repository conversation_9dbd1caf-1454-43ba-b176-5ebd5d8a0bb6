<?php
// service_list.php - 公共服务项目列表API (无需登录)
header('Content-Type: application/json; charset=utf-8');
require_once 'db.php';

try {
    // 检查services表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'services'");
    if (!$stmt->fetch()) {
        // 如果services表不存在，返回空数据
        echo json_encode([
            'success' => true,
            'data' => [],
            'total' => 0,
            'message' => '服务项目表尚未创建'
        ]);
        exit;
    }

    // 查询所有激活的服务项目，按排序字段排序
    $stmt = $pdo->query("SELECT id, name, image, service_code, duration, price, virtual_sales, service_type, sort_order, is_active, created_at, updated_at FROM services WHERE is_active = 1 ORDER BY sort_order ASC, id DESC");
    $data = $stmt ? $stmt->fetchAll() : [];

    $fields = ['id', 'name', 'image', 'service_code', 'duration', 'price', 'virtual_sales', 'service_type', 'sort_order', 'is_active', 'created_at', 'updated_at'];

    // 处理数据格式
    foreach ($data as &$row) {
        // 确保所有字段都存在
        foreach ($fields as $field) {
            if (!isset($row[$field])) {
                $row[$field] = '';
            }
        }
        
        // 格式化价格
        $row['price'] = number_format($row['price'], 2);
        
        // 格式化时长
        $row['duration_text'] = $row['duration'] . '分钟';

        // 格式化状态
        $row['status_text'] = $row['is_active'] ? '激活' : '停用';

        // 格式化项目类型
        $typeMap = [
            'spa' => '会所项目',
            'studio' => '工作室项目',
            'ts' => 'TS项目',
            'cd' => 'CD项目'
        ];
        $row['service_type_text'] = $typeMap[$row['service_type']] ?? '会所项目';

        // 确保排序字段存在
        $row['sort_order'] = $row['sort_order'] ?? 0;
    }

    echo json_encode([
        'success' => true,
        'data' => $data,
        'total' => count($data)
    ]);

} catch (Exception $e) {
    error_log("Public service list error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'msg' => '获取服务列表失败',
        'data' => []
    ]);
}
?>
