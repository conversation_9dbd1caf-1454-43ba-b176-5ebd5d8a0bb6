<?php
// service_delete.php - 删除服务项目
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

try {
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    
    if ($id <= 0) {
        echo json_encode(['success' => false, 'msg' => '无效的服务ID']);
        exit;
    }

    // 查询服务信息，获取图片路径用于删除
    $stmt = $pdo->prepare("SELECT id, name, image FROM services WHERE id = ?");
    $stmt->execute([$id]);
    $service = $stmt->fetch();
    
    if (!$service) {
        echo json_encode(['success' => false, 'msg' => '服务项目不存在']);
        exit;
    }

    // 删除数据库记录
    $stmt = $pdo->prepare("DELETE FROM services WHERE id = ?");
    $result = $stmt->execute([$id]);
    
    if ($result) {
        // 删除图片文件
        if ($service['image']) {
            $imagePath = __DIR__ . '/../' . $service['image'];
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }
        }
        
        echo json_encode([
            'success' => true, 
            'msg' => "服务项目「{$service['name']}」已删除"
        ]);
    } else {
        echo json_encode(['success' => false, 'msg' => '删除失败']);
    }

} catch (Exception $e) {
    error_log("Service delete error: " . $e->getMessage());
    echo json_encode(['success' => false, 'msg' => '删除失败: ' . $e->getMessage()]);
}
?>
