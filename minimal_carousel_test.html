<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小轮播图测试</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #222;
        }
        
        .carousel-container {
            position: relative;
            width: 100%;
            height: 180px;
            overflow: hidden;
            border-radius: 0 0 18px 18px;
        }
        
        .carousel-wrapper {
            display: flex;
            width: 100%;
            height: 100%;
            transition: transform 0.5s ease-in-out;
        }
        
        .carousel-slide {
            min-width: 100%;
            height: 100%;
            position: relative;
        }
        
        .carousel-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }
        
        .carousel-indicators {
            position: absolute;
            bottom: 12px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
            z-index: 10;
        }
        
        .carousel-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .carousel-indicator.active {
            background: rgba(255, 255, 255, 0.9);
            transform: scale(1.2);
        }
        
        .carousel-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 180px;
            background: #f5f5f5;
            color: #999;
            font-size: 14px;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .debug {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 300px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- 顶部轮播图 -->
    <div style="width:100%; background:#fff; min-height:180px; border-radius:0 0 18px 18px; overflow:hidden; box-shadow:0 2px 8px rgba(0,0,0,0.04);">
      <div id="carousel" class="carousel-container">
        <!-- 轮播图内容将通过JavaScript动态加载 -->
        <div class="carousel-loading">
          <div style="display: flex; align-items: center; gap: 8px;">
            <div style="width: 16px; height: 16px; border: 2px solid #ddd; border-top: 2px solid #00c6a2; border-radius: 50%; animation: spin 1s linear infinite;"></div>
            正在加载轮播图...
          </div>
        </div>
      </div>
    </div>
    
    <div id="debug" class="debug">调试信息...</div>
    
    <div style="padding: 20px; color: white;">
        <button onclick="testAPI()" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 5px;">测试API</button>
        <button onclick="loadCarousel()" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 5px;">加载轮播图</button>
        <button onclick="showTestImages()" style="background: #ffc107; color: black; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 5px;">显示测试图片</button>
    </div>

    <script>
        let debugInfo = [];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.push(`[${timestamp}] ${message}`);
            document.getElementById('debug').innerHTML = debugInfo.slice(-10).join('<br>');
            console.log(message);
        }
        
        async function testAPI() {
            log('开始测试API...');
            
            try {
                const response = await fetch('banner_list.php');
                log(`API响应状态: ${response.status}`);
                
                const data = await response.json();
                log(`API返回: ${JSON.stringify(data)}`);
                
                if (data.success && data.data && data.data.length > 0) {
                    log(`找到 ${data.data.length} 个轮播图`);
                    return data.data;
                } else {
                    log('没有轮播图数据');
                    return [];
                }
            } catch (error) {
                log(`API测试失败: ${error.message}`);
                return [];
            }
        }
        
        async function loadCarousel() {
            log('开始加载轮播图...');
            
            const slides = await testAPI();
            
            if (slides.length > 0) {
                renderCarousel(slides);
            } else {
                showEmptyState();
            }
        }
        
        function renderCarousel(slides) {
            log(`渲染 ${slides.length} 个轮播图`);
            
            const carousel = document.getElementById('carousel');
            if (!carousel) {
                log('错误: 找不到轮播图容器');
                return;
            }
            
            const html = `
                <div class="carousel-wrapper">
                    ${slides.map((slide, index) => `
                        <div class="carousel-slide">
                            <img src="${slide.img}" 
                                 alt="轮播图${index + 1}"
                                 onload="log('图片加载成功: ${slide.img}')"
                                 onerror="log('图片加载失败: ${slide.img}'); this.src='images/lbt.png'" />
                        </div>
                    `).join('')}
                </div>
                
                ${slides.length > 1 ? `
                    <div class="carousel-indicators">
                        ${slides.map((_, index) => `
                            <div class="carousel-indicator ${index === 0 ? 'active' : ''}"></div>
                        `).join('')}
                    </div>
                ` : ''}
            `;
            
            carousel.innerHTML = html;
            log('轮播图HTML已插入');
        }
        
        function showEmptyState() {
            log('显示空状态');
            
            const carousel = document.getElementById('carousel');
            carousel.innerHTML = `
                <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 180px; background: #f5f5f5; color: #999;">
                    <div style="font-size: 48px; margin-bottom: 12px;">🖼️</div>
                    <div>暂无轮播图</div>
                </div>
            `;
        }
        
        function showTestImages() {
            log('显示测试图片');
            
            const testSlides = [
                { id: 1, img: 'uploads/banner_20250718_200312_9681.jpg' },
                { id: 2, img: 'uploads/banner_20250718_223358_2505.jpg' },
                { id: 3, img: 'uploads/banner_20250718_223404_6006.jpg' },
                { id: 4, img: 'uploads/banner_20250718_223412_6801.jpg' }
            ];
            
            renderCarousel(testSlides);
        }
        
        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            
            // 检查容器
            const carousel = document.getElementById('carousel');
            if (carousel) {
                log('找到轮播图容器');
            } else {
                log('错误: 未找到轮播图容器');
            }
            
            // 自动加载
            setTimeout(() => {
                loadCarousel();
            }, 1000);
        });
    </script>
</body>
</html>
