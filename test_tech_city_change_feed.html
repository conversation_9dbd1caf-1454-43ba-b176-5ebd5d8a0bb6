<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技师城市变更动态测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .workflow {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .workflow-step {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            text-align: center;
            position: relative;
        }
        .workflow-step::after {
            content: '→';
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 20px;
            color: #007bff;
        }
        .workflow-step:last-child::after {
            display: none;
        }
        .workflow-step .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-weight: bold;
        }
        .workflow-step .step-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .workflow-step .step-desc {
            font-size: 12px;
            color: #666;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .btn.warning:hover {
            background: #e0a800;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .comparison-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
        .comparison-item h4 {
            margin-top: 0;
            color: #007bff;
        }
        .feed-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px;
            margin: 10px 0;
            font-size: 14px;
        }
        .feed-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        .feed-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #ddd;
        }
        .feed-name {
            font-weight: bold;
            color: #333;
        }
        .feed-time {
            font-size: 11px;
            color: #999;
        }
        .feed-content {
            color: #333;
            margin: 8px 0;
        }
        .feed-details {
            background: #e3f2fd;
            padding: 6px 8px;
            border-radius: 4px;
            font-size: 11px;
            color: #1976d2;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 技师城市变更动态测试</h1>
        
        <div class="section">
            <h3>📋 功能说明</h3>
            <p style="color: #666; line-height: 1.6;">
                当管理员在后台修改技师的城市信息时，系统会自动检测城市变更并生成相应的技师动态。
                这确保了动态页面能够实时反映技师的位置变化。
            </p>
            
            <div class="info">
                <strong>🎯 测试场景</strong><br>
                管理员将技师从"赣州市"变更到"三亚市"，系统应该自动生成一条动态：
                "技师李大宝 22/175/60/16 到三亚了"
            </div>
        </div>
        
        <div class="section">
            <h3>🔄 工作流程</h3>
            <div class="workflow">
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div class="step-title">管理员编辑</div>
                    <div class="step-desc">在后台修改技师城市</div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div class="step-title">系统检测</div>
                    <div class="step-desc">检测城市是否发生变更</div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div class="step-title">生成动态</div>
                    <div class="step-desc">自动添加技师动态记录</div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">4</div>
                    <div class="step-title">前端显示</div>
                    <div class="step-desc">动态页面显示新动态</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>🧪 测试操作</h3>
            <p>按照以下步骤测试技师城市变更动态功能：</p>
            
            <div style="margin: 20px 0;">
                <button class="btn success" onclick="openAdminPanel()">🔧 1. 打开后台管理</button>
                <button class="btn" onclick="checkCurrentFeeds()">📱 2. 查看当前动态</button>
                <button class="btn warning" onclick="openFeedPage()">🔄 3. 刷新动态页面</button>
            </div>
            
            <div class="info">
                <strong>📝 测试步骤：</strong><br>
                1. 点击"打开后台管理"，登录后台<br>
                2. 进入"技师管理"页面<br>
                3. 找到一个技师，点击"编辑"<br>
                4. 修改技师的城市（例如从赣州市改为三亚市）<br>
                5. 保存修改<br>
                6. 返回动态页面查看是否生成了新的技师动态
            </div>
            
            <div id="test-result"></div>
        </div>
        
        <div class="section">
            <h3>📊 动态对比</h3>
            <div class="comparison">
                <div class="comparison-item">
                    <h4>🔄 修改前状态</h4>
                    <div class="feed-preview">
                        <div class="feed-header">
                            <div class="feed-avatar"></div>
                            <div>
                                <div class="feed-name">李大宝</div>
                                <div class="feed-time">当前位置</div>
                            </div>
                        </div>
                        <div class="feed-content">技师李大宝 22/175/60/16 在赣州市</div>
                        <div class="feed-details">当前状态：在赣州市提供服务</div>
                    </div>
                </div>
                <div class="comparison-item">
                    <h4>✅ 修改后生成的动态</h4>
                    <div class="feed-preview">
                        <div class="feed-header">
                            <div class="feed-avatar"></div>
                            <div>
                                <div class="feed-name">李大宝</div>
                                <div class="feed-time">刚刚 · 三亚市</div>
                            </div>
                        </div>
                        <div class="feed-content">技师李大宝 22/175/60/16 到三亚了</div>
                        <div class="feed-details">
                            <strong>更换城市</strong> 三亚市 (从赣州市)<br>
                            年龄:22 | 身高:175cm | 体重:60kg | 服务年限:16年
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>📱 当前技师动态</h3>
            <div id="current-feeds" style="max-height: 400px; overflow-y: auto;">
                <div class="loading">点击"查看当前动态"加载</div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('技师城市变更动态测试页面加载完成');
        });
        
        // 打开后台管理
        function openAdminPanel() {
            window.open('admin/admin_login.html', '_blank');
            
            document.getElementById('test-result').innerHTML = `
                <div class="info">
                    ℹ️ 已打开后台管理页面。请按照以下步骤操作：<br>
                    1. 使用管理员账号登录<br>
                    2. 进入"技师管理"页面<br>
                    3. 选择一个技师点击"编辑"<br>
                    4. 修改城市字段（使用新的下拉框选择）<br>
                    5. 保存修改<br>
                    6. 返回此页面查看结果
                </div>
            `;
        }
        
        // 查看当前技师动态
        async function checkCurrentFeeds() {
            const container = document.getElementById('current-feeds');
            container.innerHTML = '<div class="loading">正在加载技师动态...</div>';
            
            try {
                const response = await fetch('technician_feed_list.php');
                const data = await response.json();
                
                if (data.success && data.feeds && data.feeds.length > 0) {
                    container.innerHTML = data.feeds.map(feed => `
                        <div class="feed-preview">
                            <div class="feed-header">
                                <img src="${feed.avatar}" alt="头像" class="feed-avatar" onerror="this.style.background='#ddd'">
                                <div>
                                    <div class="feed-name">${feed.nickname}</div>
                                    <div class="feed-time">${feed.time} · ${feed.location}</div>
                                </div>
                            </div>
                            <div class="feed-content">${feed.content}</div>
                            ${feed.technician_info ? `
                                <div class="feed-details">
                                    <strong>${feed.technician_info.action_type === 'move' ? '更换城市' : '新加入'}</strong> 
                                    ${feed.technician_info.city}
                                    ${feed.technician_info.previous_city ? ` (从${feed.technician_info.previous_city})` : ''}<br>
                                    年龄:${feed.technician_info.age} | 身高:${feed.technician_info.height}cm | 
                                    体重:${feed.technician_info.weight}kg | 服务年限:${feed.technician_info.service_years}年
                                </div>
                            ` : ''}
                        </div>
                    `).join('');
                    
                    document.getElementById('test-result').innerHTML = `
                        <div class="success">
                            ✅ 成功加载 ${data.feeds.length} 条技师动态
                        </div>
                    `;
                } else {
                    container.innerHTML = '<div class="loading">暂无技师动态</div>';
                    document.getElementById('test-result').innerHTML = `
                        <div class="info">ℹ️ 当前没有技师动态，请先在后台修改技师城市</div>
                    `;
                }
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败：${error.message}</div>`;
                document.getElementById('test-result').innerHTML = `
                    <div class="error">❌ 加载技师动态失败：${error.message}</div>
                `;
            }
        }
        
        // 打开动态页面
        function openFeedPage() {
            window.open('feed.html', '_blank');
            
            document.getElementById('test-result').innerHTML = `
                <div class="info">
                    📱 已打开动态页面。如果刚才修改了技师城市，应该能看到新生成的动态。
                </div>
            `;
        }
        
        // 模拟技师城市变更（仅用于演示）
        function simulateCityChange() {
            document.getElementById('test-result').innerHTML = `
                <div class="success">
                    🎭 模拟场景：<br>
                    • 技师：李大宝<br>
                    • 原城市：赣州市<br>
                    • 新城市：三亚市<br>
                    • 生成动态：技师李大宝 22/175/60/16 到三亚了<br>
                    <br>
                    请在实际后台操作中验证此功能。
                </div>
            `;
        }
        
        // 检查技师动态API
        async function checkFeedAPI() {
            try {
                const response = await fetch('technician_feed_list.php');
                const data = await response.json();
                
                document.getElementById('test-result').innerHTML = `
                    <div class="success">
                        ✅ 技师动态API正常<br>
                        • 接口地址：technician_feed_list.php<br>
                        • 响应状态：${data.success ? '成功' : '失败'}<br>
                        • 动态数量：${data.feeds ? data.feeds.length : 0}<br>
                        • 分页信息：${data.pagination ? JSON.stringify(data.pagination) : '无'}
                    </div>
                `;
            } catch (error) {
                document.getElementById('test-result').innerHTML = `
                    <div class="error">❌ 技师动态API异常：${error.message}</div>
                `;
            }
        }
        
        // 添加一些快捷测试按钮
        document.addEventListener('DOMContentLoaded', function() {
            const quickTests = document.createElement('div');
            quickTests.innerHTML = `
                <div style="position: fixed; bottom: 20px; right: 20px; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1000;">
                    <div style="font-weight: bold; margin-bottom: 10px;">🚀 快速测试</div>
                    <button class="btn" style="display: block; width: 100%; margin-bottom: 5px;" onclick="checkCurrentFeeds()">📱 刷新动态</button>
                    <button class="btn" style="display: block; width: 100%; margin-bottom: 5px;" onclick="checkFeedAPI()">🔧 检查API</button>
                    <button class="btn" style="display: block; width: 100%;" onclick="simulateCityChange()">🎭 模拟场景</button>
                </div>
            `;
            document.body.appendChild(quickTests);
        });
    </script>
</body>
</html>
