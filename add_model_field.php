<?php
// add_model_field.php - 为technician表添加model字段
header('Content-Type: application/json; charset=utf-8');

require_once 'db.php';

try {
    // 检查model字段是否已存在
    $checkStmt = $pdo->query("SHOW COLUMNS FROM technician LIKE 'model'");
    $fieldExists = $checkStmt->rowCount() > 0;
    
    if ($fieldExists) {
        echo json_encode([
            'success' => true,
            'message' => 'model字段已存在，无需添加',
            'action' => 'skip'
        ]);
    } else {
        // 添加model字段
        $alterSql = "ALTER TABLE technician ADD COLUMN model VARCHAR(32) DEFAULT '' AFTER size";
        $pdo->exec($alterSql);
        
        echo json_encode([
            'success' => true,
            'message' => 'model字段添加成功',
            'action' => 'added'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'message' => '添加model字段失败'
    ]);
}
?>
