<?php
// admin_index.php - 后台管理主页
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    header('Location: admin_login.html');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>后台管理系统</title>
  <style>
    /* 基础样式 */
    * { box-sizing: border-box; }
    body { 
      background: #f7f8fa; 
      font-family: 'Roboto', 'PingFang SC', 'Microsoft YaHei', sans-serif; 
      margin: 0; 
      padding: 0;
    }

    /* 布局样式 */
    .admin-layout { 
      display: flex; 
      min-height: 100vh; 
    }

    /* 导航栏样式 */
    .admin-nav {
      width: 220px; 
      background: #222; 
      color: #fff; 
      padding: 0; 
      display: flex; 
      flex-direction: column; 
      align-items: center; 
      box-shadow: 2px 0 12px rgba(0,0,0,0.06); 
      min-height: 100vh;
    }
    .admin-nav h3 { 
      color: #fff; 
      font-size: 22px; 
      font-weight: bold; 
      margin: 32px 0 24px 0; 
    }
    .admin-nav ul { 
      list-style: none; 
      padding: 0; 
      width: 100%; 
    }
    .admin-nav li { 
      padding: 16px 0 16px 38px; 
      font-size: 16px; 
      color: #bbb; 
      cursor: pointer; 
      transition: background 0.2s, color 0.2s; 
    }
    .admin-nav li.active, 
    .admin-nav li:hover { 
      background: #111; 
      color: #00c6a2; 
    }

    /* 内容区域样式 */
    .admin-content { 
      flex: 1; 
      background: #fff; 
      border-radius: 0; 
      box-shadow: 0 4px 24px rgba(0,0,0,0.06); 
      margin: 38px 38px 38px 0; 
      padding: 38px; 
      min-width: 0; 
    }
    .admin-header { 
      display: flex; 
      justify-content: space-between; 
      align-items: center; 
      margin-bottom: 28px; 
    }
    .admin-header h2 {
      margin: 0;
      color: #222;
      font-size: 24px;
    }

    /* 通用组件样式 */
    .btn {
      border: none;
      border-radius: 6px;
      padding: 8px 16px;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    .btn-primary { background: #2196f3; color: #fff; }
    .btn-primary:hover { background: #1976d2; }
    .btn-success { background: #4caf50; color: #fff; }
    .btn-success:hover { background: #388e3c; }
    .btn-danger { background: #f44336; color: #fff; }
    .btn-danger:hover { background: #d32f2f; }
    .btn-warning { background: #ff9800; color: #fff; }
    .btn-warning:hover { background: #f57c00; }
    .btn-secondary { background: #6c757d; color: #fff; }
    .btn-secondary:hover { background: #5a6268; }

    .card {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
      padding: 24px;
      margin-bottom: 24px;
    }
    .card-title {
      font-size: 20px;
      font-weight: 500;
      color: #222;
      margin-bottom: 18px;
    }

    .table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
    }
    .table th,
    .table td {
      padding: 12px 8px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }
    .table th {
      background: #f8f9fa;
      font-weight: 500;
      color: #666;
    }

    .modal {
      display: none;
      position: fixed;
      z-index: 9999;
      left: 0;
      top: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0,0,0,0.5);
      justify-content: center;
      align-items: center;
    }
    .modal-content {
      background: #fff;
      border-radius: 12px;
      max-width: 500px;
      width: 95vw;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 4px 24px rgba(0,0,0,0.12);
      position: relative;
    }
    .modal-header {
      padding: 20px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .modal-body {
      padding: 20px;
    }
    .modal-close {
      background: none;
      border: none;
      font-size: 24px;
      color: #888;
      cursor: pointer;
    }

    .form-group {
      margin-bottom: 16px;
    }
    .form-label {
      display: block;
      margin-bottom: 6px;
      font-size: 14px;
      color: #666;
    }
    .form-control {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
    }
    .form-control:focus {
      outline: none;
      border-color: #2196f3;
      box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
    }

    /* 响应式设计 */
    @media (max-width: 900px) {
      .admin-layout { flex-direction: column; }
      .admin-nav { 
        width: 100%; 
        min-height: 60px; 
        flex-direction: row; 
        align-items: center; 
        justify-content: flex-start; 
        box-shadow: none; 
      }
      .admin-nav h3 { 
        margin: 0 18px 0 0; 
        font-size: 18px; 
      }
      .admin-nav ul { 
        display: flex; 
        flex-direction: row; 
        overflow-x: auto;
      }
      .admin-nav li { 
        padding: 12px 18px; 
        font-size: 15px; 
        white-space: nowrap;
      }
      .admin-content { 
        margin: 0; 
        padding: 18px; 
      }
    }

    /* 状态样式 */
    .status-pending { color: #ff9800; }
    .status-approved { color: #4caf50; }
    .status-rejected { color: #f44336; }
    .status-review { color: #2196f3; }

    /* 工具类 */
    .text-center { text-align: center; }
    .text-muted { color: #888; }
    .mb-0 { margin-bottom: 0; }
    .mt-2 { margin-top: 16px; }
    .d-flex { display: flex; }
    .align-items-center { align-items: center; }
    .justify-content-between { justify-content: space-between; }
    .gap-2 { gap: 16px; }

    /* 推荐管理样式 */
    .recommend-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 24px;
    }

    .recommend-switch-section {
      background: #f8f9fa;
      padding: 16px;
      border-radius: 8px;
      margin-bottom: 20px;
      border: 1px solid #e9ecef;
    }

    .recommend-switch-container {
      display: flex;
      align-items: center;
    }

    .recommend-switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }

    .recommend-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .recommend-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 34px;
    }

    .recommend-slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }

    input:checked + .recommend-slider {
      background-color: #4caf50;
    }

    input:checked + .recommend-slider:before {
      transform: translateX(26px);
    }

    .recommend-tabs {
      border: 1px solid #eee;
      border-radius: 8px;
      overflow: hidden;
    }

    .tab-buttons {
      display: flex;
      background: #f8f9fa;
      border-bottom: 1px solid #eee;
    }

    .tab-btn {
      flex: 1;
      padding: 12px 20px;
      border: none;
      background: transparent;
      cursor: pointer;
      font-size: 14px;
      color: #666;
      transition: all 0.3s ease;
    }

    .tab-btn.active {
      background: #2196f3;
      color: white;
    }

    .tab-btn:hover:not(.active) {
      background: #e3f2fd;
    }

    .tab-content {
      display: none;
      padding: 24px;
    }

    .tab-content.active {
      display: block;
    }

    .recommend-rules {
      background: white;
      padding: 16px;
      border-radius: 6px;
      border: 1px solid #eee;
    }

    .rule-item {
      display: flex;
      margin-bottom: 12px;
      align-items: center;
    }

    .rule-item:last-child {
      margin-bottom: 0;
    }

    .rule-label {
      color: #666;
      font-weight: 500;
      min-width: 80px;
    }

    .rule-value {
      color: #333;
    }

    .recommended-tech-item,
    .available-tech-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border: 1px solid #eee;
      border-radius: 8px;
      margin-bottom: 12px;
      background: white;
      transition: all 0.2s ease;
    }

    .recommended-tech-item:hover,
    .available-tech-item:hover {
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .tech-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .tech-avatar-small {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
    }

    .tech-details {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .tech-name {
      font-weight: 600;
      color: #333;
    }

    .tech-meta {
      display: flex;
      gap: 16px;
      font-size: 12px;
      color: #666;
    }

    .tech-actions {
      display: flex;
      gap: 8px;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 12px;
    }

    /* 公告管理样式 */
    .announcement-tabs {
      border: 1px solid #eee;
      border-radius: 8px;
      overflow: hidden;
    }

    .announcement-switch-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 24px;
      border: 1px solid #e9ecef;
    }

    .announcement-controls {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .control-item {
      display: flex;
      align-items: center;
    }

    .announcement-switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }

    .announcement-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .announcement-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 34px;
    }

    .announcement-slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }

    input:checked + .announcement-slider {
      background-color: #4caf50;
    }

    input:checked + .announcement-slider:before {
      transform: translateX(26px);
    }

    .announcement-content-section {
      background: white;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }

    .announcement-editor textarea {
      font-family: inherit;
      line-height: 1.5;
    }

    .announcement-editor textarea:focus {
      outline: none;
      border-color: #2196f3;
      box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
    }

    /* 滚动速度控制样式 */
    .control-item {
      align-items: center;
    }

    .control-item input[type="range"] {
      -webkit-appearance: none;
      appearance: none;
      height: 6px;
      background: #ddd;
      border-radius: 3px;
      outline: none;
    }

    .control-item input[type="range"]::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 20px;
      height: 20px;
      background: #4caf50;
      border-radius: 50%;
      cursor: pointer;
    }

    .control-item input[type="range"]::-moz-range-thumb {
      width: 20px;
      height: 20px;
      background: #4caf50;
      border-radius: 50%;
      cursor: pointer;
      border: none;
    }
  </style>
</head>
<body>
  <div class="admin-layout">
    <!-- 左侧导航 -->
    <nav class="admin-nav">
      <h3>管理后台</h3>
      <ul>
        <li class="active" id="nav-dashboard">数据看板</li>
        <li id="nav-user">客户管理</li>
        <li id="nav-tech">技师管理</li>
        <li id="nav-recommend">推荐管理</li>
        <li id="nav-announcement">公告管理</li>
        <li id="nav-service">服务管理</li>
        <li id="nav-feed">动态管理</li>
        <li id="nav-taxi">路费管理</li>
        <li id="nav-city">城市管理</li>
        <li id="nav-password">修改密码</li>
        <li id="nav-banner">轮播图管理</li>
        <li id="nav-amap">高德key管理</li>
        <li id="nav-project">项目介绍管理</li>
        <li id="nav-logout" style="color:#ff6a6a;">退出登录</li>
      </ul>
    </nav>

    <!-- 主内容区域 -->
    <div class="admin-content">
      <div class="admin-header">
        <h2 id="admin-title">数据看板</h2>
      </div>
      <div id="admin-main">
        <div class="card">
          <div class="card-title">欢迎使用后台管理系统</div>
          <p>请选择左侧功能菜单进行管理操作。</p>
        </div>
      </div>
    </div>
  </div>

  <!-- JavaScript 代码 -->
  <script src="js/admin-common.js?v=<?php echo time(); ?>"></script>
  <script src="js/admin-navigation.js?v=<?php echo time(); ?>"></script>
  <script src="js/admin-modules.js?v=<?php echo time(); ?>"></script>
  <script>
    // 检查脚本是否正确加载
    document.addEventListener('DOMContentLoaded', function() {
      if (!window.AdminCommon) {
        console.error('AdminCommon not loaded, falling back to inline scripts');
      } else {
        console.log('后台管理系统已加载');
      }
    });
  </script>

  <!-- 内联脚本作为备用 -->
  <script>
    // 如果外部脚本加载失败，使用内联版本
    if (!window.AdminNavigation) {
      console.warn('External scripts failed to load, using inline navigation');

      // 简化的导航功能
      document.addEventListener('DOMContentLoaded', function() {
        const navItems = document.querySelectorAll('.admin-nav li');
        const title = document.getElementById('admin-title');
        const main = document.getElementById('admin-main');

        navItems.forEach(nav => {
          nav.addEventListener('click', () => {
            navItems.forEach(n => n.classList.remove('active'));
            nav.classList.add('active');

            switch(nav.id) {
              case 'nav-logout':
                if (confirm('确定要退出登录吗？')) {
                  window.location.href = 'admin_logout.php';
                }
                break;
              default:
                title.textContent = nav.textContent;
                main.innerHTML = `
                  <div class="card">
                    <div class="card-title">${nav.textContent}</div>
                    <p class="text-muted">功能开发中，请稍后...</p>
                  </div>
                `;
            }
          });
        });
      });
    }
  </script>
</body>
</html>
