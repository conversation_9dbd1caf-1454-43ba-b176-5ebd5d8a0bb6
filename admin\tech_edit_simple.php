<?php
// 简化版技师编辑 - 用于调试
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ob_start();

session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    ob_clean();
    http_response_code(401);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

try {
    require_once '../db.php';
    
    if (!$pdo) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '数据库连接失败']);
        exit;
    }

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '请求方法错误']);
        exit;
    }

    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    
    if ($id <= 0) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '无效的技师ID']);
        exit;
    }

    // 获取基本表单数据（不处理文件上传）
    $nick = isset($_POST['nick']) ? trim($_POST['nick']) : '';
    $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    $age = isset($_POST['age']) ? trim($_POST['age']) : '';
    $height = isset($_POST['height']) ? trim($_POST['height']) : '';
    $weight = isset($_POST['weight']) ? trim($_POST['weight']) : '';
    $size = isset($_POST['size']) ? trim($_POST['size']) : '';
    $model = isset($_POST['model']) ? trim($_POST['model']) : '';
    $city = isset($_POST['city']) ? trim($_POST['city']) : '';
    $hotel = isset($_POST['hotel']) ? trim($_POST['hotel']) : '';
    $virtual_orders = isset($_POST['virtual_orders']) ? intval($_POST['virtual_orders']) : 0;

    // 基本验证
    if (!$nick || !$phone || !$age || !$height || !$weight || !$size || $model === '' || !$city || !$hotel) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '请填写完整信息']);
        exit;
    }

    // 验证虚拟单量
    if ($virtual_orders < 0 || $virtual_orders > 9999) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '虚拟单量必须在0-9999之间']);
        exit;
    }

    // 查询现有技师信息
    $stmt = $pdo->prepare("SELECT * FROM technician WHERE id = ?");
    $stmt->execute([$id]);
    $existingTech = $stmt->fetch();
    
    if (!$existingTech) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '技师不存在']);
        exit;
    }

    // 保持原有的文件路径
    $workimg_path = $existingTech['workimg'];
    $lifeimg_str = $existingTech['lifeimg'];
    $video_path = $existingTech['video'];

    // 更新数据库（不包含文件上传）
    $stmt = $pdo->prepare("UPDATE technician SET nick=?, phone=?, age=?, height=?, weight=?, size=?, model=?, city=?, hotel=?, virtual_orders=? WHERE id=?");
    $result = $stmt->execute([
        $nick, $phone, $age, $height, $weight, $size, $model, $city, $hotel, $virtual_orders, $id
    ]);

    if ($result) {
        ob_clean();
        echo json_encode([
            'success' => true,
            'msg' => "技师资料修改成功（简化版，未处理文件上传）"
        ]);
    } else {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '修改失败']);
    }

} catch (Exception $e) {
    error_log("Tech edit simple error: " . $e->getMessage());
    ob_clean();
    echo json_encode(['success' => false, 'msg' => '修改失败: ' . $e->getMessage()]);
}
?>
