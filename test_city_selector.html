<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>城市选择器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        .city-selector-btn {
            background: linear-gradient(135deg, #00c6a2 0%, #00a085 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .city-selector-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 198, 162, 0.3);
        }
        .current-city {
            display: inline-block;
            margin-left: 10px;
            padding: 8px 16px;
            background: #f0f8ff;
            border: 1px solid #00c6a2;
            border-radius: 20px;
            color: #00c6a2;
            font-weight: bold;
        }
        .feature-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 15px;
        }
        .feature-list h4 {
            margin-top: 0;
            color: #00c6a2;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin-bottom: 8px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏙️ 增强版城市选择器</h1>
        
        <div class="demo-section">
            <h3>🎯 功能演示</h3>
            <p style="color: #666; margin-bottom: 20px;">
                点击下方按钮体验新的城市选择器，包含搜索、热门城市和A-Z快速定位功能。
            </p>
            
            <button class="city-selector-btn" onclick="showCitySelector()">
                📍 选择城市
            </button>
            <span class="current-city" id="current-city">全部城市</span>
            
            <div class="feature-list">
                <h4>✨ 新功能特性</h4>
                <ul>
                    <li><strong>🔍 实时搜索</strong> - 输入关键词快速筛选城市</li>
                    <li><strong>🔥 热门城市</strong> - 优先显示热门大城市，一键选择</li>
                    <li><strong>📝 A-Z索引</strong> - 右侧字母索引，快速定位到指定首字母的城市</li>
                    <li><strong>🎨 高亮显示</strong> - 搜索关键词高亮，当前选中城市特殊标识</li>
                    <li><strong>📱 响应式设计</strong> - 适配手机和桌面端，流畅的交互体验</li>
                    <li><strong>⚡ 智能排序</strong> - 热门城市优先，按拼音首字母分组</li>
                </ul>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>📊 数据来源</h3>
            <p style="color: #666;">
                热门城市数据来自后台管理系统的城市表，自动获取标记为热门的城市。<br>
                如果无法获取热门城市数据，将使用默认的热门城市列表。
            </p>
            <button onclick="testHotCities()" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                测试热门城市API
            </button>
            <div id="api-result" style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-family: monospace; font-size: 12px; display: none;"></div>
        </div>
    </div>

    <script>
        // 模拟可用城市列表
        const availableCities = [
            '全部城市', '北京市', '上海市', '天津市', '重庆市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市', '西安市', '郑州市', '济南市', '青岛市', '大连市', '沈阳市', '长春市', '哈尔滨市', '石家庄市', '太原市', '呼和浩特市', '南昌市', '合肥市', '福州市', '厦门市', '长沙市', '南宁市', '海口市', '贵阳市', '昆明市', '拉萨市', '兰州市', '西宁市', '银川市', '乌鲁木齐市',
            '苏州市', '无锡市', '常州市', '徐州市', '扬州市', '泰州市', '南通市', '盐城市', '淮安市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '台州市', '丽水市', '佛山市', '东莞市', '中山市', '珠海市', '泉州市', '漳州市', '莆田市', '三明市', '龙岩市', '宁德市', '烟台市', '潍坊市', '临沂市', '济宁市', '泰安市', '威海市', '日照市', '淄博市', '枣庄市', '东营市', '聊城市', '德州市', '滨州市', '洛阳市', '开封市', '新乡市', '焦作市', '安阳市', '平顶山市'
        ];

        // 获取热门城市列表
        function getHotCities() {
            return fetch('admin/city_list.php')
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data)) {
                        return data.filter(city => city.is_hot == 1).map(city => city.name);
                    }
                    return ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市'];
                })
                .catch(() => ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市']);
        }

        // 显示城市选择器
        function showCitySelector() {
            const currentCity = document.getElementById('current-city').textContent;
            
            getHotCities().then(hotCities => {
                createCitySelectorModal(availableCities, hotCities, currentCity);
            });
        }

        // 创建城市选择器模态框
        function createCitySelectorModal(cities, hotCities, currentCity) {
            const modal = document.createElement('div');
            modal.className = 'city-selector-modal';
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0,0,0,0.5); z-index: 10000;
                display: flex; align-items: center; justify-content: center;
                padding: 20px;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 16px; width: 100%; max-width: 500px; max-height: 80vh; overflow: hidden; display: flex; flex-direction: column;">
                    <!-- 头部 -->
                    <div style="padding: 20px; text-align: center; font-size: 18px; font-weight: bold; border-bottom: 1px solid #eee; position: relative;">
                        选择城市
                        <button onclick="this.closest('.city-selector-modal').remove()" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); background: none; border: none; font-size: 20px; cursor: pointer; color: #999;">×</button>
                    </div>
                    
                    <!-- 搜索框 -->
                    <div style="padding: 15px; border-bottom: 1px solid #eee;">
                        <div style="position: relative;">
                            <input type="text" id="city-search-input" placeholder="搜索城市..." style="
                                width: 100%; padding: 12px 40px 12px 12px; border: 1px solid #ddd; 
                                border-radius: 8px; font-size: 16px; outline: none;
                                box-sizing: border-box;
                            ">
                            <span style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #999;">🔍</span>
                        </div>
                    </div>
                    
                    <!-- 主体内容 -->
                    <div style="flex: 1; display: flex; overflow: hidden;">
                        <!-- 左侧城市列表 -->
                        <div style="flex: 1; display: flex; flex-direction: column;">
                            <!-- 热门城市 -->
                            <div id="hot-cities-section" style="border-bottom: 1px solid #eee;">
                                <div style="padding: 12px 15px; background: #f8f9fa; font-size: 14px; font-weight: bold; color: #666;">
                                    🔥 热门城市
                                </div>
                                <div id="hot-cities-list" style="padding: 8px;">
                                    ${hotCities.map(city => `
                                        <span onclick="selectCity('${city}')" style="
                                            display: inline-block; margin: 4px; padding: 6px 12px; 
                                            background: ${city === currentCity ? '#00c6a2' : '#f0f0f0'}; 
                                            color: ${city === currentCity ? 'white' : '#333'};
                                            border-radius: 16px; cursor: pointer; font-size: 14px;
                                            transition: all 0.2s ease;
                                        " onmouseover="if('${city}' !== '${currentCity}') this.style.background='#e0e0e0'" 
                                           onmouseout="if('${city}' !== '${currentCity}') this.style.background='#f0f0f0'">
                                            ${city}
                                        </span>
                                    `).join('')}
                                </div>
                            </div>
                            
                            <!-- 所有城市列表 -->
                            <div style="flex: 1; overflow-y: auto;" id="cities-container">
                                <div style="padding: 12px 15px; background: #f8f9fa; font-size: 14px; font-weight: bold; color: #666;">
                                    📍 所有城市
                                </div>
                                <div id="all-cities-list">
                                    ${cities.map(city => `
                                        <div onclick="selectCity('${city}')" style="
                                            padding: 12px 20px; border-bottom: 1px solid #f0f0f0; 
                                            cursor: pointer; transition: background 0.2s ease;
                                            ${city === currentCity ? 'background: #f0f8ff; color: #00c6a2; font-weight: bold;' : ''}
                                        " onmouseover="if('${city}' !== '${currentCity}') this.style.background='#f8f9fa'" 
                                           onmouseout="if('${city}' !== '${currentCity}') this.style.background='transparent'"
                                           data-letter="${getCityFirstLetter(city)}">
                                            ${city}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧A-Z索引 -->
                        <div id="alphabet-index" style="
                            width: 40px; background: #f8f9fa; border-left: 1px solid #eee;
                            display: flex; flex-direction: column; align-items: center; 
                            padding: 10px 0; overflow-y: auto;
                        ">
                            ${generateAlphabetIndex()}
                        </div>
                    </div>
                </div>
            `;

            modal.onclick = function(e) {
                if (e.target === modal) modal.remove();
            };

            document.body.appendChild(modal);
            
            // 初始化搜索功能
            initCitySearch(cities, hotCities, currentCity);
        }

        // 选择城市
        function selectCity(city) {
            document.getElementById('current-city').textContent = city;
            const modal = document.querySelector('.city-selector-modal');
            if (modal) modal.remove();
        }

        // 测试热门城市API
        function testHotCities() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在获取热门城市数据...';
            
            getHotCities().then(hotCities => {
                resultDiv.textContent = `热门城市API测试结果:

获取到 ${hotCities.length} 个热门城市:
${hotCities.join(', ')}

API调用成功！`;
            }).catch(error => {
                resultDiv.textContent = `热门城市API测试失败:
${error.message}

将使用默认热门城市列表。`;
            });
        }

        // 以下是从technicians.html复制的辅助函数
        function generateAlphabetIndex() {
            const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
            return letters.map(letter => `
                <div onclick="scrollToLetter('${letter}')" style="
                    width: 24px; height: 24px; display: flex; align-items: center; 
                    justify-content: center; cursor: pointer; font-size: 12px; 
                    font-weight: bold; color: #666; margin: 1px 0;
                    border-radius: 50%; transition: all 0.2s ease;
                " onmouseover="this.style.background='#00c6a2'; this.style.color='white'" 
                   onmouseout="this.style.background='transparent'; this.style.color='#666'">
                    ${letter}
                </div>
            `).join('');
        }

        function initCitySearch(cities, hotCities, currentCity) {
            const searchInput = document.getElementById('city-search-input');
            const allCitiesList = document.getElementById('all-cities-list');
            const hotCitiesSection = document.getElementById('hot-cities-section');
            
            searchInput.addEventListener('input', function(e) {
                const keyword = e.target.value.trim().toLowerCase();
                
                if (!keyword) {
                    hotCitiesSection.style.display = 'block';
                    allCitiesList.innerHTML = cities.map(city => `
                        <div onclick="selectCity('${city}')" style="
                            padding: 12px 20px; border-bottom: 1px solid #f0f0f0; 
                            cursor: pointer; transition: background 0.2s ease;
                            ${city === currentCity ? 'background: #f0f8ff; color: #00c6a2; font-weight: bold;' : ''}
                        " onmouseover="if('${city}' !== '${currentCity}') this.style.background='#f8f9fa'" 
                           onmouseout="if('${city}' !== '${currentCity}') this.style.background='transparent'"
                           data-letter="${getCityFirstLetter(city)}">
                            ${city}
                        </div>
                    `).join('');
                } else {
                    hotCitiesSection.style.display = 'none';
                    const filteredCities = cities.filter(city => 
                        city.toLowerCase().includes(keyword)
                    );
                    
                    if (filteredCities.length > 0) {
                        allCitiesList.innerHTML = filteredCities.map(city => `
                            <div onclick="selectCity('${city}')" style="
                                padding: 12px 20px; border-bottom: 1px solid #f0f0f0; 
                                cursor: pointer; transition: background 0.2s ease;
                                ${city === currentCity ? 'background: #f0f8ff; color: #00c6a2; font-weight: bold;' : ''}
                            " onmouseover="if('${city}' !== '${currentCity}') this.style.background='#f8f9fa'" 
                               onmouseout="if('${city}' !== '${currentCity}') this.style.background='transparent'">
                                ${highlightSearchKeyword(city, keyword)}
                            </div>
                        `).join('');
                    } else {
                        allCitiesList.innerHTML = `
                            <div style="padding: 40px 20px; text-align: center; color: #999;">
                                <div style="font-size: 48px; margin-bottom: 16px;">🔍</div>
                                <div>未找到匹配的城市</div>
                                <div style="font-size: 14px; margin-top: 8px;">请尝试其他关键词</div>
                            </div>
                        `;
                    }
                }
            });
        }

        function highlightSearchKeyword(text, keyword) {
            if (!keyword) return text;
            const regex = new RegExp(`(${keyword})`, 'gi');
            return text.replace(regex, '<span style="background: #ffeb3b; color: #333;">$1</span>');
        }

        function getCityFirstLetter(cityName) {
            const pinyinMap = {
                '阿': 'A', '安': 'A', '鞍': 'A',
                '北': 'B', '包': 'B', '保': 'B', '蚌': 'B', '本': 'B', '滨': 'B', '亳': 'B',
                '重': 'C', '成': 'C', '长': 'C', '常': 'C', '承': 'C', '沧': 'C', '滁': 'C', '池': 'C', '潮': 'C', '崇': 'C', '赤': 'C', '朝': 'C',
                '大': 'D', '东': 'D', '德': 'D', '丹': 'D', '定': 'D', '都': 'D', '儋': 'D', '达': 'D',
                '鄂': 'E', '恩': 'E',
                '福': 'F', '佛': 'F', '抚': 'F', '阜': 'F', '防': 'F', '丰': 'F',
                '广': 'G', '贵': 'G', '桂': 'G', '赣': 'G', '格': 'G', '固': 'G',
                '杭': 'H', '哈': 'H', '海': 'H', '合': 'H', '河': 'H', '衡': 'H', '呼': 'H', '黄': 'H', '惠': 'H', '湖': 'H', '怀': 'H', '邯': 'H', '汉': 'H', '菏': 'H', '鹤': 'H', '黑': 'H', '红': 'H', '淮': 'H', '华': 'H',
                '济': 'J', '金': 'J', '江': 'J', '嘉': 'J', '九': 'J', '吉': 'J', '锦': 'J', '焦': 'J', '荆': 'J', '景': 'J', '揭': 'J', '晋': 'J',
                '昆': 'K', '开': 'K', '克': 'K', '喀': 'K',
                '兰': 'L', '拉': 'L', '洛': 'L', '连': 'L', '柳': 'L', '六': 'L', '临': 'L', '廊': 'L', '聊': 'L', '辽': 'L', '丽': 'L', '娄': 'L', '漯': 'L', '吕': 'L', '龙': 'L', '陇': 'L', '林': 'L',
                '马': 'M', '梅': 'M', '绵': 'M', '牡': 'M', '茂': 'M', '眉': 'M',
                '南': 'N', '宁': 'N', '内': 'N', '那': 'N',
                '盘': 'P', '平': 'P', '莆': 'P', '濮': 'P', '攀': 'P', '普': 'P',
                '青': 'Q', '秦': 'Q', '齐': 'Q', '泉': 'Q', '衢': 'Q', '庆': 'Q', '钦': 'Q', '曲': 'Q', '清': 'Q',
                '日': 'R', '瑞': 'R',
                '上': 'S', '深': 'S', '苏': 'S', '沈': 'S', '石': 'S', '三': 'S', '绍': 'S', '汕': 'S', '韶': 'S', '商': 'S', '十': 'S', '随': 'S', '宿': 'S', '朔': 'S', '双': 'S', '松': 'S', '遂': 'S', '邵': 'S', '神': 'S', '山': 'S',
                '天': 'T', '太': 'T', '唐': 'T', '台': 'T', '泰': 'T', '通': 'T', '铜': 'T', '铁': 'T', '塔': 'T', '吐': 'T', '图': 'T',
                '乌': 'U', '无': 'W', '武': 'W', '温': 'W', '威': 'W', '潍': 'W', '芜': 'W', '梧': 'W', '万': 'W', '文': 'W', '渭': 'W', '吴': 'W', '五': 'W',
                '西': 'X', '厦': 'X', '徐': 'X', '新': 'X', '襄': 'X', '许': 'X', '宣': 'X', '咸': 'X', '湘': 'X', '孝': 'X', '信': 'X', '忻': 'X', '邢': 'X', '兴': 'X', '锡': 'X',
                '银': 'Y', '宜': 'Y', '扬': 'Y', '烟': 'Y', '盐': 'Y', '营': 'Y', '岳': 'Y', '运': 'Y', '玉': 'Y', '榆': 'Y', '永': 'Y', '益': 'Y', '阳': 'Y', '鹰': 'Y', '伊': 'Y', '延': 'Y', '雅': 'Y', '义': 'Y',
                '郑': 'Z', '珠': 'Z', '中': 'Z', '株': 'Z', '淄': 'Z', '枣': 'Z', '张': 'Z', '湛': 'Z', '肇': 'Z', '镇': 'Z', '周': 'Z', '驻': 'Z', '舟': 'Z', '漳': 'Z', '遵': 'Z', '资': 'Z', '自': 'Z', '昭': 'Z'
            };
            
            const firstChar = cityName.charAt(0);
            return pinyinMap[firstChar] || firstChar.toUpperCase();
        }

        function scrollToLetter(letter) {
            const citiesContainer = document.getElementById('cities-container');
            const targetElement = citiesContainer.querySelector(`[data-letter="${letter}"]`);
            
            if (targetElement) {
                targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                targetElement.style.background = '#e3f2fd';
                setTimeout(() => {
                    targetElement.style.background = '';
                }, 1000);
            }
        }
    </script>
</body>
</html>
