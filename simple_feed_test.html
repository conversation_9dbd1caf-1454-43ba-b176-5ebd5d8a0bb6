<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化技师动态测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .feed-item {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .feed-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .feed-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
        }
        .feed-name {
            font-weight: bold;
            color: #333;
        }
        .feed-time {
            font-size: 12px;
            color: #999;
        }
        .feed-content {
            color: #333;
            margin: 10px 0;
        }
        .feed-details {
            background: #e3f2fd;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            color: #1976d2;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简化技师动态测试</h1>
        
        <div style="margin: 20px 0;">
            <button class="btn" onclick="loadFeeds()">📱 加载技师动态</button>
            <button class="btn" onclick="testAPI()">🔧 测试API</button>
            <button class="btn" onclick="clearResults()">🗑️ 清空结果</button>
        </div>
        
        <div id="debug-info" class="debug-info">
            调试信息将显示在这里...
        </div>
        
        <div id="feed-container">
            <div class="loading">点击"加载技师动态"开始测试</div>
        </div>
    </div>

    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const debugInfo = document.getElementById('debug-info');
            debugInfo.textContent += `[${timestamp}] ${message}\n`;
            console.log(message);
        }
        
        // 加载技师动态
        async function loadFeeds() {
            log('开始加载技师动态...');
            const container = document.getElementById('feed-container');
            container.innerHTML = '<div class="loading">正在加载技师动态...</div>';
            
            try {
                log('发送请求到 technician_feed_list.php');
                const response = await fetch('technician_feed_list.php');
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`API响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success && data.feeds && data.feeds.length > 0) {
                    log(`成功获取 ${data.feeds.length} 条技师动态`);
                    renderFeeds(data.feeds);
                } else {
                    log('没有技师动态数据');
                    container.innerHTML = '<div class="loading">暂无技师动态</div>';
                }
            } catch (error) {
                log(`加载失败: ${error.message}`);
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }
        
        // 渲染技师动态
        function renderFeeds(feeds) {
            log(`开始渲染 ${feeds.length} 条动态`);
            const container = document.getElementById('feed-container');
            
            container.innerHTML = feeds.map((feed, index) => {
                log(`渲染第 ${index + 1} 条动态: ${feed.content}`);
                
                return `
                    <div class="feed-item">
                        <div class="feed-header">
                            <img src="${feed.avatar || 'images/tx.jpg'}" alt="头像" class="feed-avatar" onerror="this.style.background='#ddd'">
                            <div>
                                <div class="feed-name">${feed.nickname || '技师'}</div>
                                <div class="feed-time">${feed.time || ''} · ${feed.location || ''}</div>
                            </div>
                        </div>
                        <div class="feed-content">${feed.content || ''}</div>
                        ${feed.technician_info ? `
                            <div class="feed-details">
                                <strong>${feed.technician_info.action_type === 'move' ? '更换城市' : '新加入'}</strong> 
                                ${feed.technician_info.city}
                                ${feed.technician_info.previous_city ? ` (从${feed.technician_info.previous_city})` : ''}<br>
                                年龄:${feed.technician_info.age} | 身高:${feed.technician_info.height}cm | 
                                体重:${feed.technician_info.weight}kg | 服务年限:${feed.technician_info.service_years}年
                            </div>
                        ` : ''}
                    </div>
                `;
            }).join('');
            
            log('动态渲染完成');
        }
        
        // 测试API
        async function testAPI() {
            log('开始测试API...');
            
            try {
                const response = await fetch('technician_feed_list.php');
                const text = await response.text();
                log(`API原始响应: ${text}`);
                
                try {
                    const data = JSON.parse(text);
                    log('JSON解析成功');
                    log(`数据结构: success=${data.success}, feeds数量=${data.feeds ? data.feeds.length : 0}`);
                } catch (e) {
                    log(`JSON解析失败: ${e.message}`);
                }
            } catch (error) {
                log(`API测试失败: ${error.message}`);
            }
        }
        
        // 清空结果
        function clearResults() {
            document.getElementById('debug-info').textContent = '调试信息已清空...\n';
            document.getElementById('feed-container').innerHTML = '<div class="loading">点击"加载技师动态"开始测试</div>';
        }
        
        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            setTimeout(() => {
                log('自动开始加载技师动态...');
                loadFeeds();
            }, 1000);
        });
    </script>
</body>
</html>
