<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>城市管理 - 后台管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }
        
        .header .breadcrumb {
            margin-top: 8px;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .search-box input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            width: 200px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .alphabet-nav {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .alphabet-nav h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
        }
        
        .alphabet-letters {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .alphabet-letter {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s ease;
            font-size: 14px;
        }
        
        .alphabet-letter:hover {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .alphabet-letter.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .alphabet-letter.has-cities {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .cities-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .cities-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .cities-content {
            padding: 20px;
        }
        
        .city-group {
            margin-bottom: 30px;
        }
        
        .city-group-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .city-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .city-card {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            background: #fff;
            transition: all 0.2s ease;
        }
        
        .city-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .city-card.hot {
            border-color: #ffc107;
            background: #fff8e1;
        }
        
        .city-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .city-name {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .city-badges {
            display: flex;
            gap: 5px;
        }
        
        .badge {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .badge-hot {
            background: #ffc107;
            color: #212529;
        }
        
        .badge-pinyin {
            background: #007bff;
            color: white;
        }
        
        .city-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .city-actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #999;
        }
        
        .error {
            text-align: center;
            padding: 40px;
            color: #dc3545;
            background: #f8d7da;
            border-radius: 8px;
            margin: 20px;
        }
        
        .empty {
            text-align: center;
            padding: 40px;
            color: #999;
        }
        
        @media (max-width: 768px) {
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                justify-content: center;
            }
            
            .search-box input {
                width: 100%;
                max-width: 300px;
            }
            
            .alphabet-letters {
                justify-content: center;
            }
            
            .stats {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .city-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏙️ 城市管理</h1>
        <div class="breadcrumb">后台管理 > 城市管理</div>
    </div>
    
    <div class="container">
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="search-box">
                <input type="text" id="search-input" placeholder="搜索城市名称..." onkeyup="searchCities(event)">
                <button class="btn btn-primary" onclick="searchCities()">🔍 搜索</button>
            </div>
            <div>
                <button class="btn btn-success" onclick="addCity()">➕ 添加城市</button>
                <button class="btn btn-warning" onclick="batchImport()">📥 批量导入</button>
                <button class="btn btn-primary" onclick="refreshCities()">🔄 刷新</button>
            </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-cities">-</div>
                <div class="stat-label">总城市数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="hot-cities">-</div>
                <div class="stat-label">热门城市</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="alphabet-groups">-</div>
                <div class="stat-label">字母分组</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="current-filter">全部</div>
                <div class="stat-label">当前筛选</div>
            </div>
        </div>
        
        <!-- 字母导航 -->
        <div class="alphabet-nav">
            <h3>🔤 按拼音首字母筛选</h3>
            <div class="alphabet-letters" id="alphabet-nav">
                <!-- 字母导航将通过JavaScript生成 -->
            </div>
        </div>
        
        <!-- 城市列表 -->
        <div class="cities-container">
            <div class="cities-header">
                <h3>城市列表 (按拼音A-Z排序)</h3>
                <div>
                    <span id="filtered-count">0</span> 个城市
                </div>
            </div>
            <div class="cities-content" id="cities-content">
                <div class="loading">正在加载城市列表...</div>
            </div>
        </div>
    </div>

    <script>
        let cities = [];
        let filteredCities = [];
        let currentFilter = 'ALL';
        let searchKeyword = '';
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            checkLoginStatus();
            // 加载城市列表
            loadCities();
        });
        
        // 检查登录状态
        function checkLoginStatus() {
            // 这里可以添加登录验证逻辑
            // 暂时跳过，实际使用时需要验证session
        }
        
        // 加载城市列表
        async function loadCities() {
            const content = document.getElementById('cities-content');
            content.innerHTML = '<div class="loading">正在加载城市列表...</div>';
            
            try {
                const response = await fetch('city_list.php');
                const data = await response.json();
                
                if (Array.isArray(data)) {
                    cities = data;
                    applyFilters();
                    updateStats();
                    generateAlphabetNav();
                    renderCities();
                } else {
                    throw new Error('返回数据格式错误');
                }
            } catch (error) {
                console.error('加载城市失败:', error);
                content.innerHTML = `
                    <div class="error">
                        <h4>❌ 加载失败</h4>
                        <p>${error.message}</p>
                        <button class="btn btn-primary" onclick="loadCities()">重新加载</button>
                    </div>
                `;
            }
        }
        
        // 应用筛选条件
        function applyFilters() {
            filteredCities = cities.filter(city => {
                // 字母筛选
                const letterMatch = currentFilter === 'ALL' || city.pinyin_first === currentFilter;
                
                // 关键词搜索
                const keywordMatch = !searchKeyword || 
                    city.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
                    city.pinyin_first.toLowerCase().includes(searchKeyword.toLowerCase());
                
                return letterMatch && keywordMatch;
            });
        }
        
        // 渲染城市列表
        function renderCities() {
            const content = document.getElementById('cities-content');
            
            if (filteredCities.length === 0) {
                content.innerHTML = `
                    <div class="empty">
                        <h4>📭 暂无城市</h4>
                        <p>没有找到符合条件的城市</p>
                    </div>
                `;
                return;
            }
            
            // 按字母分组
            const groupedCities = {};
            filteredCities.forEach(city => {
                const letter = city.pinyin_first;
                if (!groupedCities[letter]) {
                    groupedCities[letter] = [];
                }
                groupedCities[letter].push(city);
            });
            
            // 生成HTML
            let html = '';
            const letters = Object.keys(groupedCities).sort();
            
            letters.forEach(letter => {
                const citiesInGroup = groupedCities[letter];
                html += `
                    <div class="city-group" id="group-${letter}">
                        <div class="city-group-header">
                            <span>${letter} 字母</span>
                            <span>${citiesInGroup.length} 个城市</span>
                        </div>
                        <div class="city-grid">
                            ${citiesInGroup.map(city => renderCityCard(city)).join('')}
                        </div>
                    </div>
                `;
            });
            
            content.innerHTML = html;
            updateFilteredCount();
        }
        
        // 渲染城市卡片
        function renderCityCard(city) {
            return `
                <div class="city-card ${city.is_hot ? 'hot' : ''}">
                    <div class="city-header">
                        <div class="city-name">${city.name}</div>
                        <div class="city-badges">
                            <span class="badge badge-pinyin">${city.pinyin_first}</span>
                            ${city.is_hot ? '<span class="badge badge-hot">⭐ 热门</span>' : ''}
                        </div>
                    </div>
                    <div class="city-info">
                        ID: ${city.id} | 
                        坐标: ${city.lng ? city.lng.toFixed(4) : 'N/A'}, ${city.lat ? city.lat.toFixed(4) : 'N/A'} |
                        创建: ${city.created_at ? new Date(city.created_at).toLocaleDateString() : 'N/A'}
                    </div>
                    <div class="city-actions">
                        <button class="btn btn-sm ${city.is_hot ? 'btn-warning' : 'btn-success'}" 
                                onclick="toggleHot(${city.id}, ${city.is_hot})">
                            ${city.is_hot ? '取消热门' : '设为热门'}
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editCity(${city.id})">编辑</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteCity(${city.id}, '${city.name}')">删除</button>
                    </div>
                </div>
            `;
        }
        
        // 更新统计信息
        function updateStats() {
            const totalCities = cities.length;
            const hotCities = cities.filter(city => city.is_hot).length;
            const alphabetGroups = new Set(cities.map(city => city.pinyin_first)).size;
            
            document.getElementById('total-cities').textContent = totalCities;
            document.getElementById('hot-cities').textContent = hotCities;
            document.getElementById('alphabet-groups').textContent = alphabetGroups;
            document.getElementById('current-filter').textContent = currentFilter === 'ALL' ? '全部' : currentFilter;
        }
        
        // 更新筛选后的数量
        function updateFilteredCount() {
            document.getElementById('filtered-count').textContent = filteredCities.length;
        }
        
        // 生成字母导航
        function generateAlphabetNav() {
            const nav = document.getElementById('alphabet-nav');
            const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
            const usedLetters = new Set(cities.map(city => city.pinyin_first));
            
            nav.innerHTML = `
                <div class="alphabet-letter ${currentFilter === 'ALL' ? 'active' : ''}" onclick="filterByLetter('ALL')">
                    全部
                </div>
                ${letters.map(letter => `
                    <div class="alphabet-letter ${usedLetters.has(letter) ? 'has-cities' : ''} ${currentFilter === letter ? 'active' : ''}" 
                         onclick="filterByLetter('${letter}')"
                         title="${usedLetters.has(letter) ? `${getCitiesCountByLetter(letter)}个城市` : '无城市'}">
                        ${letter}
                    </div>
                `).join('')}
            `;
        }
        
        // 按字母筛选
        function filterByLetter(letter) {
            currentFilter = letter;
            applyFilters();
            updateStats();
            generateAlphabetNav();
            renderCities();
            
            // 滚动到对应分组
            if (letter !== 'ALL') {
                setTimeout(() => {
                    const group = document.getElementById(`group-${letter}`);
                    if (group) {
                        group.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                }, 100);
            }
        }
        
        // 搜索城市
        function searchCities(event) {
            if (event && event.key && event.key !== 'Enter') return;
            
            searchKeyword = document.getElementById('search-input').value.trim();
            applyFilters();
            renderCities();
            updateFilteredCount();
        }
        
        // 获取指定字母的城市数量
        function getCitiesCountByLetter(letter) {
            return cities.filter(city => city.pinyin_first === letter).length;
        }
        
        // 切换热门状态
        async function toggleHot(cityId, isCurrentlyHot) {
            try {
                const response = await fetch('city_set_hot.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: cityId,
                        is_hot: isCurrentlyHot ? 0 : 1
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // 更新本地数据
                    const city = cities.find(c => c.id === cityId);
                    if (city) {
                        city.is_hot = !isCurrentlyHot;
                    }
                    
                    // 重新渲染
                    applyFilters();
                    updateStats();
                    renderCities();
                    
                    alert(isCurrentlyHot ? '已取消热门' : '已设为热门');
                } else {
                    alert('操作失败: ' + result.msg);
                }
            } catch (error) {
                alert('操作失败: ' + error.message);
            }
        }
        
        // 添加城市
        function addCity() {
            // 这里可以打开添加城市的模态框或跳转到添加页面
            alert('添加城市功能开发中...');
        }
        
        // 编辑城市
        function editCity(cityId) {
            // 这里可以打开编辑城市的模态框或跳转到编辑页面
            alert(`编辑城市 ID: ${cityId} 功能开发中...`);
        }
        
        // 删除城市
        function deleteCity(cityId, cityName) {
            if (confirm(`确定要删除城市 "${cityName}" 吗？此操作不可恢复！`)) {
                // 这里调用删除API
                alert(`删除城市 "${cityName}" 功能开发中...`);
            }
        }
        
        // 批量导入
        function batchImport() {
            alert('批量导入功能开发中...');
        }
        
        // 刷新城市列表
        function refreshCities() {
            loadCities();
        }
    </script>
</body>
</html>
