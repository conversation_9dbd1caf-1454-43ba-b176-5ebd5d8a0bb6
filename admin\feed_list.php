<?php
// feed_list.php - 动态列表
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

try {
    // 自动建表（如无则创建）
    $pdo->exec("CREATE TABLE IF NOT EXISTS feeds (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL COMMENT '用户ID',
        content TEXT NOT NULL COMMENT '动态内容',
        images TEXT DEFAULT NULL COMMENT '图片列表(JSON格式)',
        video VARCHAR(255) DEFAULT NULL COMMENT '视频文件',
        location VARCHAR(100) DEFAULT NULL COMMENT '位置信息',
        likes_count INT DEFAULT 0 COMMENT '点赞数',
        comments_count INT DEFAULT 0 COMMENT '评论数',
        is_public TINYINT(1) DEFAULT 1 COMMENT '是否公开(1=公开,0=私密)',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) CHARSET=utf8mb4 COMMENT='用户动态表';");

    // 查询所有动态，关联用户信息
    $stmt = $pdo->query("
        SELECT
            f.id, f.user_id, f.content, f.images, f.video, f.location,
            f.likes_count, f.comments_count, f.is_public, f.created_at, f.updated_at,
            u.nickname as user_nick, u.phone as user_phone, u.avatar as user_avatar
        FROM feeds f
        LEFT JOIN user u ON f.user_id = u.id
        ORDER BY f.created_at DESC
    ");
    $data = $stmt ? $stmt->fetchAll() : [];
    
    // 处理数据格式
    foreach ($data as &$row) {
        // 处理图片列表
        if ($row['images']) {
            $row['images_array'] = json_decode($row['images'], true) ?: [];
        } else {
            $row['images_array'] = [];
        }
        
        // 格式化时间
        $row['created_time'] = date('Y-m-d H:i:s', strtotime($row['created_at']));
        $row['time_ago'] = getTimeAgo($row['created_at']);
        
        // 处理用户信息
        $row['user_nick'] = $row['user_nick'] ?: '未知用户';
        $row['user_phone'] = $row['user_phone'] ?: '';
        
        // 格式化状态
        $row['status_text'] = $row['is_public'] ? '公开' : '私密';
        
        // 截取内容预览
        $row['content_preview'] = strlen($row['content']) > 100 ?
            substr($row['content'], 0, 100) . '...' : $row['content'];
    }

    echo json_encode([
        'success' => true,
        'data' => $data,
        'total' => count($data)
    ]);

} catch (Exception $e) {
    error_log("Feed list error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'msg' => '获取动态列表失败: ' . $e->getMessage(),
        'data' => []
    ]);
}

// 时间差计算函数
function getTimeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return '刚刚';
    } elseif ($time < 3600) {
        return floor($time / 60) . '分钟前';
    } elseif ($time < 86400) {
        return floor($time / 3600) . '小时前';
    } elseif ($time < 2592000) {
        return floor($time / 86400) . '天前';
    } else {
        return date('Y-m-d', strtotime($datetime));
    }
}
?>
