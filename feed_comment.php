<?php
// feed_comment.php
header('Content-Type: application/json; charset=utf-8');
require_once 'db.php';

$user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
$feed_id = isset($_POST['feed_id']) ? intval($_POST['feed_id']) : 0;
$content = isset($_POST['content']) ? trim($_POST['content']) : '';
if (!$user_id || !$feed_id || !$content) {
    echo json_encode(['success'=>false, 'msg'=>'缺少参数']);
    exit;
}
// 评论表结构: feed_comment(id, feed_id, user_id, content, created_at)
$sql = "INSERT INTO feed_comment (feed_id, user_id, content, created_at) VALUES (?, ?, ?, NOW())";
$stmt = $pdo->prepare($sql);
$stmt->execute([$feed_id, $user_id, $content]);
echo json_encode(['success'=>true, 'msg'=>'评论成功']);
