<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>城市拼音排序最终测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .comparison-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
        .comparison-item h4 {
            margin-top: 0;
            color: #007bff;
        }
        .city-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 10px;
            background: #fafafa;
        }
        .city-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            margin: 2px 0;
            background: white;
            border-radius: 4px;
            border: 1px solid #eee;
        }
        .city-item.hot {
            background: #fff8e1;
            border-color: #ffc107;
        }
        .city-name {
            font-weight: bold;
        }
        .city-pinyin {
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 12px;
        }
        .city-hot {
            color: #ffc107;
            font-size: 14px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin: 15px 0;
            padding: 15px;
            background: #e3f2fd;
            border-radius: 8px;
        }
        .stat-item {
            text-align: center;
            flex: 1;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        .error {
            text-align: center;
            padding: 20px;
            color: #dc3545;
            background: #f8d7da;
            border-radius: 8px;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #c3e6cb;
        }
        .alphabet-summary {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 15px 0;
        }
        .alphabet-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
        }
        .alphabet-item.has-cities {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏙️ 城市拼音排序功能测试</h1>
        
        <div class="section">
            <h3>📋 功能说明</h3>
            <p style="line-height: 1.6; color: #666;">
                本测试页面验证后台城市管理的拼音排序功能。城市列表现在按以下规则排序：<br>
                <strong>1. 热门城市优先</strong> - 标记为热门的城市显示在最前面<br>
                <strong>2. 拼音首字母A-Z排序</strong> - 按城市名称的拼音首字母排序<br>
                <strong>3. 同字母内按名称排序</strong> - 相同首字母的城市按名称排序
            </p>
            
            <div class="stats" id="stats">
                <div class="stat-item">
                    <div class="stat-number" id="total-cities">-</div>
                    <div class="stat-label">总城市数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="hot-cities">-</div>
                    <div class="stat-label">热门城市</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="alphabet-groups">-</div>
                    <div class="stat-label">字母分组</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="sort-status">-</div>
                    <div class="stat-label">排序状态</div>
                </div>
            </div>
            
            <div>
                <button class="btn" onclick="loadAndTest()">🔄 重新测试</button>
                <button class="btn success" onclick="openCityManagement()">🔗 打开城市管理</button>
                <button class="btn" onclick="showAlphabetSummary()">🔤 字母分布</button>
            </div>
        </div>
        
        <div class="section">
            <h3>🔤 字母分布统计</h3>
            <div id="alphabet-summary" class="alphabet-summary">
                <!-- 字母分布将通过JavaScript生成 -->
            </div>
        </div>
        
        <div class="section">
            <h3>📊 排序对比</h3>
            <div class="comparison">
                <div class="comparison-item">
                    <h4>🔄 新排序 (热门+拼音A-Z)</h4>
                    <div id="new-sort-list" class="city-list">
                        <div class="loading">正在加载...</div>
                    </div>
                </div>
                <div class="comparison-item">
                    <h4>📝 旧排序 (热门+ID)</h4>
                    <div id="old-sort-list" class="city-list">
                        <div class="loading">正在加载...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>✅ 排序验证结果</h3>
            <div id="validation-result">
                <div class="loading">等待测试...</div>
            </div>
        </div>
    </div>

    <script>
        let cities = [];
        
        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            loadAndTest();
        });
        
        // 加载并测试
        async function loadAndTest() {
            try {
                await loadCities();
                validateSorting();
                showComparison();
                updateStats();
                showAlphabetSummary();
            } catch (error) {
                console.error('测试失败:', error);
            }
        }
        
        // 加载城市列表
        async function loadCities() {
            try {
                const response = await fetch('admin/city_list.php');
                const data = await response.json();
                
                if (Array.isArray(data)) {
                    cities = data;
                    console.log('城市数据加载成功:', cities.length, '个城市');
                } else {
                    throw new Error('返回数据格式错误');
                }
            } catch (error) {
                console.error('加载城市失败:', error);
                throw error;
            }
        }
        
        // 验证排序
        function validateSorting() {
            const resultDiv = document.getElementById('validation-result');
            let isValid = true;
            let errors = [];
            
            // 检查热门城市是否在前面
            let hotCitiesEnded = false;
            for (let i = 0; i < cities.length; i++) {
                const city = cities[i];
                
                if (!city.is_hot && !hotCitiesEnded) {
                    hotCitiesEnded = true;
                } else if (city.is_hot && hotCitiesEnded) {
                    errors.push(`热门城市排序错误: ${city.name} 应该在前面`);
                    isValid = false;
                }
            }
            
            // 检查拼音排序
            for (let i = 1; i < cities.length; i++) {
                const prev = cities[i - 1];
                const curr = cities[i];
                
                // 在相同热门状态下检查拼音排序
                if (prev.is_hot === curr.is_hot) {
                    if (prev.pinyin_first > curr.pinyin_first) {
                        errors.push(`拼音排序错误: ${prev.name}(${prev.pinyin_first}) 应该在 ${curr.name}(${curr.pinyin_first}) 之后`);
                        isValid = false;
                    } else if (prev.pinyin_first === curr.pinyin_first && prev.name > curr.name) {
                        errors.push(`同字母排序错误: ${prev.name} 应该在 ${curr.name} 之后`);
                        isValid = false;
                    }
                }
            }
            
            // 显示验证结果
            if (isValid) {
                resultDiv.innerHTML = `
                    <div class="success-message">
                        <h4>✅ 排序验证通过</h4>
                        <p>城市列表排序完全正确！</p>
                        <ul>
                            <li>热门城市优先排序 ✓</li>
                            <li>拼音首字母A-Z排序 ✓</li>
                            <li>同字母内名称排序 ✓</li>
                        </ul>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 排序验证失败</h4>
                        <p>发现 ${errors.length} 个排序问题：</p>
                        <ul>
                            ${errors.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
            
            return isValid;
        }
        
        // 显示排序对比
        function showComparison() {
            // 新排序（当前API返回的）
            const newSortDiv = document.getElementById('new-sort-list');
            newSortDiv.innerHTML = cities.map(city => `
                <div class="city-item ${city.is_hot ? 'hot' : ''}">
                    <div>
                        <span class="city-name">${city.name}</span>
                        <span style="font-size: 12px; color: #666; margin-left: 8px;">ID: ${city.id}</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 5px;">
                        <span class="city-pinyin">${city.pinyin_first}</span>
                        ${city.is_hot ? '<span class="city-hot">⭐</span>' : ''}
                    </div>
                </div>
            `).join('');
            
            // 旧排序（模拟按ID排序）
            const oldSortCities = [...cities].sort((a, b) => {
                // 热门优先，然后按ID排序
                if (a.is_hot !== b.is_hot) {
                    return b.is_hot - a.is_hot;
                }
                return a.id - b.id;
            });
            
            const oldSortDiv = document.getElementById('old-sort-list');
            oldSortDiv.innerHTML = oldSortCities.map(city => `
                <div class="city-item ${city.is_hot ? 'hot' : ''}">
                    <div>
                        <span class="city-name">${city.name}</span>
                        <span style="font-size: 12px; color: #666; margin-left: 8px;">ID: ${city.id}</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 5px;">
                        <span class="city-pinyin">${city.pinyin_first}</span>
                        ${city.is_hot ? '<span class="city-hot">⭐</span>' : ''}
                    </div>
                </div>
            `).join('');
        }
        
        // 更新统计信息
        function updateStats() {
            const totalCities = cities.length;
            const hotCities = cities.filter(city => city.is_hot).length;
            const alphabetGroups = new Set(cities.map(city => city.pinyin_first)).size;
            const sortStatus = validateSorting() ? '正确' : '错误';
            
            document.getElementById('total-cities').textContent = totalCities;
            document.getElementById('hot-cities').textContent = hotCities;
            document.getElementById('alphabet-groups').textContent = alphabetGroups;
            document.getElementById('sort-status').textContent = sortStatus;
            document.getElementById('sort-status').style.color = sortStatus === '正确' ? '#28a745' : '#dc3545';
        }
        
        // 显示字母分布
        function showAlphabetSummary() {
            const alphabetDiv = document.getElementById('alphabet-summary');
            const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
            
            // 统计每个字母的城市数量
            const letterCounts = {};
            cities.forEach(city => {
                const letter = city.pinyin_first;
                letterCounts[letter] = (letterCounts[letter] || 0) + 1;
            });
            
            alphabetDiv.innerHTML = letters.map(letter => {
                const count = letterCounts[letter] || 0;
                return `
                    <div class="alphabet-item ${count > 0 ? 'has-cities' : ''}">
                        ${letter}: ${count}
                    </div>
                `;
            }).join('');
        }
        
        // 打开城市管理页面
        function openCityManagement() {
            window.open('admin/city_management.html', '_blank');
        }
        
        // 测试拼音映射
        function testPinyinMapping() {
            console.log('拼音映射测试:');
            const testCities = ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市', '西安市', '重庆市'];
            
            testCities.forEach(cityName => {
                // 这里需要调用后端的拼音映射函数，暂时用前端模拟
                const firstChar = cityName.charAt(0);
                console.log(`${cityName} -> ${firstChar} -> 需要后端映射`);
            });
        }
    </script>
</body>
</html>
