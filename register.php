<?php
// 禁用错误输出到页面，防止破坏JSON格式
error_reporting(0);
ini_set('display_errors', 0);

header('Content-Type: application/json; charset=utf-8');

$host = 'localhost';
$db   = '7spa';
$user = 'root';
$pass = '';
$charset = 'utf8mb4';

$phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
$password = isset($_POST['password']) ? trim($_POST['password']) : '';

if (!$phone || !$password) {
    echo json_encode(['success' => false, 'msg' => '手机号和密码不能为空']);
    exit;
}

$dsn = "mysql:host=$host;dbname=$db;charset=$charset";
try {
    $pdo = new PDO($dsn, $user, $pass, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
    // 检查手机号是否已注册
    $stmt = $pdo->prepare("SELECT id FROM user WHERE phone = ?");
    $stmt->execute([$phone]);
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'msg' => '手机号已注册']);
        exit;
    }
    // 插入新用户（同时保存明文密码）
    $stmt = $pdo->prepare("INSERT INTO user (phone, password, password_plain) VALUES (?, ?, ?)");
    $stmt->execute([$phone, password_hash($password, PASSWORD_DEFAULT), $password]);
    echo json_encode(['success' => true, 'msg' => '注册成功']);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'msg' => '数据库错误']);
}
?>
