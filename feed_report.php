<?php
// feed_report.php
header('Content-Type: application/json; charset=utf-8');
require_once 'db.php';

$user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
$feed_id = isset($_POST['feed_id']) ? intval($_POST['feed_id']) : 0;
$reason = isset($_POST['reason']) ? trim($_POST['reason']) : '';
if (!$user_id || !$feed_id) {
    echo json_encode(['success'=>false, 'msg'=>'缺少参数']);
    exit;
}
// 举报表结构: feed_report(id, feed_id, user_id, reason, created_at)
$sql = "INSERT INTO feed_report (feed_id, user_id, reason, created_at) VALUES (?, ?, ?, NOW())";
$stmt = $pdo->prepare($sql);
$stmt->execute([$feed_id, $user_id, $reason]);
echo json_encode(['success'=>true, 'msg'=>'举报成功']);
