<?php
// reset_cities.php - 清理并重新导入热门城市
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

try {
    // 第一步：清空现有城市数据
    $pdo->exec("DELETE FROM city");
    $pdo->exec("ALTER TABLE city AUTO_INCREMENT = 1"); // 重置自增ID
    
    // 第二步：导入精选的热门城市
    $hotCities = [
        // 直辖市（4个）
        ['北京市', 116.4074, 39.9042, 1],
        ['上海市', 121.4737, 31.2304, 1],
        ['天津市', 117.1901, 39.1084, 1],
        ['重庆市', 106.5516, 29.5630, 1],
        
        // 省会城市和副省级城市（31个）
        ['广州市', 113.2644, 23.1291, 1],
        ['深圳市', 114.0579, 22.5431, 1],
        ['杭州市', 120.1551, 30.2741, 1],
        ['南京市', 118.7969, 32.0603, 1],
        ['武汉市', 114.2985, 30.5844, 1],
        ['成都市', 104.0665, 30.5723, 1],
        ['西安市', 108.9398, 34.3416, 1],
        ['郑州市', 113.6254, 34.7466, 1],
        ['济南市', 117.0009, 36.6758, 1],
        ['青岛市', 120.3826, 36.0671, 1],
        ['大连市', 121.6147, 38.9140, 1],
        ['沈阳市', 123.4315, 41.8057, 1],
        ['长春市', 125.3245, 43.8171, 1],
        ['哈尔滨市', 126.5358, 45.8023, 1],
        ['石家庄市', 114.5149, 38.0428, 1],
        ['太原市', 112.5489, 37.8706, 1],
        ['呼和浩特市', 111.7519, 40.8414, 1],
        ['南昌市', 115.8921, 28.6765, 1],
        ['合肥市', 117.2272, 31.8206, 1],
        ['福州市', 119.2965, 26.0745, 1],
        ['厦门市', 118.0894, 24.4798, 1],
        ['长沙市', 112.9388, 28.2282, 1],
        ['南宁市', 108.3669, 22.8170, 1],
        ['海口市', 110.3312, 20.0311, 1],
        ['贵阳市', 106.7135, 26.5783, 1],
        ['昆明市', 102.8329, 24.8801, 1],
        ['拉萨市', 91.1409, 29.6456, 1],
        ['兰州市', 103.8236, 36.0581, 1],
        ['西宁市', 101.7782, 36.6171, 1],
        ['银川市', 106.2309, 38.4872, 1],
        ['乌鲁木齐市', 87.6177, 43.7928, 1],
        
        // 经济发达的地级市（85个）
        // 江苏省
        ['苏州市', 120.6519, 31.3989, 0],
        ['无锡市', 120.3019, 31.5747, 0],
        ['常州市', 119.9772, 31.7728, 0],
        ['徐州市', 117.1836, 34.2616, 0],
        ['扬州市', 119.4215, 32.3932, 0],
        ['泰州市', 119.9153, 32.4849, 0],
        ['南通市', 120.8644, 32.0162, 0],
        ['盐城市', 120.1633, 33.3775, 0],
        ['淮安市', 119.0153, 33.5975, 0],
        
        // 浙江省
        ['宁波市', 121.5440, 29.8683, 0],
        ['温州市', 120.6994, 27.9944, 0],
        ['嘉兴市', 120.7554, 30.7469, 0],
        ['湖州市', 120.0865, 30.8936, 0],
        ['绍兴市', 120.5820, 29.9971, 0],
        ['金华市', 119.6491, 29.0895, 0],
        ['台州市', 121.4287, 28.6561, 0],
        ['丽水市', 119.9220, 28.4517, 0],
        
        // 广东省
        ['佛山市', 113.1220, 23.0288, 0],
        ['东莞市', 113.7518, 23.0489, 0],
        ['中山市', 113.3823, 22.5211, 0],
        ['珠海市', 113.5767, 22.2707, 0],
        
        // 福建省
        ['泉州市', 118.5751, 24.9139, 0],
        ['漳州市', 117.6758, 24.5109, 0],
        ['莆田市', 119.0077, 25.4310, 0],
        ['三明市', 117.6087, 26.2654, 0],
        ['龙岩市', 117.0297, 25.0918, 0],
        ['宁德市', 119.5270, 26.6590, 0],
        
        // 山东省
        ['烟台市', 121.3914, 37.5393, 0],
        ['潍坊市', 119.1070, 36.7093, 0],
        ['临沂市', 118.3269, 35.1045, 0],
        ['济宁市', 116.5873, 35.4154, 0],
        ['泰安市', 117.1289, 36.1948, 0],
        ['威海市', 122.1201, 37.5097, 0],
        ['日照市', 119.4610, 35.4164, 0],
        ['淄博市', 118.0371, 36.8134, 0],
        ['枣庄市', 117.5578, 34.8564, 0],
        ['东营市', 118.6748, 37.4341, 0],
        ['聊城市', 115.9851, 36.4560, 0],
        ['德州市', 116.3073, 37.4341, 0],
        ['滨州市', 118.0371, 37.3835, 0],
        
        // 河南省
        ['洛阳市', 112.4540, 34.6197, 0],
        ['开封市', 114.3411, 34.7971, 0],
        ['新乡市', 113.9268, 35.3030, 0],
        ['焦作市', 113.2418, 35.2158, 0],
        ['安阳市', 114.3927, 36.1034, 0],
        ['平顶山市', 113.1929, 33.7453, 0],
        
        // 湖北省
        ['宜昌市', 111.2905, 30.7019, 0],
        ['襄阳市', 112.1441, 32.0420, 0],
        ['荆州市', 112.2386, 30.3269, 0],
        ['黄石市', 115.0770, 30.2200, 0],
        ['十堰市', 110.7982, 32.6469, 0],
        
        // 湖南省
        ['株洲市', 113.1518, 27.8274, 0],
        ['湘潭市', 112.9445, 27.8294, 0],
        ['衡阳市', 112.6072, 26.8934, 0],
        ['岳阳市', 113.1287, 29.3570, 0],
        ['常德市', 111.6990, 29.0402, 0],
        ['张家界市', 110.4790, 29.1274, 0],
        
        // 其他重要城市
        ['柳州市', 109.4281, 24.3146, 0],
        ['桂林市', 110.2993, 25.2342, 0],
        ['北海市', 109.1193, 21.4733, 0],
        ['三亚市', 109.5082, 18.2479, 0],
        ['绵阳市', 104.6419, 31.4640, 0],
        ['德阳市', 104.3982, 31.1270, 0],
        ['宜宾市', 104.6308, 28.7602, 0],
        ['遵义市', 106.9348, 27.7253, 0],
        ['大理市', 100.2670, 25.6064, 0],
        ['丽江市', 100.2330, 26.8721, 0],
        ['宝鸡市', 107.1826, 34.3640, 0],
        ['咸阳市', 108.7093, 34.3336, 0],
        ['包头市', 109.9402, 40.6522, 0],
        ['鄂尔多斯市', 109.9900, 39.8172, 0],
        ['大庆市', 125.1031, 46.5907, 0],
        ['齐齐哈尔市', 123.9180, 47.3420, 0],
        ['吉林市', 126.5502, 43.8436, 0],
        ['鞍山市', 122.9951, 41.1106, 0],
        ['抚顺市', 123.9570, 41.8654, 0],
        ['唐山市', 118.1758, 39.6304, 0],
        ['保定市', 115.4648, 38.8971, 0],
        ['廊坊市', 116.7039, 39.5231, 0],
        ['秦皇岛市', 119.5881, 39.9425, 0],
        ['大同市', 113.3007, 40.0769, 0],
        ['芜湖市', 118.3760, 31.3262, 0],
        ['蚌埠市', 117.3889, 32.9169, 0],
        ['九江市', 115.9929, 29.7196, 0],
        ['赣州市', 114.9400, 25.8312, 0],
        ['克拉玛依市', 84.8739, 45.5959, 0]
    ];
    
    $successCount = 0;
    $errorCount = 0;
    $errors = [];
    
    // 插入新的热门城市数据
    $insertStmt = $pdo->prepare("INSERT INTO city (name, lng, lat, is_hot, created_at) VALUES (?, ?, ?, ?, NOW())");
    
    foreach ($hotCities as $cityData) {
        try {
            if ($insertStmt->execute($cityData)) {
                $successCount++;
            } else {
                $errorCount++;
                $errors[] = "插入 {$cityData[0]} 失败";
            }
        } catch (Exception $e) {
            $errorCount++;
            $errors[] = "处理 {$cityData[0]} 时出错: " . $e->getMessage();
        }
    }
    
    echo json_encode([
        'success' => true,
        'msg' => "城市数据重置完成",
        'data' => [
            'total' => count($hotCities),
            'success' => $successCount,
            'errors' => $errorCount,
            'error_details' => $errors,
            'hot_cities' => array_filter($hotCities, function($city) { return $city[3] == 1; }),
            'hot_count' => count(array_filter($hotCities, function($city) { return $city[3] == 1; }))
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'msg' => '重置城市数据失败: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
