<?php
// service_list.php - 服务项目列表
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

try {
    // 自动建表（如无则创建）
    $pdo->exec("CREATE TABLE IF NOT EXISTS services (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL COMMENT '项目名称',
        image VARCHAR(255) DEFAULT NULL COMMENT '项目图片',
        service_code VARCHAR(50) NOT NULL COMMENT '项目编号',
        duration INT NOT NULL COMMENT '项目时长(分钟)',
        price DECIMAL(10,2) NOT NULL COMMENT '项目价格',
        virtual_sales INT DEFAULT 0 COMMENT '虚拟购买量',
        service_type VARCHAR(20) DEFAULT 'spa' COMMENT '项目类型(spa=会所项目,studio=工作室项目,ts=TS项目,cd=CD项目)',
        sort_order INT DEFAULT 0 COMMENT '排序顺序(数字越小越靠前)',
        is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活(1=激活,0=停用)',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) CHARSET=utf8mb4 COMMENT='服务项目表';");

    // 检查并添加新字段（兼容已有数据）
    try {
        $pdo->exec("ALTER TABLE services ADD COLUMN service_type VARCHAR(20) DEFAULT 'spa' COMMENT '项目类型' AFTER virtual_sales");
    } catch (Exception $e) {
        // 字段已存在，忽略错误
    }

    try {
        $pdo->exec("ALTER TABLE services ADD COLUMN sort_order INT DEFAULT 0 COMMENT '排序顺序' AFTER service_type");
    } catch (Exception $e) {
        // 字段已存在，忽略错误
    }

    // 移除service_code的唯一性约束（如果存在）
    try {
        // 查询是否存在唯一性约束
        $stmt = $pdo->query("SHOW INDEX FROM services WHERE Column_name = 'service_code' AND Non_unique = 0");
        if ($stmt->fetch()) {
            // 存在唯一性约束，删除它
            $pdo->exec("ALTER TABLE services DROP INDEX service_code");
        }
    } catch (Exception $e) {
        // 约束不存在或删除失败，忽略错误
    }

    // 查询所有服务项目，按排序字段排序
    $stmt = $pdo->query("SELECT id, name, image, service_code, duration, price, virtual_sales, service_type, sort_order, is_active, created_at, updated_at FROM services ORDER BY sort_order ASC, id DESC");
    $data = $stmt ? $stmt->fetchAll() : [];

    $fields = ['id', 'name', 'image', 'service_code', 'duration', 'price', 'virtual_sales', 'service_type', 'sort_order', 'is_active', 'created_at', 'updated_at'];

    // 处理数据格式
    foreach ($data as &$row) {
        // 确保所有字段都存在
        foreach ($fields as $field) {
            if (!isset($row[$field])) {
                $row[$field] = '';
            }
        }
        
        // 格式化价格
        $row['price'] = number_format($row['price'], 2);
        
        // 格式化时长
        $row['duration_text'] = $row['duration'] . '分钟';

        // 格式化状态
        $row['status_text'] = $row['is_active'] ? '激活' : '停用';

        // 格式化项目类型
        $typeMap = [
            'spa' => '会所项目',
            'studio' => '工作室项目',
            'ts' => 'TS项目',
            'cd' => 'CD项目'
        ];
        $row['service_type_text'] = $typeMap[$row['service_type']] ?? '会所项目';

        // 确保排序字段存在
        $row['sort_order'] = $row['sort_order'] ?? 0;
    }

    echo json_encode([
        'success' => true,
        'data' => $data,
        'total' => count($data)
    ]);

} catch (Exception $e) {
    error_log("Service list error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'msg' => '获取服务列表失败: ' . $e->getMessage(),
        'data' => []
    ]);
}
?>
