<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技师编辑城市选择器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .btn.warning:hover {
            background: #e0a800;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            align-items: center;
            justify-content: center;
        }
        .modal-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        .modal-close:hover {
            color: #333;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .comparison-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
        .comparison-item h4 {
            margin-top: 0;
            color: #007bff;
        }
        .old-input {
            background: #fff2f2;
            border-color: #ffcccc;
        }
        .new-select {
            background: #f2fff2;
            border-color: #ccffcc;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏙️ 技师编辑城市选择器测试</h1>
        
        <div class="section">
            <h3>📝 功能说明</h3>
            <p style="color: #666; line-height: 1.6;">
                后台技师管理中的技师编辑功能现在使用城市下拉框选择，替代了原来的文本输入框。
                城市列表按拼音A-Z排序显示，提供更好的用户体验。
            </p>
            
            <div style="background: white; padding: 15px; border-radius: 6px; margin-top: 15px;">
                <h4>✨ 改进内容</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    <li><strong>下拉框选择</strong> - 城市字段改为下拉框，避免输入错误</li>
                    <li><strong>拼音排序</strong> - 城市按拼音首字母A-Z排序显示</li>
                    <li><strong>当前值保持</strong> - 编辑时自动选中技师当前所在城市</li>
                    <li><strong>实时加载</strong> - 从后台API动态获取最新城市列表</li>
                    <li><strong>错误处理</strong> - 加载失败时显示友好提示</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h3>🔄 新旧对比</h3>
            <div class="comparison">
                <div class="comparison-item">
                    <h4>❌ 旧版本 (文本输入)</h4>
                    <div class="form-group">
                        <label class="form-label">城市</label>
                        <input type="text" class="form-control old-input" value="三亚市" placeholder="手动输入城市名称">
                    </div>
                    <div style="font-size: 12px; color: #999; margin-top: 8px;">
                        问题：容易输入错误、格式不统一、无法验证城市是否存在
                    </div>
                </div>
                <div class="comparison-item">
                    <h4>✅ 新版本 (下拉选择)</h4>
                    <div class="form-group">
                        <label class="form-label">城市</label>
                        <select class="form-control new-select" id="demo-city-select">
                            <option value="">正在加载城市...</option>
                        </select>
                    </div>
                    <div style="font-size: 12px; color: #999; margin-top: 8px;">
                        优势：避免输入错误、格式统一、按拼音排序、实时更新
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>🧪 功能测试</h3>
            <p>点击下方按钮测试技师编辑模态框中的城市选择器功能：</p>
            
            <div style="margin: 20px 0;">
                <button class="btn success" onclick="showTestModal()">🔧 打开技师编辑测试</button>
                <button class="btn" onclick="loadDemoCities()">🔄 加载城市列表</button>
                <button class="btn warning" onclick="openAdminPanel()">🔗 打开后台管理</button>
            </div>
            
            <div id="test-result"></div>
        </div>
    </div>
    
    <!-- 测试模态框 -->
    <div class="modal" id="testModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑技师资料 (测试)</h3>
                <button class="modal-close" onclick="hideTestModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="test-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">昵称</label>
                            <input type="text" class="form-control" name="nick" value="李大宝" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">手机号</label>
                            <input type="text" class="form-control" name="phone" value="13800138000" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">年龄</label>
                            <input type="number" class="form-control" name="age" value="22" readonly>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">身高(cm)</label>
                            <input type="number" class="form-control" name="height" value="175" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">体重(kg)</label>
                            <input type="number" class="form-control" name="weight" value="60" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">尺寸</label>
                            <input type="text" class="form-control" name="size" value="16" readonly>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">城市 ⭐</label>
                            <select class="form-control new-select" name="city" id="test-city-select" required>
                                <option value="">请选择城市</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">详细地址</label>
                            <input type="text" class="form-control" name="hotel" value="某某酒店" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">虚拟单量</label>
                            <input type="number" class="form-control" name="virtual_orders" value="0" readonly>
                        </div>
                    </div>
                    
                    <div style="display: flex; gap: 10px; margin-top: 20px;">
                        <button type="button" class="btn success" onclick="testSave()">💾 测试保存</button>
                        <button type="button" class="btn" onclick="hideTestModal()">取消</button>
                    </div>
                    
                    <div id="test-msg" style="margin-top: 15px; display: none;"></div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时自动加载演示城市
        document.addEventListener('DOMContentLoaded', function() {
            loadDemoCities();
        });
        
        // 加载演示城市列表
        async function loadDemoCities() {
            const demoSelect = document.getElementById('demo-city-select');
            demoSelect.innerHTML = '<option value="">正在加载城市...</option>';
            
            try {
                const response = await fetch('admin/city_list.php');
                const cities = await response.json();
                
                if (Array.isArray(cities) && cities.length > 0) {
                    demoSelect.innerHTML = '<option value="">请选择城市</option>';
                    
                    cities.forEach(city => {
                        const option = document.createElement('option');
                        option.value = city.name;
                        option.textContent = city.name;
                        
                        // 默认选中三亚市作为演示
                        if (city.name === '三亚市') {
                            option.selected = true;
                        }
                        
                        demoSelect.appendChild(option);
                    });
                    
                    document.getElementById('test-result').innerHTML = `
                        <div class="success">
                            ✅ 城市列表加载成功！共 ${cities.length} 个城市，按拼音A-Z排序
                        </div>
                    `;
                } else {
                    demoSelect.innerHTML = '<option value="">暂无城市数据</option>';
                    document.getElementById('test-result').innerHTML = `
                        <div class="error">❌ 城市数据为空</div>
                    `;
                }
            } catch (error) {
                console.error('加载城市失败:', error);
                demoSelect.innerHTML = '<option value="">加载失败</option>';
                document.getElementById('test-result').innerHTML = `
                    <div class="error">❌ 加载城市失败: ${error.message}</div>
                `;
            }
        }
        
        // 显示测试模态框
        function showTestModal() {
            const modal = document.getElementById('testModal');
            modal.style.display = 'flex';
            
            // 加载测试城市列表
            loadTestCities('三亚市'); // 假设当前技师在三亚市
        }
        
        // 隐藏测试模态框
        function hideTestModal() {
            const modal = document.getElementById('testModal');
            modal.style.display = 'none';
        }
        
        // 加载测试城市列表
        async function loadTestCities(currentCity) {
            const citySelect = document.getElementById('test-city-select');
            citySelect.innerHTML = '<option value="">正在加载城市...</option>';
            
            try {
                const response = await fetch('admin/city_list.php');
                const cities = await response.json();
                
                if (Array.isArray(cities) && cities.length > 0) {
                    citySelect.innerHTML = '<option value="">请选择城市</option>';
                    
                    cities.forEach(city => {
                        const option = document.createElement('option');
                        option.value = city.name;
                        option.textContent = city.name;
                        
                        // 如果是当前城市，设为选中
                        if (city.name === currentCity) {
                            option.selected = true;
                        }
                        
                        citySelect.appendChild(option);
                    });
                    
                    console.log(`城市列表加载成功，当前选中: ${currentCity}`);
                } else {
                    citySelect.innerHTML = '<option value="">暂无城市数据</option>';
                }
            } catch (error) {
                console.error('加载城市列表失败:', error);
                citySelect.innerHTML = '<option value="">加载失败，请刷新重试</option>';
            }
        }
        
        // 测试保存功能
        function testSave() {
            const citySelect = document.getElementById('test-city-select');
            const selectedCity = citySelect.value;
            const msg = document.getElementById('test-msg');
            
            if (!selectedCity) {
                msg.innerHTML = '<div class="error">❌ 请选择城市</div>';
                msg.style.display = 'block';
                return;
            }
            
            msg.innerHTML = `
                <div class="success">
                    ✅ 测试成功！<br>
                    选中城市: <strong>${selectedCity}</strong><br>
                    在实际使用中，这里会调用 tech_edit.php 保存技师信息
                </div>
            `;
            msg.style.display = 'block';
            
            console.log('测试保存 - 选中城市:', selectedCity);
        }
        
        // 打开后台管理
        function openAdminPanel() {
            // 需要先登录后台
            window.open('admin/admin_login.html', '_blank');
        }
        
        // 点击模态框外部关闭
        document.getElementById('testModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideTestModal();
            }
        });
    </script>
</body>
</html>
