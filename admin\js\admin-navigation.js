/**
 * 后台管理系统 - 导航管理
 */

window.AdminNavigation = {
  // 当前激活的导航
  currentNav: 'nav-dashboard',

  // 初始化导航
  init: function() {
    const navItems = document.querySelectorAll('.admin-nav li');
    const title = document.getElementById('admin-title');
    const main = document.getElementById('admin-main');

    navItems.forEach(nav => {
      nav.addEventListener('click', () => {
        // 移除所有激活状态
        navItems.forEach(n => n.classList.remove('active'));
        // 添加当前激活状态
        nav.classList.add('active');
        
        // 更新当前导航
        this.currentNav = nav.id;
        
        // 根据导航ID加载对应内容
        this.loadContent(nav.id, title, main);
      });
    });

    // 加载默认内容
    this.loadContent('nav-dashboard', title, main);
  },

  // 加载内容
  loadContent: function(navId, titleElement, mainElement) {
    switch(navId) {
      case 'nav-dashboard':
        this.loadDashboard(titleElement, mainElement);
        break;
      case 'nav-user':
        this.loadUserManagement(titleElement, mainElement);
        break;
      case 'nav-tech':
        this.loadTechManagement(titleElement, mainElement);
        break;
      case 'nav-recommend':
        this.loadRecommendManagement(titleElement, mainElement);
        break;
      case 'nav-announcement':
        this.loadAnnouncementManagement(titleElement, mainElement);
        break;
      case 'nav-service':
        this.loadServiceManagement(titleElement, mainElement);
        break;
      case 'nav-feed':
        this.loadFeedManagement(titleElement, mainElement);
        break;
      case 'nav-taxi':
        this.loadTaxiManagement(titleElement, mainElement);
        break;
      case 'nav-city':
        this.loadCityManagement(titleElement, mainElement);
        break;
      case 'nav-password':
        this.loadPasswordManagement(titleElement, mainElement);
        break;
      case 'nav-banner':
        this.loadBannerManagement(titleElement, mainElement);
        break;
      case 'nav-amap':
        this.loadAmapManagement(titleElement, mainElement);
        break;
      case 'nav-project':
        this.loadProjectManagement(titleElement, mainElement);
        break;
      case 'nav-logout':
        this.logout();
        break;
      default:
        this.loadDashboard(titleElement, mainElement);
    }
  },

  // 数据看板
  loadDashboard: function(titleElement, mainElement) {
    titleElement.textContent = '数据看板';
    mainElement.innerHTML = `
      <div class="card">
        <div class="card-title">平台数据概览</div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
          <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #2196f3;" id="user-count">-</div>
            <div style="color: #666; margin-top: 8px;">注册用户</div>
          </div>
          <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #4caf50;" id="tech-count">-</div>
            <div style="color: #666; margin-top: 8px;">认证技师</div>
          </div>
          <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #ff9800;" id="pending-count">-</div>
            <div style="color: #666; margin-top: 8px;">待审核</div>
          </div>
          <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 24px; font-weight: bold; color: #9c27b0;" id="city-count">-</div>
            <div style="color: #666; margin-top: 8px;">开通城市</div>
          </div>
        </div>
      </div>
      <div class="card">
        <div class="card-title">快捷操作</div>
        <div style="display: flex; gap: 12px; flex-wrap: wrap;">
          <button class="btn btn-primary" onclick="AdminNavigation.switchTo('nav-user')">客户管理</button>
          <button class="btn btn-success" onclick="AdminNavigation.switchTo('nav-tech')">技师管理</button>
          <button class="btn btn-warning" onclick="AdminNavigation.switchTo('nav-banner')">轮播图管理</button>
          <button class="btn btn-danger" onclick="AdminNavigation.switchTo('nav-password')">修改密码</button>
        </div>
      </div>
    `;

    // 加载统计数据
    this.loadDashboardStats();
  },

  // 加载统计数据
  loadDashboardStats: function() {
    // 用户数量
    AdminCommon.api.get('user_list.php')
      .then(data => {
        const count = Array.isArray(data) ? data.length : 0;
        document.getElementById('user-count').textContent = count;
      })
      .catch(() => {
        document.getElementById('user-count').textContent = '0';
      });

    // 技师数量
    AdminCommon.api.get('tech_list.php')
      .then(data => {
        const techs = data.success ? data.data : [];
        const approvedCount = techs.filter(t => t.status == 1 || t.status === 'approved').length;
        document.getElementById('tech-count').textContent = approvedCount;
        
        const pendingCount = techs.filter(t => t.status == 0 || t.status === 'pending').length;
        document.getElementById('pending-count').textContent = pendingCount;
      })
      .catch(() => {
        document.getElementById('tech-count').textContent = '0';
        document.getElementById('pending-count').textContent = '0';
      });

    // 城市数量
    AdminCommon.api.get('city_list.php')
      .then(data => {
        const count = Array.isArray(data) ? data.length : 0;
        document.getElementById('city-count').textContent = count;
      })
      .catch(() => {
        document.getElementById('city-count').textContent = '0';
      });
  },

  // 切换到指定导航
  switchTo: function(navId) {
    const navElement = document.getElementById(navId);
    if (navElement) {
      navElement.click();
    }
  },

  // 客户管理
  loadUserManagement: function(titleElement, mainElement) {
    titleElement.textContent = '客户管理';
    mainElement.innerHTML = `
      <div class="card">
        <div class="card-title">客户列表</div>
        <div id="user-list-container">
          <div class="text-center text-muted">正在加载...</div>
        </div>
      </div>
    `;

    // 加载客户数据
    AdminModules.user.loadList();
  },

  // 技师管理
  loadTechManagement: function(titleElement, mainElement) {
    titleElement.textContent = '技师管理';
    mainElement.innerHTML = `
      <div class="card">
        <div class="card-title">技师管理</div>

        <!-- 搜索框 -->
        <div style="margin-bottom: 16px;">
          <div style="display: flex; gap: 12px; align-items: center; margin-bottom: 12px;">
            <div style="flex: 1; max-width: 400px;">
              <input type="text" id="tech-search-input" placeholder="搜索技师昵称、手机号、城市..."
                     style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;"
                     onkeyup="AdminModules.tech.handleSearchKeyup(event)">
            </div>
            <button class="btn btn-primary" onclick="AdminModules.tech.performSearch()">
              🔍 搜索
            </button>
            <button class="btn btn-secondary" onclick="AdminModules.tech.clearSearch()">
              🗑️ 清空
            </button>
          </div>
        </div>

        <!-- 状态筛选按钮 -->
        <div style="margin-bottom: 16px;" id="tech-filter-buttons">
          <button class="btn btn-primary" onclick="AdminModules.tech.filterByStatus('all')" id="btn-all">全部 (0)</button>
          <button class="btn btn-warning" onclick="AdminModules.tech.filterByStatus('pending')" id="btn-pending">申请中 (0)</button>
          <button class="btn btn-success" onclick="AdminModules.tech.filterByStatus('approved')" id="btn-approved">已通过 (0)</button>
          <button class="btn btn-danger" onclick="AdminModules.tech.filterByStatus('rejected')" id="btn-rejected">已驳回 (0)</button>
        </div>

        <div id="tech-list-container">
          <div class="text-center text-muted">正在加载...</div>
        </div>
        <div id="tech-pagination-container">
          <!-- 分页组件将在这里渲染 -->
        </div>
      </div>
    `;

    // 加载技师数据
    AdminModules.tech.loadList();
  },

  // 推荐管理
  loadRecommendManagement: function(titleElement, mainElement) {
    titleElement.textContent = '推荐管理';
    mainElement.innerHTML = `
      <div class="card">
        <div class="card-title">推荐管理</div>

        <!-- 推荐功能开关 -->
        <div class="recommend-section">
          <h3 style="color: #333; margin-bottom: 16px; font-size: 18px;">推荐功能设置</h3>
          <div class="recommend-switch-container">
            <label class="recommend-switch">
              <input type="checkbox" id="recommend-toggle" onchange="AdminModules.recommend.toggleRecommendFunction()">
              <span class="recommend-slider"></span>
            </label>
            <span id="recommend-status-text" style="margin-left: 12px; font-weight: 500;">推荐功能：加载中...</span>
          </div>
        </div>

        <!-- 推荐管理选项卡 -->
        <div class="recommend-tabs" style="margin-top: 32px;">
          <div class="tab-buttons" style="margin-bottom: 24px;">
            <button class="tab-btn active" onclick="AdminModules.recommend.switchTab('system')" id="tab-system">系统推荐</button>
            <button class="tab-btn" onclick="AdminModules.recommend.switchTab('manual')" id="tab-manual">人工推荐</button>
          </div>

          <!-- 系统推荐内容 -->
          <div id="system-recommend" class="tab-content active">
            <!-- 系统推荐开关 -->
            <div class="recommend-switch-section">
              <h4 style="color: #333; margin-bottom: 16px;">系统推荐设置</h4>
              <div class="recommend-switch-container">
                <label class="recommend-switch">
                  <input type="checkbox" id="system-recommend-toggle" onchange="AdminModules.recommend.toggleSystemRecommend()">
                  <span class="recommend-slider"></span>
                </label>
                <span id="system-recommend-status-text" style="margin-left: 12px; font-weight: 500;">系统推荐：加载中...</span>
              </div>
            </div>

            <div class="system-recommend-info" style="margin-top: 24px;">
              <h4 style="color: #333; margin-bottom: 16px;">系统推荐规则</h4>
              <div class="recommend-rules">
                <div class="rule-item">
                  <span class="rule-label">排序规则：</span>
                  <span class="rule-value">按虚拟单量从高到低排序</span>
                </div>
                <div class="rule-item">
                  <span class="rule-label">推荐条件：</span>
                  <span class="rule-value">状态为"已通过"且非休息状态</span>
                </div>
                <div class="rule-item">
                  <span class="rule-label">显示位置：</span>
                  <span class="rule-value">技师列表顶部优先显示</span>
                </div>
              </div>
              <div style="margin-top: 20px;">
                <button class="btn btn-primary" onclick="AdminModules.recommend.refreshSystemRecommend()">刷新系统推荐</button>
              </div>
            </div>
          </div>

          <!-- 人工推荐内容 -->
          <div id="manual-recommend" class="tab-content">
            <!-- 人工推荐开关 -->
            <div class="recommend-switch-section">
              <h4 style="color: #333; margin-bottom: 16px;">人工推荐设置</h4>
              <div class="recommend-switch-container">
                <label class="recommend-switch">
                  <input type="checkbox" id="manual-recommend-toggle" onchange="AdminModules.recommend.toggleManualRecommend()">
                  <span class="recommend-slider"></span>
                </label>
                <span id="manual-recommend-status-text" style="margin-left: 12px; font-weight: 500;">人工推荐：加载中...</span>
              </div>
            </div>

            <div class="manual-recommend-controls" style="margin-top: 24px;">
              <h4 style="color: #333; margin-bottom: 16px;">人工推荐管理</h4>
              <div style="margin-bottom: 20px;">
                <button class="btn btn-success" onclick="AdminModules.recommend.showRecommendedTechs()">查看已推荐技师</button>
                <button class="btn btn-warning" onclick="AdminModules.recommend.showAvailableTechs()">选择推荐技师</button>
                <button class="btn btn-secondary" onclick="AdminModules.recommend.batchManageRecommend()">批量管理</button>
              </div>
            </div>
            <div id="manual-recommend-content">
              <div class="text-center text-muted">请选择上方操作</div>
            </div>
          </div>
        </div>
      </div>
    `;

    // 初始化推荐管理
    AdminModules.recommend.init();
  },

  // 公告管理
  loadAnnouncementManagement: function(titleElement, mainElement) {
    titleElement.textContent = '公告管理';
    mainElement.innerHTML = `
      <div class="card">
        <div class="card-title">公告管理</div>

        <!-- 公告管理选项卡 -->
        <div class="announcement-tabs">
          <div class="tab-buttons" style="margin-bottom: 24px;">
            <button class="tab-btn active" onclick="AdminModules.announcement.switchTab('home')" id="tab-home">首页公告</button>
            <button class="tab-btn" onclick="AdminModules.announcement.switchTab('tech')" id="tab-tech">技师页公告</button>
          </div>

          <!-- 首页公告内容 -->
          <div id="home-announcement" class="tab-content active">
            <!-- 首页公告开关 -->
            <div class="announcement-switch-section">
              <h4 style="color: #333; margin-bottom: 16px;">首页公告设置</h4>
              <div class="announcement-controls">
                <div class="control-item">
                  <label class="announcement-switch">
                    <input type="checkbox" id="home-announcement-toggle" onchange="AdminModules.announcement.toggleHomeAnnouncement()">
                    <span class="announcement-slider"></span>
                  </label>
                  <span id="home-announcement-status" style="margin-left: 12px; font-weight: 500;">公告显示：加载中...</span>
                </div>
                <div class="control-item">
                  <label class="announcement-switch">
                    <input type="checkbox" id="home-scroll-toggle" onchange="AdminModules.announcement.toggleHomeScroll()">
                    <span class="announcement-slider"></span>
                  </label>
                  <span id="home-scroll-status" style="margin-left: 12px; font-weight: 500;">滚动显示：加载中...</span>
                </div>
                <div class="control-item" id="home-speed-control" style="display: none;">
                  <label style="margin-right: 12px; font-weight: 500; min-width: 80px;">滚动速度:</label>
                  <input type="range" id="home-scroll-speed" min="5" max="100" value="15" step="1" style="width: 200px; margin-right: 12px;" onchange="AdminModules.announcement.updateHomeScrollSpeed()">
                  <span id="home-speed-value" style="font-weight: 500; color: #666;">15秒</span>
                </div>
              </div>
            </div>

            <!-- 首页公告内容管理 -->
            <div class="announcement-content-section">
              <h4 style="color: #333; margin-bottom: 16px;">公告内容</h4>
              <div class="announcement-editor">
                <textarea id="home-announcement-text" placeholder="请输入首页公告内容..." style="width: 100%; height: 120px; padding: 12px; border: 1px solid #ddd; border-radius: 6px; resize: vertical; font-size: 14px;"></textarea>
                <div style="margin-top: 12px;">
                  <button class="btn btn-primary" onclick="AdminModules.announcement.saveHomeAnnouncement()">保存公告</button>
                  <button class="btn btn-secondary" onclick="AdminModules.announcement.previewHomeAnnouncement()">预览效果</button>
                </div>
              </div>
            </div>
          </div>

          <!-- 技师页公告内容 -->
          <div id="tech-announcement" class="tab-content">
            <!-- 技师页公告开关 -->
            <div class="announcement-switch-section">
              <h4 style="color: #333; margin-bottom: 16px;">技师页公告设置</h4>
              <div class="announcement-controls">
                <div class="control-item">
                  <label class="announcement-switch">
                    <input type="checkbox" id="tech-announcement-toggle" onchange="AdminModules.announcement.toggleTechAnnouncement()">
                    <span class="announcement-slider"></span>
                  </label>
                  <span id="tech-announcement-status" style="margin-left: 12px; font-weight: 500;">公告显示：加载中...</span>
                </div>
                <div class="control-item">
                  <label class="announcement-switch">
                    <input type="checkbox" id="tech-scroll-toggle" onchange="AdminModules.announcement.toggleTechScroll()">
                    <span class="announcement-slider"></span>
                  </label>
                  <span id="tech-scroll-status" style="margin-left: 12px; font-weight: 500;">滚动显示：加载中...</span>
                </div>
                <div class="control-item" id="tech-speed-control" style="display: none;">
                  <label style="margin-right: 12px; font-weight: 500; min-width: 80px;">滚动速度:</label>
                  <input type="range" id="tech-scroll-speed" min="5" max="100" value="15" step="1" style="width: 200px; margin-right: 12px;" onchange="AdminModules.announcement.updateTechScrollSpeed()">
                  <span id="tech-speed-value" style="font-weight: 500; color: #666;">15秒</span>
                </div>
              </div>
            </div>

            <!-- 技师页公告内容管理 -->
            <div class="announcement-content-section">
              <h4 style="color: #333; margin-bottom: 16px;">公告内容</h4>
              <div class="announcement-editor">
                <textarea id="tech-announcement-text" placeholder="请输入技师页公告内容..." style="width: 100%; height: 120px; padding: 12px; border: 1px solid #ddd; border-radius: 6px; resize: vertical; font-size: 14px;"></textarea>
                <div style="margin-top: 12px;">
                  <button class="btn btn-primary" onclick="AdminModules.announcement.saveTechAnnouncement()">保存公告</button>
                  <button class="btn btn-secondary" onclick="AdminModules.announcement.previewTechAnnouncement()">预览效果</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    // 初始化公告管理
    AdminModules.announcement.init();
  },

  // 服务管理
  loadServiceManagement: function(titleElement, mainElement) {
    titleElement.textContent = '服务管理';
    mainElement.innerHTML = `
      <div class="card" style="border-radius: 16px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
        <div style="
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 24px;
          border-radius: 16px 16px 0 0;
          display: flex;
          align-items: center;
          justify-content: space-between;
        ">
          <div>
            <h2 style="margin: 0; font-size: 24px; font-weight: 600;">🛍️ 服务管理</h2>
            <p style="margin: 8px 0 0 0; opacity: 0.9; font-size: 14px;">管理系统的服务项目</p>
          </div>
          <button
            class="btn btn-primary"
            onclick="AdminModules.service.showAddModal()"
            style="
              background: rgba(255,255,255,0.2);
              border: 1px solid rgba(255,255,255,0.3);
              color: white;
              padding: 12px 24px;
              border-radius: 8px;
              font-size: 14px;
              font-weight: 600;
              cursor: pointer;
              transition: all 0.3s ease;
              backdrop-filter: blur(10px);
            "
            onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-1px)'"
            onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'">
            ➕ 添加服务项目
          </button>
        </div>

        <div style="padding: 0 24px 24px 24px;">
          <div id="service-list-container">
            <div style="text-align: center; padding: 40px; color: #999;">
              <div style="font-size: 18px;">⏳ 正在加载服务数据...</div>
            </div>
          </div>
        </div>
      </div>
    `;

    // 加载服务数据
    AdminModules.service.loadList();
  },

  // 动态管理
  loadFeedManagement: function(titleElement, mainElement) {
    titleElement.textContent = '动态管理';
    mainElement.innerHTML = `
      <div class="card" style="border-radius: 16px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
        <div style="
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 24px;
          border-radius: 16px 16px 0 0;
          display: flex;
          align-items: center;
          justify-content: space-between;
        ">
          <div>
            <h2 style="margin: 0; font-size: 24px; font-weight: 600;">📱 动态管理</h2>
            <p style="margin: 8px 0 0 0; opacity: 0.9; font-size: 14px;">管理用户发布的动态内容</p>
          </div>
          <div style="display: flex; gap: 12px;">
            <button
              onclick="AdminModules.feed.loadList()"
              style="
                background: rgba(255,255,255,0.2);
                border: 1px solid rgba(255,255,255,0.3);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                backdrop-filter: blur(10px);
              "
              onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-1px)'"
              onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'">
              🔄 刷新列表
            </button>
          </div>
        </div>

        <div style="padding: 0 24px 24px 24px;">
          <div id="feed-list-container">
            <div style="text-align: center; padding: 40px; color: #999;">
              <div style="font-size: 18px;">⏳ 正在加载动态数据...</div>
            </div>
          </div>
        </div>
      </div>
    `;

    // 加载动态数据
    AdminModules.feed.loadList();
  },

  // 路费管理
  loadTaxiManagement: function(titleElement, mainElement) {
    titleElement.textContent = '路费管理';
    mainElement.innerHTML = `
      <div class="card">
        <div class="card-title">路费管理</div>
        <div id="taxi-form-container">
          <div class="text-center text-muted">正在加载...</div>
        </div>
      </div>
    `;

    // 加载路费管理
    AdminModules.taxi.loadForm();
  },

  // 城市管理
  loadCityManagement: function(titleElement, mainElement) {
    titleElement.textContent = '城市管理';
    mainElement.innerHTML = `
      <div class="card" style="border-radius: 16px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
        <div style="
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 24px;
          border-radius: 16px 16px 0 0;
          display: flex;
          align-items: center;
          justify-content: space-between;
        ">
          <div>
            <h2 style="margin: 0; font-size: 24px; font-weight: 600;">🏙️ 城市管理</h2>
            <p style="margin: 8px 0 0 0; opacity: 0.9; font-size: 14px;">管理系统支持的城市列表</p>
          </div>
          <button
            class="btn btn-primary"
            onclick="AdminModules.city.showAddModal()"
            style="
              background: rgba(255,255,255,0.2);
              border: 1px solid rgba(255,255,255,0.3);
              color: white;
              padding: 12px 24px;
              border-radius: 8px;
              font-size: 14px;
              font-weight: 600;
              cursor: pointer;
              transition: all 0.3s ease;
              backdrop-filter: blur(10px);
            "
            onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-1px)'"
            onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'">
            ➕ 新增城市
          </button>
        </div>

        <div style="padding: 0 24px 24px 24px;">
          <div id="city-list-container">
            <div style="text-align: center; padding: 40px; color: #999;">
              <div style="font-size: 18px;">⏳ 正在加载城市数据...</div>
            </div>
          </div>
        </div>
      </div>
    `;

    // 加载城市数据
    AdminModules.city.loadList();
  },

  // 密码管理
  loadPasswordManagement: function(titleElement, mainElement) {
    titleElement.textContent = '修改密码';
    mainElement.innerHTML = `
      <div class="card" style="max-width: 400px; margin: 0 auto;">
        <div class="card-title">修改管理员密码</div>
        <div id="password-form-container">
          <div class="text-center text-muted">正在加载...</div>
        </div>
      </div>
    `;

    // 加载密码修改表单
    AdminModules.password.loadForm();
  },

  // 轮播图管理
  loadBannerManagement: function(titleElement, mainElement) {
    titleElement.textContent = '轮播图管理';
    mainElement.innerHTML = `
      <div class="card">
        <div class="card-title">轮播图管理</div>
        <div style="margin-bottom: 16px;">
          <button class="btn btn-primary" onclick="AdminModules.banner.showAddModal()">+ 新增轮播图</button>
        </div>
        <div id="banner-list-container">
          <div class="text-center text-muted">正在加载...</div>
        </div>
      </div>
    `;

    // 加载轮播图数据
    AdminModules.banner.loadList();
  },

  // 高德key管理
  loadAmapManagement: function(titleElement, mainElement) {
    titleElement.textContent = '高德key管理';
    mainElement.innerHTML = `
      <div class="card" style="max-width: 400px; margin: 0 auto;">
        <div class="card-title">高德地图API Key管理</div>
        <div id="amap-form-container">
          <div class="text-center text-muted">正在加载...</div>
        </div>
      </div>
    `;

    // 加载高德key管理
    AdminModules.amap.loadForm();
  },

  // 项目介绍管理
  loadProjectManagement: function(titleElement, mainElement) {
    titleElement.textContent = '项目介绍管理';
    mainElement.innerHTML = `
      <div class="card" style="max-width: 500px; margin: 0 auto;">
        <div class="card-title">项目介绍管理</div>
        <div id="project-form-container">
          <div class="text-center text-muted">正在加载...</div>
        </div>
      </div>
    `;

    // 加载项目介绍管理
    AdminModules.project.loadForm();
  },

  // 退出登录
  logout: function() {
    if (confirm('确定要退出登录吗？')) {
      window.location.href = 'admin_logout.php';
    }
  }
};

// 页面加载完成后初始化导航
document.addEventListener('DOMContentLoaded', function() {
  AdminNavigation.init();
});
