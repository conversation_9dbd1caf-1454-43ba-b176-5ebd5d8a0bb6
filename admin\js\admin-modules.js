/**
 * 后台管理系统 - 功能模块
 */

window.AdminModules = {
  // 用户管理模块
  user: {
    loadList: function() {
      const container = document.getElementById('user-list-container');
      AdminCommon.ui.showLoading(container);

      AdminCommon.api.get('user_list.php')
        .then(data => {
          if (Array.isArray(data) && data.length > 0) {
            this.renderList(data);
          } else {
            AdminCommon.ui.showEmpty(container, '暂无客户数据');
          }
        })
        .catch(error => {
          AdminCommon.ui.showError(container, '加载客户数据失败');
        });
    },

    renderList: function(users) {
      const columns = [
        { key: 'id', title: 'ID', width: '60px' },
        { 
          key: 'avatar', 
          title: '头像', 
          width: '80px',
          render: (value) => `<img src="${AdminCommon.format.imagePath(value)}" style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;">`
        },
        { key: 'nickname', title: '昵称' },
        { key: 'mobile', title: '手机号' },
        {
          key: 'last_visit',
          title: '最后访问',
          render: (value) => AdminCommon.format.lastVisit(value)
        },
        { 
          key: 'created_at', 
          title: '注册时间', 
          render: (value) => AdminCommon.format.date(value)
        },
        { 
          key: 'is_black', 
          title: '状态', 
          render: (value) => value ? '<span style="color: #f44336;">已拉黑</span>' : '<span style="color: #4caf50;">正常</span>'
        }
      ];

      AdminCommon.table.render('user-list-container', users, columns);
    }
  },

  // 技师管理模块
  tech: {
    currentFilter: 'all',
    currentPage: 1,
    pageSize: 10,
    filteredData: [],
    searchKeyword: '',

    loadList: function() {
      const container = document.getElementById('tech-list-container');
      AdminCommon.ui.showLoading(container);

      AdminCommon.api.get('tech_list.php')
        .then(data => {
          if (data.success && Array.isArray(data.data)) {
            AdminCommon.cache.techs = data.data;
            this.updateFilterButtons(data.data);
            this.filterByStatus(this.currentFilter);
          } else {
            AdminCommon.ui.showEmpty(container, '暂无技师数据');
            this.updateFilterButtons([]);
          }
        })
        .catch(error => {
          AdminCommon.ui.showError(container, '加载技师数据失败');
          this.updateFilterButtons([]);
        });
    },

    updateFilterButtons: function(techs) {
      // 统计各状态的数量
      const counts = {
        all: techs.length,
        pending: techs.filter(t => t.status == 0 || t.status === 'pending').length,
        approved: techs.filter(t => t.status == 1 || t.status === 'approved').length,
        rejected: techs.filter(t => t.status == 2 || t.status === 'rejected').length
      };

      // 更新按钮文本
      const btnAll = document.getElementById('btn-all');
      const btnPending = document.getElementById('btn-pending');
      const btnApproved = document.getElementById('btn-approved');
      const btnRejected = document.getElementById('btn-rejected');

      if (btnAll) btnAll.textContent = `全部 (${counts.all})`;
      if (btnPending) btnPending.textContent = `申请中 (${counts.pending})`;
      if (btnApproved) btnApproved.textContent = `已通过 (${counts.approved})`;
      if (btnRejected) btnRejected.textContent = `已驳回 (${counts.rejected})`;

      // 更新按钮样式，突出显示当前筛选
      const buttons = document.querySelectorAll('#tech-filter-buttons button');
      buttons.forEach(btn => {
        btn.classList.remove('btn-primary', 'btn-warning', 'btn-success', 'btn-danger');
        btn.classList.add('btn-secondary');
      });

      // 根据当前筛选状态设置激活按钮样式
      switch(this.currentFilter) {
        case 'all':
          if (btnAll) {
            btnAll.classList.remove('btn-secondary');
            btnAll.classList.add('btn-primary');
          }
          break;
        case 'pending':
          if (btnPending) {
            btnPending.classList.remove('btn-secondary');
            btnPending.classList.add('btn-warning');
          }
          break;
        case 'approved':
          if (btnApproved) {
            btnApproved.classList.remove('btn-secondary');
            btnApproved.classList.add('btn-success');
          }
          break;
        case 'rejected':
          if (btnRejected) {
            btnRejected.classList.remove('btn-secondary');
            btnRejected.classList.add('btn-danger');
          }
          break;
      }
    },

    filterByStatus: function(status) {
      this.currentFilter = status;
      this.currentPage = 1; // 重置到第一页
      this.applyFilters();
    },

    // 应用所有过滤条件
    applyFilters: function() {
      let filteredData = AdminCommon.cache.techs;

      // 按状态筛选
      if (this.currentFilter !== 'all') {
        filteredData = filteredData.filter(tech => {
          if (this.currentFilter === 'pending') return tech.status == 0 || tech.status === 'pending';
          if (this.currentFilter === 'approved') return tech.status == 1 || tech.status === 'approved';
          if (this.currentFilter === 'rejected') return tech.status == 2 || tech.status === 'rejected';
          return true;
        });
      }

      // 按搜索关键字筛选
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.trim().toLowerCase();
        filteredData = filteredData.filter(tech => {
          return (tech.nick && tech.nick.toLowerCase().includes(keyword)) ||
                 (tech.phone && tech.phone.includes(keyword)) ||
                 (tech.city && tech.city.toLowerCase().includes(keyword)) ||
                 (tech.hotel && tech.hotel.toLowerCase().includes(keyword));
        });
      }

      this.filteredData = filteredData;
      // 更新按钮样式
      this.updateFilterButtons(AdminCommon.cache.techs);
      this.renderList();
    },

    changePage: function(page) {
      this.currentPage = page;
      this.renderList();
    },

    // 搜索相关函数
    handleSearchKeyup: function(event) {
      if (event.key === 'Enter') {
        this.performSearch();
      }
    },

    performSearch: function() {
      const searchInput = document.getElementById('tech-search-input');
      if (searchInput) {
        this.searchKeyword = searchInput.value.trim();
        this.currentPage = 1; // 重置到第一页
        this.applyFilters();

        // 更新搜索状态显示
        this.updateSearchStatus();
      }
    },

    clearSearch: function() {
      const searchInput = document.getElementById('tech-search-input');
      if (searchInput) {
        searchInput.value = '';
        this.searchKeyword = '';
        this.currentPage = 1; // 重置到第一页
        this.applyFilters();

        // 更新搜索状态显示
        this.updateSearchStatus();
      }
    },

    updateSearchStatus: function() {
      // 可以在这里添加搜索状态的显示逻辑
      if (this.searchKeyword.trim()) {
        console.log(`搜索关键字: "${this.searchKeyword}", 找到 ${this.filteredData.length} 条结果`);
      }
    },

    renderSearchInfo: function() {
      // 在技师列表容器前添加搜索结果信息
      const container = document.getElementById('tech-list-container');
      if (!container) return;

      // 移除之前的搜索信息
      const existingInfo = document.getElementById('tech-search-info');
      if (existingInfo) {
        existingInfo.remove();
      }

      // 如果有搜索关键字或筛选条件，显示结果信息
      if (this.searchKeyword.trim() || this.currentFilter !== 'all') {
        const totalCount = AdminCommon.cache.techs ? AdminCommon.cache.techs.length : 0;
        const filteredCount = this.filteredData.length;

        let infoText = '';
        if (this.searchKeyword.trim() && this.currentFilter !== 'all') {
          const filterText = this.getFilterText(this.currentFilter);
          infoText = `搜索"${this.searchKeyword}"在${filterText}中找到 ${filteredCount} 条结果`;
        } else if (this.searchKeyword.trim()) {
          infoText = `搜索"${this.searchKeyword}"找到 ${filteredCount} 条结果`;
        } else if (this.currentFilter !== 'all') {
          const filterText = this.getFilterText(this.currentFilter);
          infoText = `${filterText}共 ${filteredCount} 条记录`;
        }

        if (infoText) {
          const infoDiv = document.createElement('div');
          infoDiv.id = 'tech-search-info';
          infoDiv.style.cssText = `
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            margin-bottom: 12px;
            font-size: 14px;
            color: #495057;
          `;
          infoDiv.innerHTML = `
            <span style="color: #007bff;">📊</span> ${infoText}
            ${this.searchKeyword.trim() ? `<button onclick="AdminModules.tech.clearSearch()" style="margin-left: 12px; background: none; border: none; color: #6c757d; cursor: pointer; font-size: 12px;">✕ 清除搜索</button>` : ''}
          `;

          container.parentNode.insertBefore(infoDiv, container);
        }
      }
    },

    getFilterText: function(filter) {
      const filterMap = {
        'all': '全部技师',
        'pending': '申请中的技师',
        'approved': '已通过的技师',
        'rejected': '已驳回的技师'
      };
      return filterMap[filter] || '技师';
    },

    renderList: function() {
      // 显示搜索结果信息
      this.renderSearchInfo();

      // 使用分页数据
      const paginationResult = AdminCommon.pagination.paginate(
        this.filteredData,
        this.currentPage,
        this.pageSize
      );

      const columns = [
        { key: 'id', title: 'ID', width: '60px' },
        {
          key: 'workimg',
          title: '头像',
          width: '80px',
          render: (value) => `<img src="${AdminCommon.format.imagePath(value)}" style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;">`
        },
        { key: 'nick', title: '昵称' },
        { key: 'phone', title: '手机号' },
        { key: 'age', title: '年龄' },
        { key: 'city', title: '城市' },
        {
          key: 'virtual_orders',
          title: '虚拟单量',
          width: '80px',
          render: (value) => `<span style="color: #666; font-weight: bold;">${value || 0}</span>`
        },
        {
          key: 'is_resting',
          title: '工作状态',
          width: '80px',
          render: (value, row) => {
            const isResting = value == 1;
            const color = isResting ? '#dc3545' : '#28a745';
            const text = isResting ? '休息中' : '工作中';
            return `<span style="color: ${color}; font-weight: bold;">${text}</span>`;
          }
        },
        {
          key: 'status',
          title: '状态',
          render: (value, row) => {
            let statusHtml = AdminCommon.format.status(value, 'tech');
            // 如果是推荐技师，添加推荐标识
            if (row.is_recommended == 1) {
              statusHtml += ' <span style="color: #ff9800; font-size: 12px; font-weight: bold;">[推荐]</span>';
            }
            // 如果是已拉黑的技师，添加拉黑标识
            if (row.is_black == 1) {
              statusHtml += ' <span style="color: #dc3545; font-size: 12px;">[已拉黑]</span>';
            }
            return statusHtml;
          }
        },
        {
          key: 'apply_time',
          title: '申请时间',
          render: (value) => AdminCommon.format.date(value)
        },
        // 为已通过的技师添加最后访问时间列
        {
          key: 'last_visit',
          title: '最后访问',
          render: (value, row) => {
            if (row.status == 1 || row.status === 'approved') {
              return AdminCommon.format.lastVisit(value);
            }
            return '-';
          }
        },
        {
          key: 'actions',
          title: '操作',
          width: '320px',
          render: (value, row) => {
            let buttons = `<button class="btn btn-primary" onclick="AdminModules.tech.viewDetail(${row.id})" style="margin: 2px; font-size: 12px; padding: 4px 8px;">查看</button>`;

            if (row.status == 0 || row.status === 'pending') {
              // 申请中的技师：通过、驳回、删除
              buttons += ` <button class="btn btn-success" onclick="AdminModules.tech.approve(${row.id})" style="margin: 2px; font-size: 12px; padding: 4px 8px;">通过</button>`;
              buttons += ` <button class="btn btn-danger" onclick="AdminModules.tech.reject(${row.id})" style="margin: 2px; font-size: 12px; padding: 4px 8px;">驳回</button>`;
              buttons += ` <button class="btn btn-warning" onclick="AdminModules.tech.delete(${row.id})" style="margin: 2px; font-size: 12px; padding: 4px 8px; background: #dc3545; border-color: #dc3545;">删除</button>`;
            } else if (row.status == 1 || row.status === 'approved') {
              // 已通过的技师：编辑、删除、推荐/取消推荐、拉黑/解除拉黑
              buttons += ` <button class="btn btn-warning" onclick="AdminModules.tech.showEditModal(${row.id})" style="margin: 2px; font-size: 12px; padding: 4px 8px;">编辑</button>`;

              // 推荐/取消推荐按钮
              if (row.is_recommended == 1) {
                buttons += ` <button class="btn" onclick="AdminModules.tech.unrecommend(${row.id})" style="margin: 2px; font-size: 12px; padding: 4px 8px; background: #6c757d; color: white; border-color: #6c757d;">取消推荐</button>`;
              } else {
                buttons += ` <button class="btn" onclick="AdminModules.tech.recommend(${row.id})" style="margin: 2px; font-size: 12px; padding: 4px 8px; background: #ff9800; color: white; border-color: #ff9800;">设为推荐</button>`;
              }

              // 休息/工作切换按钮
              if (row.is_resting == 1) {
                buttons += ` <button class="btn" onclick="AdminModules.tech.setWorking(${row.id})" style="margin: 2px; font-size: 12px; padding: 4px 8px; background: #28a745; color: white; border-color: #28a745;">设为工作</button>`;
              } else {
                buttons += ` <button class="btn" onclick="AdminModules.tech.setResting(${row.id})" style="margin: 2px; font-size: 12px; padding: 4px 8px; background: #dc3545; color: white; border-color: #dc3545;">设为休息</button>`;
              }

              buttons += ` <button class="btn btn-danger" onclick="AdminModules.tech.delete(${row.id})" style="margin: 2px; font-size: 12px; padding: 4px 8px;">删除</button>`;

              if (row.is_black == 1) {
                buttons += ` <button class="btn btn-success" onclick="AdminModules.tech.unblacklist(${row.id})" style="margin: 2px; font-size: 12px; padding: 4px 8px;">解除拉黑</button>`;
              } else {
                buttons += ` <button class="btn" onclick="AdminModules.tech.blacklist(${row.id})" style="margin: 2px; font-size: 12px; padding: 4px 8px; background: #6c757d; color: white; border-color: #6c757d;">拉黑</button>`;
              }
            } else if (row.status == 2 || row.status === 'rejected') {
              // 已驳回的技师：删除
              buttons += ` <button class="btn btn-danger" onclick="AdminModules.tech.delete(${row.id})" style="margin: 2px; font-size: 12px; padding: 4px 8px;">删除</button>`;
            }

            return buttons;
          }
        }
      ];

      // 渲染表格
      AdminCommon.table.render('tech-list-container', paginationResult.data, columns);

      // 渲染分页组件
      AdminCommon.pagination.render(
        'tech-pagination-container',
        paginationResult.currentPage,
        paginationResult.totalPages,
        'AdminModules.tech.changePage'
      );
    },

    viewDetail: function(id) {
      const tech = AdminCommon.cache.techs.find(t => t.id == id);
      if (!tech) {
        AdminCommon.ui.alert('未找到技师信息');
        return;
      }

      const content = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
          <div><strong>ID:</strong> ${tech.id}</div>
          <div><strong>昵称:</strong> ${tech.nick || '未填写'}</div>
          <div><strong>手机:</strong> ${tech.phone || '未填写'}</div>
          <div><strong>年龄:</strong> ${tech.age || '未填写'}</div>
          <div><strong>身高:</strong> ${tech.height || '未填写'}cm</div>
          <div><strong>体重:</strong> ${tech.weight || '未填写'}kg</div>
          <div><strong>尺寸:</strong> ${tech.size || '未填写'}</div>
          <div><strong>你的型号:</strong> ${tech.model || '未填写'}</div>
          <div><strong>城市:</strong> ${tech.city || '未填写'}</div>
          <div><strong>住址:</strong> ${tech.hotel || '未填写'}</div>
          <div><strong>申请时间:</strong> ${AdminCommon.format.date(tech.apply_time)}</div>
          <div><strong>状态:</strong> ${AdminCommon.format.status(tech.status, 'tech')}</div>
          <div><strong>推荐状态:</strong> <span style="color: ${tech.is_recommended == 1 ? '#ff9800' : '#666'}; font-weight: bold;">${tech.is_recommended_text || (tech.is_recommended == 1 ? '推荐' : '普通')}</span></div>
          <div><strong>虚拟单量:</strong> <span style="color: #666; font-weight: bold;">${tech.virtual_orders || 0}</span></div>
          <div><strong>工作状态:</strong> <span style="color: ${tech.is_resting == 1 ? '#dc3545' : '#28a745'}; font-weight: bold;">${tech.is_resting_text || (tech.is_resting == 1 ? '休息中' : '工作中')}</span></div>
          ${tech.reject_reason ? '<div style="grid-column: 1/-1;"><strong>驳回原因:</strong> ' + tech.reject_reason + '</div>' : ''}
        </div>
        ${tech.workimg ? '<div style="margin-top: 15px;"><strong>工作照:</strong><br><img src="' + AdminCommon.format.imagePath(tech.workimg) + '" style="max-width: 200px; border-radius: 8px; cursor: pointer;" onclick="AdminModules.tech.showImage(this.src)"></div>' : ''}
        ${tech.lifeimg ? '<div style="margin-top: 15px;"><strong>生活照:</strong><br>' + tech.lifeimg.split(',').filter(img => img.trim()).map(img => '<img src="' + AdminCommon.format.imagePath(img.trim()) + '" style="width: 120px; height: 120px; object-fit: cover; margin: 5px; border-radius: 8px; cursor: pointer; border: 1px solid #ddd;" onclick="AdminModules.tech.showImage(this.src)" onerror="this.style.display=\'none\';" title="点击查看大图">').join('') + '</div>' : ''}
        ${tech.video ? '<div style="margin-top: 15px;"><strong>视频:</strong><br><video controls style="max-width: 100%; max-height: 300px; border-radius: 8px;"><source src="' + AdminCommon.format.imagePath(tech.video) + '" type="video/mp4">不支持视频播放</video></div>' : ''}
      `;

      AdminCommon.modal.create('techDetailModal', '技师详细信息', content, { width: '800px' });
      AdminCommon.modal.show('techDetailModal');
    },

    showImage: function(src) {
      const content = `<img src="${src}" style="max-width: 100%; height: auto;">`;
      AdminCommon.modal.create('imageModal', '查看图片', content, { width: '90vw' });
      AdminCommon.modal.show('imageModal');
    },

    approve: function(id) {
      AdminCommon.ui.confirm('确定要审核通过该技师吗？', () => {
        AdminCommon.api.postJSON('tech_audit.php', { id: id, action: 'approve' })
          .then(response => {
            AdminCommon.ui.alert(response.msg || '操作完成');
            if (response.success) {
              // 重新加载数据，保持当前筛选和页面
              this.refreshCurrentView();
            }
          })
          .catch(error => {
            AdminCommon.ui.alert('操作失败');
          });
      });
    },

    reject: function(id) {
      const reason = AdminCommon.ui.prompt('请输入驳回原因：');
      if (!reason || !reason.trim()) {
        AdminCommon.ui.alert('请填写驳回原因');
        return;
      }

      AdminCommon.api.postJSON('tech_audit.php', { id: id, action: 'reject', reason: reason })
        .then(response => {
          AdminCommon.ui.alert(response.msg || '操作完成');
          if (response.success) {
            // 重新加载数据，保持当前筛选和页面
            this.refreshCurrentView();
          }
        })
        .catch(error => {
          AdminCommon.ui.alert('操作失败');
        });
    },

    // 刷新当前视图，保持筛选状态和页面
    refreshCurrentView: function() {
      const currentFilter = this.currentFilter;
      const currentPage = this.currentPage;

      // 重新加载原始数据
      AdminCommon.api.get('tech_list.php')
        .then(data => {
          if (data.success && Array.isArray(data.data)) {
            AdminCommon.cache.techs = data.data;
            this.updateFilterButtons(data.data);

            // 重新筛选数据
            let filteredData = AdminCommon.cache.techs;
            if (currentFilter !== 'all') {
              filteredData = AdminCommon.cache.techs.filter(tech => {
                if (currentFilter === 'pending') return tech.status == 0 || tech.status === 'pending';
                if (currentFilter === 'approved') return tech.status == 1 || tech.status === 'approved';
                if (currentFilter === 'rejected') return tech.status == 2 || tech.status === 'rejected';
                return true;
              });
            }
            this.filteredData = filteredData;

            // 检查当前页是否还有数据，如果没有则跳转到最后一页
            const totalPages = Math.ceil(filteredData.length / this.pageSize);
            if (currentPage > totalPages && totalPages > 0) {
              this.currentPage = totalPages;
            } else {
              this.currentPage = currentPage;
            }

            // 重新渲染
            this.renderList();
          }
        })
        .catch(error => {
          console.error('刷新数据失败:', error);
        });
    },

    delete: function(id) {
      const tech = AdminCommon.cache.techs.find(t => t.id == id);
      if (!tech) {
        AdminCommon.ui.alert('未找到技师信息');
        return;
      }

      const confirmMsg = `确定要删除技师吗？\n\n昵称：${tech.nick || '未填写'}\n手机：${tech.phone || '未填写'}\n状态：${AdminCommon.format.status(tech.status, 'tech')}\n\n删除后将无法恢复，相关文件也会被删除。`;

      AdminCommon.ui.confirm(confirmMsg, () => {
        AdminCommon.api.postJSON('tech_delete.php', { id: id })
          .then(response => {
            AdminCommon.ui.alert(response.msg || '操作完成');
            if (response.success) {
              // 删除后可能需要调整页面，如果当前页没有数据了
              this.refreshCurrentView();
            }
          })
          .catch(error => {
            AdminCommon.ui.alert('删除失败');
          });
      });
    },

    showEditModal: function(id) {
      // 先从缓存获取技师数据
      let tech = AdminCommon.cache.techs.find(t => t.id == id);
      if (!tech) {
        AdminCommon.ui.alert('未找到技师信息');
        return;
      }

      // 从服务器获取最新的技师数据
      AdminCommon.api.get(`tech_detail.php?id=${id}`)
        .then(response => {
          if (response.success && response.data) {
            // 使用最新的服务器数据
            tech = response.data;
            // 更新缓存中的数据
            const index = AdminCommon.cache.techs.findIndex(t => t.id == id);
            if (index !== -1) {
              AdminCommon.cache.techs[index] = tech;
            }
          }
          // 显示编辑模态框
          this.renderEditModal(tech);
        })
        .catch(error => {
          console.warn('获取最新技师数据失败，使用缓存数据:', error);
          // 如果获取最新数据失败，使用缓存数据
          this.renderEditModal(tech);
        });
    },

    renderEditModal: function(tech) {
      const modalHtml = `
        <div class="modal" id="techEditModal" style="display:flex;">
          <div class="modal-content" style="max-width: 800px; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header">
              <h3 style="margin:0;">编辑技师资料</h3>
              <button type="button" class="modal-close" onclick="AdminModules.tech.hideEditModal()">&times;</button>
            </div>
            <div class="modal-body">
              <form id="tech-edit-form" enctype="multipart/form-data">
                <input type="hidden" name="id" value="${tech.id}">
                <input type="hidden" name="replace_lifeimg" value="0" id="replace-lifeimg-flag">

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                  <div class="form-group">
                    <label class="form-label">昵称</label>
                    <input type="text" class="form-control" name="nick" value="${tech.nick || ''}" required>
                  </div>
                  <div class="form-group">
                    <label class="form-label">手机号</label>
                    <input type="text" class="form-control" name="phone" value="${tech.phone || ''}" required>
                  </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                  <div class="form-group">
                    <label class="form-label">年龄</label>
                    <input type="number" class="form-control" name="age" value="${tech.age || ''}" required>
                  </div>
                  <div class="form-group">
                    <label class="form-label">身高(cm)</label>
                    <input type="number" class="form-control" name="height" value="${tech.height || ''}" required>
                  </div>
                  <div class="form-group">
                    <label class="form-label">体重(kg)</label>
                    <input type="number" class="form-control" name="weight" value="${tech.weight || ''}" required>
                  </div>
                  <div class="form-group">
                    <label class="form-label">尺寸</label>
                    <input type="text" class="form-control" name="size" value="${tech.size || ''}" required>
                  </div>
                  <div class="form-group">
                    <label class="form-label">你的型号</label>
                    <select class="form-control" name="model" required>
                      <option value="">请选择</option>
                      <option value="1" ${tech.model === '1' ? 'selected' : ''}>1</option>
                      <option value="0" ${tech.model === '0' ? 'selected' : ''}>0</option>
                      <option value="0.5" ${tech.model === '0.5' ? 'selected' : ''}>0.5</option>
                      <option value="不10" ${tech.model === '不10' ? 'selected' : ''}>不10</option>
                    </select>
                  </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                  <div class="form-group">
                    <label class="form-label">城市</label>
                    <select class="form-control" name="city" id="tech-edit-city" required>
                      <option value="">请选择城市</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label class="form-label">详细地址</label>
                    <input type="text" class="form-control" name="hotel" value="${tech.hotel || ''}" required>
                  </div>
                  <div class="form-group">
                    <label class="form-label">虚拟单量</label>
                    <input type="number" class="form-control" name="virtual_orders" value="${tech.virtual_orders || 0}" min="0" max="9999">
                    <small style="color: #666;">用于展示的虚拟订单数量</small>
                  </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                  <div class="form-group">
                    <label class="form-label">工作照</label>
                    ${tech.workimg ? `
                      <div style="margin-bottom: 10px; position: relative; display: inline-block;">
                        <img src="${AdminCommon.format.imagePath(tech.workimg)}" style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px; border: 1px solid #ddd;">
                        <button type="button" onclick="AdminModules.tech.deleteCurrentImage('workimg', ${tech.id})" style="
                          position: absolute; top: -5px; right: -5px; width: 20px; height: 20px;
                          background: #ff4757; color: white; border: none; border-radius: 50%;
                          cursor: pointer; font-size: 12px; display: flex; align-items: center; justify-content: center;
                        " title="删除当前工作照">×</button>
                        <button type="button" onclick="AdminModules.tech.replaceImage('workimg')" style="
                          position: absolute; bottom: -5px; right: -5px; width: 20px; height: 20px;
                          background: #2ed573; color: white; border: none; border-radius: 50%;
                          cursor: pointer; font-size: 12px; display: flex; align-items: center; justify-content: center;
                        " title="更换工作照">+</button>
                      </div>
                    ` : `
                      <button type="button" onclick="AdminModules.tech.replaceImage('workimg')" style="
                        width: 100px; height: 100px; border: 2px dashed #ddd; background: #f8f9fa;
                        border-radius: 8px; cursor: pointer; display: flex; align-items: center; justify-content: center;
                        font-size: 24px; color: #666; margin-bottom: 10px;
                      " title="添加工作照">+</button>
                    `}
                    <input type="file" class="form-control" name="workimg" accept="image/*" style="display: none;" id="workimg-input">
                    <small style="color: #666;">点击+号更换图片，点击×号删除图片</small>
                  </div>
                  <div class="form-group">
                    <label class="form-label">生活照</label>
                    ${tech.lifeimg ? `
                      <div style="margin-bottom: 10px; display: flex; flex-wrap: wrap; gap: 5px;">
                        ${tech.lifeimg.split(',').filter(img => img.trim()).map((img, index) => `
                          <div style="position: relative; display: inline-block;">
                            <img src="${AdminCommon.format.imagePath(img.trim())}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px; border: 1px solid #ddd;" onerror="AdminModules.tech.handleImageError(this, '${img.trim()}')" title="生活照预览: ${img.trim()}">
                            <button type="button" onclick="AdminModules.tech.deleteLifeImage(${tech.id}, ${index})" style="
                              position: absolute; top: -5px; right: -5px; width: 16px; height: 16px;
                              background: #ff4757; color: white; border: none; border-radius: 50%;
                              cursor: pointer; font-size: 10px; display: flex; align-items: center; justify-content: center;
                            " title="删除这张生活照">×</button>
                          </div>
                        `).join('')}
                        <button type="button" onclick="AdminModules.tech.addLifeImage()" style="
                          width: 60px; height: 60px; border: 2px dashed #ddd; background: #f8f9fa;
                          border-radius: 4px; cursor: pointer; display: flex; align-items: center; justify-content: center;
                          font-size: 20px; color: #666;
                        " title="添加生活照">+</button>
                      </div>
                    ` : `
                      <div style="margin-bottom: 10px;">
                        <button type="button" onclick="AdminModules.tech.addLifeImage()" style="
                          width: 60px; height: 60px; border: 2px dashed #ddd; background: #f8f9fa;
                          border-radius: 4px; cursor: pointer; display: flex; align-items: center; justify-content: center;
                          font-size: 20px; color: #666;
                        " title="添加生活照">+</button>
                      </div>
                    `}
                    <div style="margin-bottom: 10px;">
                      <label style="display: flex; align-items: center; gap: 5px; font-size: 12px; color: #666;">
                        <input type="checkbox" id="replace-all-lifeimg" onchange="AdminModules.tech.toggleReplaceMode(this)">
                        替换所有生活照（勾选后上传的图片将替换现有的所有生活照）
                      </label>
                    </div>
                    <input type="file" class="form-control" name="lifeimg" accept="image/*" multiple style="display: none;" id="lifeimg-input">
                    <small style="color: #666;">点击+号添加生活照，点击×号删除单张照片</small>
                  </div>
                  <div class="form-group">
                    <label class="form-label">个人视频</label>
                    ${tech.video ? `
                      <div style="margin-bottom: 10px; position: relative; display: inline-block;">
                        <video controls style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px; border: 1px solid #ddd;">
                          <source src="${AdminCommon.format.imagePath(tech.video)}" type="video/mp4">
                        </video>
                        <button type="button" onclick="AdminModules.tech.deleteCurrentImage('video', ${tech.id})" style="
                          position: absolute; top: -5px; right: -5px; width: 20px; height: 20px;
                          background: #ff4757; color: white; border: none; border-radius: 50%;
                          cursor: pointer; font-size: 12px; display: flex; align-items: center; justify-content: center;
                        " title="删除当前视频">×</button>
                        <button type="button" onclick="AdminModules.tech.replaceImage('video')" style="
                          position: absolute; bottom: -5px; right: -5px; width: 20px; height: 20px;
                          background: #2ed573; color: white; border: none; border-radius: 50%;
                          cursor: pointer; font-size: 12px; display: flex; align-items: center; justify-content: center;
                        " title="更换视频">+</button>
                      </div>
                    ` : `
                      <button type="button" onclick="AdminModules.tech.replaceImage('video')" style="
                        width: 100px; height: 100px; border: 2px dashed #ddd; background: #f8f9fa;
                        border-radius: 8px; cursor: pointer; display: flex; align-items: center; justify-content: center;
                        font-size: 24px; color: #666; margin-bottom: 10px;
                      " title="添加视频">+</button>
                    `}
                    <input type="file" class="form-control" name="video" accept="video/*" style="display: none;" id="video-input">
                    <small style="color: #666;">点击+号更换视频，点击×号删除视频</small>
                  </div>
                </div>

                <div style="display: flex; gap: 10px; margin-top: 20px;">
                  <button type="submit" class="btn btn-primary" style="flex: 1;">保存修改</button>
                  <button type="button" class="btn btn-secondary" onclick="AdminModules.tech.hideEditModal()" style="flex: 1;">取消</button>
                </div>

                <div id="tech-edit-msg" style="color:#ff6a6a;font-size:15px;display:none;margin-top:8px;"></div>
              </form>
            </div>
          </div>
        </div>
      `;

      // 移除已存在的编辑模态框
      const existingModal = document.getElementById('techEditModal');
      if (existingModal) {
        existingModal.remove();
      }

      document.body.insertAdjacentHTML('beforeend', modalHtml);

      // 绑定表单提交事件
      document.getElementById('tech-edit-form').addEventListener('submit', (e) => {
        e.preventDefault();
        this.editTech(new FormData(e.target));
      });

      // 加载城市列表
      this.loadCitiesForEdit(tech.city);

      // 绑定文件输入框事件
      this.bindFileInputEvents();
    },

    hideEditModal: function() {
      const modal = document.getElementById('techEditModal');
      if (modal) {
        modal.remove();
      }
    },

    loadCitiesForEdit: function(currentCity) {
      const citySelect = document.getElementById('tech-edit-city');
      if (!citySelect) return;

      // 显示加载状态
      citySelect.innerHTML = '<option value="">正在加载城市...</option>';

      // 获取城市列表
      AdminCommon.api.get('city_list.php')
        .then(cities => {
          if (Array.isArray(cities) && cities.length > 0) {
            // 清空选项
            citySelect.innerHTML = '<option value="">请选择城市</option>';

            // 添加城市选项（按拼音排序）
            cities.forEach(city => {
              const option = document.createElement('option');
              option.value = city.name;
              option.textContent = city.name;

              // 如果是当前城市，设为选中
              if (city.name === currentCity) {
                option.selected = true;
              }

              citySelect.appendChild(option);
            });
          } else {
            citySelect.innerHTML = '<option value="">暂无城市数据</option>';
          }
        })
        .catch(error => {
          console.error('加载城市列表失败:', error);
          citySelect.innerHTML = '<option value="">加载失败，请刷新重试</option>';
        });
    },

    editTech: function(formData) {
      const msg = document.getElementById('tech-edit-msg');
      const submitBtn = document.querySelector('#tech-edit-form button[type="submit"]');

      msg.style.display = 'none';

      // 禁用提交按钮，防止重复提交
      if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.textContent = '提交中...';
      }

      AdminCommon.api.postForm('tech_edit.php', formData)
        .then(res => {
          console.log('技师编辑响应:', res);
          msg.textContent = res.msg || (res.success ? '修改成功' : '修改失败');
          msg.style.color = res.success ? '#00c6a2' : '#ff6a6a';
          msg.style.display = 'block';

          if(res.success) {
            setTimeout(() => {
              this.hideEditModal();
              this.loadList();
            }, 1000);
          }
        })
        .catch(error => {
          console.error('技师编辑失败:', error);
          msg.textContent = `网络错误：${error.message || '修改失败'}`;
          msg.style.color = '#ff6a6a';
          msg.style.display = 'block';
        })
        .finally(() => {
          // 恢复提交按钮
          if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.textContent = '保存修改';
          }
        });
    },

    blacklist: function(id) {
      const tech = AdminCommon.cache.techs.find(t => t.id == id);
      if (!tech) {
        AdminCommon.ui.alert('未找到技师信息');
        return;
      }

      const confirmMsg = `确定要拉黑技师"${tech.nick}"吗？\n\n拉黑后该技师将无法正常使用系统功能。`;

      AdminCommon.ui.confirm(confirmMsg, () => {
        AdminCommon.api.postJSON('tech_blacklist.php', { id: id, action: 'blacklist' })
          .then(response => {
            AdminCommon.ui.alert(response.msg || '操作完成');
            if (response.success) {
              this.refreshCurrentView();
            }
          })
          .catch(error => {
            AdminCommon.ui.alert('操作失败');
          });
      });
    },

    unblacklist: function(id) {
      const tech = AdminCommon.cache.techs.find(t => t.id == id);
      if (!tech) {
        AdminCommon.ui.alert('未找到技师信息');
        return;
      }

      const confirmMsg = `确定要解除技师"${tech.nick}"的拉黑状态吗？\n\n解除后该技师可以正常使用系统功能。`;

      AdminCommon.ui.confirm(confirmMsg, () => {
        AdminCommon.api.postJSON('tech_blacklist.php', { id: id, action: 'unblacklist' })
          .then(response => {
            AdminCommon.ui.alert(response.msg || '操作完成');
            if (response.success) {
              this.refreshCurrentView();
            }
          })
          .catch(error => {
            AdminCommon.ui.alert('操作失败');
          });
      });
    },

    // 设为推荐技师
    recommend: function(id) {
      const tech = AdminCommon.cache.techs.find(t => t.id == id);
      if (!tech) {
        AdminCommon.ui.alert('未找到技师信息');
        return;
      }

      const confirmMsg = `确定要将技师"${tech.nick}"设为推荐技师吗？\n\n推荐技师将在前端优先显示。`;

      AdminCommon.ui.confirm(confirmMsg, () => {
        AdminCommon.api.postJSON('tech_recommend.php', { id: id, action: 'recommend' })
          .then(response => {
            AdminCommon.ui.alert(response.msg || '操作完成');
            if (response.success) {
              this.refreshCurrentView();
            }
          })
          .catch(error => {
            AdminCommon.ui.alert('操作失败');
          });
      });
    },

    // 取消推荐技师
    unrecommend: function(id) {
      const tech = AdminCommon.cache.techs.find(t => t.id == id);
      if (!tech) {
        AdminCommon.ui.alert('未找到技师信息');
        return;
      }

      const confirmMsg = `确定要取消技师"${tech.nick}"的推荐状态吗？\n\n取消后该技师将按普通排序显示。`;

      AdminCommon.ui.confirm(confirmMsg, () => {
        AdminCommon.api.postJSON('tech_recommend.php', { id: id, action: 'unrecommend' })
          .then(response => {
            AdminCommon.ui.alert(response.msg || '操作完成');
            if (response.success) {
              this.refreshCurrentView();
            }
          })
          .catch(error => {
            AdminCommon.ui.alert('操作失败');
          });
      });
    },

    // 设置技师为休息状态
    setResting: function(id) {
      const tech = AdminCommon.cache.techs.find(t => t.id == id);
      if (!tech) {
        AdminCommon.ui.alert('未找到技师信息');
        return;
      }

      AdminCommon.ui.confirm(`确定要设置技师 "${tech.nick}" 为休息状态吗？\n\n休息后前端将不显示该技师。`, () => {
        AdminCommon.api.post('tech_update.php', {
          id: id,
          is_resting: 1
        })
          .then(data => {
            if (data.success) {
              AdminCommon.ui.alert('设置休息成功');
              this.refreshCurrentView();
            } else {
              AdminCommon.ui.alert(data.msg || '设置休息失败');
            }
          })
          .catch(() => {
            AdminCommon.ui.alert('操作失败');
          });
      });
    },

    // 设置技师为工作状态
    setWorking: function(id) {
      const tech = AdminCommon.cache.techs.find(t => t.id == id);
      if (!tech) {
        AdminCommon.ui.alert('未找到技师信息');
        return;
      }

      AdminCommon.ui.confirm(`确定要设置技师 "${tech.nick}" 为工作状态吗？\n\n工作后前端将显示该技师。`, () => {
        AdminCommon.api.post('tech_update.php', {
          id: id,
          is_resting: 0
        })
          .then(data => {
            if (data.success) {
              AdminCommon.ui.alert('设置工作成功');
              this.refreshCurrentView();
            } else {
              AdminCommon.ui.alert(data.msg || '设置工作失败');
            }
          })
          .catch(() => {
            AdminCommon.ui.alert('操作失败');
          });
      });
    },

    // 更换图片
    replaceImage: function(type) {
      const input = document.getElementById(type + '-input');
      if (input) {
        input.click();
      }
    },

    // 删除当前图片
    deleteCurrentImage: function(type, techId) {
      AdminCommon.ui.confirm('确定要删除当前' + (type === 'workimg' ? '工作照' : '图片') + '吗？', () => {
        const formData = new FormData();
        formData.append('id', techId);
        formData.append('action', 'delete_' + type);

        AdminCommon.api.postForm('tech_edit.php', formData)
          .then(response => {
            if (response.success) {
              AdminCommon.ui.alert('删除成功');
              this.hideEditModal();
              this.refreshCurrentView();
            } else {
              AdminCommon.ui.alert(response.msg || '删除失败');
            }
          })
          .catch(error => {
            AdminCommon.ui.alert('删除失败');
          });
      });
    },

    // 添加生活照
    addLifeImage: function() {
      const input = document.getElementById('lifeimg-input');
      if (input) {
        input.click();
      }
    },

    // 绑定文件输入框事件
    bindFileInputEvents: function() {
      // 工作照文件选择事件
      const workimgInput = document.getElementById('workimg-input');
      if (workimgInput) {
        workimgInput.addEventListener('change', (e) => {
          if (e.target.files.length > 0) {
            this.previewWorkImage(e.target.files[0]);
            AdminCommon.ui.alert('已选择工作照，请点击保存按钮提交修改');
          }
        });
      }

      // 生活照文件选择事件
      const lifeimgInput = document.getElementById('lifeimg-input');
      if (lifeimgInput) {
        lifeimgInput.addEventListener('change', (e) => {
          if (e.target.files.length > 0) {
            this.previewLifeImages(e.target.files);
            AdminCommon.ui.alert(`已选择 ${e.target.files.length} 张生活照，请点击保存按钮提交修改`);
          }
        });
      }

      // 视频文件选择事件
      const videoInput = document.getElementById('video-input');
      if (videoInput) {
        videoInput.addEventListener('change', (e) => {
          if (e.target.files.length > 0) {
            this.previewVideo(e.target.files[0]);
            AdminCommon.ui.alert('已选择视频文件，请点击保存按钮提交修改');
          }
        });
      }
    },

    // 预览工作照
    previewWorkImage: function(file) {
      const reader = new FileReader();
      reader.onload = function(e) {
        // 找到工作照的表单组
        const workimgInput = document.getElementById('workimg-input');
        const workimgGroup = workimgInput ? workimgInput.closest('.form-group') : null;
        if (workimgGroup) {
          // 查找现有的预览区域或创建新的
          let previewDiv = workimgGroup.querySelector('.preview-area');
          if (!previewDiv) {
            previewDiv = document.createElement('div');
            previewDiv.className = 'preview-area';
            previewDiv.style.cssText = 'margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9;';
            workimgGroup.insertBefore(previewDiv, workimgGroup.querySelector('small'));
          }

          previewDiv.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
              <img src="${e.target.result}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px; border: 1px solid #ddd;">
              <div>
                <div style="font-size: 14px; color: #333;">新选择的工作照</div>
                <div style="font-size: 12px; color: #666;">${file.name}</div>
              </div>
            </div>
          `;
        }
      };
      reader.readAsDataURL(file);
    },

    // 预览生活照
    previewLifeImages: function(files) {
      // 找到生活照的表单组
      const lifeimgInput = document.getElementById('lifeimg-input');
      const lifeimgGroup = lifeimgInput ? lifeimgInput.closest('.form-group') : null;
      if (lifeimgGroup) {
        // 查找现有的预览区域或创建新的
        let previewDiv = lifeimgGroup.querySelector('.preview-area');
        if (!previewDiv) {
          previewDiv = document.createElement('div');
          previewDiv.className = 'preview-area';
          previewDiv.style.cssText = 'margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9;';
          lifeimgGroup.insertBefore(previewDiv, lifeimgGroup.querySelector('small'));
        }

        // 初始化预览容器
        previewDiv.innerHTML = `
          <div style="margin-bottom: 8px; font-size: 14px; color: #333;">新选择的生活照：</div>
          <div class="life-images-container" style="display: flex; flex-wrap: wrap; gap: 5px;"></div>
        `;

        const container = previewDiv.querySelector('.life-images-container');

        Array.from(files).forEach((file, index) => {
          const reader = new FileReader();
          reader.onload = function(e) {
            const imgDiv = document.createElement('div');
            imgDiv.style.cssText = 'position: relative; display: inline-block;';
            imgDiv.innerHTML = `
              <img src="${e.target.result}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px; border: 1px solid #ddd;">
              <div style="font-size: 10px; color: #666; text-align: center; margin-top: 2px;">${file.name.substring(0, 8)}...</div>
            `;
            container.appendChild(imgDiv);
          };
          reader.readAsDataURL(file);
        });
      }
    },

    // 预览视频
    previewVideo: function(file) {
      const reader = new FileReader();
      reader.onload = function(e) {
        // 找到视频的表单组
        const videoInput = document.getElementById('video-input');
        const videoGroup = videoInput ? videoInput.closest('.form-group') : null;
        if (videoGroup) {
          // 查找现有的预览区域或创建新的
          let previewDiv = videoGroup.querySelector('.preview-area');
          if (!previewDiv) {
            previewDiv = document.createElement('div');
            previewDiv.className = 'preview-area';
            previewDiv.style.cssText = 'margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9;';
            videoGroup.insertBefore(previewDiv, videoGroup.querySelector('small'));
          }

          previewDiv.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
              <video src="${e.target.result}" style="width: 80px; height: 60px; object-fit: cover; border-radius: 4px; border: 1px solid #ddd;" controls></video>
              <div>
                <div style="font-size: 14px; color: #333;">新选择的视频</div>
                <div style="font-size: 12px; color: #666;">${file.name}</div>
              </div>
            </div>
          `;
        }
      };
      reader.readAsDataURL(file);
    },

    // 删除单张生活照
    deleteLifeImage: function(techId, imageIndex) {
      AdminCommon.ui.confirm('确定要删除这张生活照吗？', () => {
        const formData = new FormData();
        formData.append('id', techId);
        formData.append('action', 'delete_lifeimg');
        formData.append('image_index', imageIndex);

        AdminCommon.api.postForm('tech_edit.php', formData)
          .then(response => {
            if (response.success) {
              AdminCommon.ui.alert('删除成功');
              this.hideEditModal();
              this.refreshCurrentView();
            } else {
              AdminCommon.ui.alert(response.msg || '删除失败');
            }
          })
          .catch(error => {
            AdminCommon.ui.alert('删除失败');
          });
      });
    },

    // 切换替换模式
    toggleReplaceMode: function(checkbox) {
      const flag = document.getElementById('replace-lifeimg-flag');
      if (flag) {
        flag.value = checkbox.checked ? '1' : '0';
      }
    },

    // 处理图片加载错误
    handleImageError: function(img, originalPath) {
      console.error('图片加载失败:', originalPath, '完整路径:', img.src);

      // 尝试不同的路径格式
      const alternatives = [
        '../uploads/' + originalPath,
        '../uploads/' + originalPath.replace(/^uploads\//, ''),
        '../images/' + originalPath.replace(/^(uploads\/|images\/)/, ''),
        '../' + originalPath
      ];

      let currentIndex = parseInt(img.dataset.retryIndex || '0');

      if (currentIndex < alternatives.length) {
        img.dataset.retryIndex = (currentIndex + 1).toString();
        img.src = alternatives[currentIndex];
        console.log('尝试备用路径:', alternatives[currentIndex]);
      } else {
        // 所有路径都失败，显示占位符
        img.style.display = 'none';
        const placeholder = document.createElement('div');
        placeholder.style.cssText = 'width: 60px; height: 60px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #666;';
        placeholder.textContent = '图片\n丢失';
        placeholder.title = '图片加载失败: ' + originalPath;
        img.parentNode.insertBefore(placeholder, img);
      }
    }
  },

  // 动态管理模块
  feed: {
    loadList: function() {
      AdminCommon.api.get('feed_list.php')
        .then(data => {
          if (data.success && Array.isArray(data.data)) {
            AdminCommon.cache.feeds = data.data;
            this.renderList(data.data);
          } else {
            document.getElementById('feed-list-container').innerHTML = '<div class="text-center text-muted">加载失败</div>';
          }
        })
        .catch(error => {
          document.getElementById('feed-list-container').innerHTML = '<div class="text-center text-muted">网络错误</div>';
        });
    },

    renderList: function(feeds) {
      const container = document.getElementById('feed-list-container');

      if (feeds.length === 0) {
        container.innerHTML = `
          <div style="text-align: center; padding: 60px 20px; color: #999;">
            <div style="font-size: 48px; margin-bottom: 16px;">📱</div>
            <div style="font-size: 16px; margin-bottom: 8px;">暂无动态数据</div>
            <div style="font-size: 14px;">用户还没有发布任何动态</div>
          </div>
        `;
        return;
      }

      // 使用列表式布局
      container.innerHTML = `
        <div style="display: flex; flex-direction: column; gap: 16px; padding: 20px 0;">
          ${feeds.map(feed => `
            <div style="
              background: #fff;
              border: 1px solid #e8e8e8;
              border-radius: 12px;
              padding: 20px;
              box-shadow: 0 2px 8px rgba(0,0,0,0.06);
              transition: all 0.3s ease;
              position: relative;
            " onmouseover="this.style.boxShadow='0 4px 16px rgba(0,0,0,0.12)'"
               onmouseout="this.style.boxShadow='0 2px 8px rgba(0,0,0,0.06)'">

              <!-- 用户信息和时间 -->
              <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 16px;">
                <div style="display: flex; align-items: center; gap: 12px;">
                  <div style="
                    width: 48px;
                    height: 48px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 18px;
                    font-weight: bold;
                  ">
                    ${feed.user_nick ? feed.user_nick.charAt(0).toUpperCase() : '👤'}
                  </div>
                  <div>
                    <div style="font-size: 16px; font-weight: 600; color: #333;">
                      ${feed.user_nick || '未知用户'}
                    </div>
                    <div style="font-size: 13px; color: #888;">
                      ${feed.user_phone || ''} • ${feed.time_ago}
                    </div>
                  </div>
                </div>

                <div style="display: flex; align-items: center; gap: 8px;">
                  <!-- 状态标识 -->
                  <span style="
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: 600;
                    ${feed.is_public == 1 ?
                      'background: #d4edda; color: #155724;' :
                      'background: #fff3cd; color: #856404;'
                    }
                  ">
                    ${feed.is_public == 1 ? '🌍 公开' : '🔒 私密'}
                  </span>

                  <!-- 删除按钮 -->
                  <button
                    onclick="AdminModules.feed.delete(${feed.id})"
                    style="
                      background: #dc3545;
                      color: white;
                      border: none;
                      padding: 6px 12px;
                      border-radius: 6px;
                      font-size: 12px;
                      cursor: pointer;
                      transition: all 0.2s ease;
                    "
                    onmouseover="this.style.background='#c82333'; this.style.transform='scale(1.05)'"
                    onmouseout="this.style.background='#dc3545'; this.style.transform='scale(1)'">
                    🗑️ 删除
                  </button>
                </div>
              </div>

              <!-- 动态内容 -->
              <div style="margin-bottom: 16px;">
                <div style="font-size: 15px; line-height: 1.6; color: #333; margin-bottom: 12px;">
                  ${feed.content}
                </div>

                ${feed.location ? `
                  <div style="font-size: 13px; color: #888; margin-bottom: 8px;">
                    📍 ${feed.location}
                  </div>
                ` : ''}

                <!-- 图片展示 -->
                ${feed.images_array && feed.images_array.length > 0 ? `
                  <div style="display: flex; gap: 8px; flex-wrap: wrap; margin-bottom: 12px;">
                    ${feed.images_array.slice(0, 4).map(img => `
                      <img src="${AdminCommon.format.imagePath(img)}"
                           style="width: 80px; height: 80px; object-fit: cover; border-radius: 8px; cursor: pointer;"
                           onclick="window.open('${AdminCommon.format.imagePath(img)}', '_blank')">
                    `).join('')}
                    ${feed.images_array.length > 4 ? `
                      <div style="width: 80px; height: 80px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #666; font-size: 12px;">
                        +${feed.images_array.length - 4}
                      </div>
                    ` : ''}
                  </div>
                ` : ''}

                <!-- 视频展示 -->
                ${feed.video ? `
                  <div style="margin-bottom: 12px;">
                    <video controls style="max-width: 200px; height: 120px; border-radius: 8px;">
                      <source src="${AdminCommon.format.imagePath(feed.video)}" type="video/mp4">
                    </video>
                  </div>
                ` : ''}
              </div>

              <!-- 统计信息 -->
              <div style="display: flex; gap: 20px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
                <div style="display: flex; align-items: center; gap: 6px; color: #666; font-size: 14px;">
                  ❤️ ${feed.likes_count} 点赞
                </div>
                <div style="display: flex; align-items: center; gap: 6px; color: #666; font-size: 14px;">
                  💬 ${feed.comments_count} 评论
                </div>
                <div style="display: flex; align-items: center; gap: 6px; color: #666; font-size: 14px;">
                  🆔 ID: ${feed.id}
                </div>
              </div>
            </div>
          `).join('')}
        </div>
      `;
    },

    delete: function(id) {
      const feed = AdminCommon.cache.feeds.find(f => f.id == id);
      if (!feed) {
        AdminCommon.ui.alert('未找到动态信息');
        return;
      }

      const userName = feed.user_nick || '未知用户';
      const contentPreview = feed.content_preview || feed.content.substring(0, 50) + '...';
      const confirmMsg = `确定要删除这条动态吗？\n\n用户：${userName}\n内容：${contentPreview}\n\n⚠️ 删除后将无法恢复，相关图片和视频也会被删除！`;

      if (confirm(confirmMsg)) {
        // 显示删除中状态
        const deleteBtn = document.querySelector(`[onclick*="AdminModules.feed.delete(${id})"]`);
        if (deleteBtn) {
          deleteBtn.disabled = true;
          deleteBtn.innerHTML = '⏳ 删除中...';
          deleteBtn.style.opacity = '0.6';
        }

        AdminCommon.api.postJSON('feed_delete.php', { id: id })
          .then(response => {
            if (response.success) {
              // 显示成功动画
              if (deleteBtn) {
                deleteBtn.innerHTML = '✅ 已删除';
                deleteBtn.style.background = '#28a745';
              }

              // 延迟刷新列表，让用户看到反馈
              setTimeout(() => {
                this.loadList();
              }, 800);
            } else {
              // 恢复按钮状态
              if (deleteBtn) {
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = '🗑️ 删除';
                deleteBtn.style.opacity = '1';
                deleteBtn.style.background = '#dc3545';
              }
              AdminCommon.ui.alert('删除失败：' + (response.msg || '未知错误'));
            }
          })
          .catch(error => {
            // 恢复按钮状态
            if (deleteBtn) {
              deleteBtn.disabled = false;
              deleteBtn.innerHTML = '🗑️ 删除';
              deleteBtn.style.opacity = '1';
              deleteBtn.style.background = '#dc3545';
            }
            AdminCommon.ui.alert('网络错误，删除失败');
          });
      }
    }
  },

  // 服务管理模块
  service: {
    loadList: function() {
      AdminCommon.api.get('service_list.php')
        .then(data => {
          if (data.success && Array.isArray(data.data)) {
            AdminCommon.cache.services = data.data;
            this.renderList(data.data);
          } else {
            document.getElementById('service-list-container').innerHTML = '<div class="text-center text-muted">加载失败</div>';
          }
        })
        .catch(error => {
          document.getElementById('service-list-container').innerHTML = '<div class="text-center text-muted">网络错误</div>';
        });
    },

    renderList: function(services) {
      const container = document.getElementById('service-list-container');

      if (services.length === 0) {
        container.innerHTML = `
          <div style="text-align: center; padding: 60px 20px; color: #999;">
            <div style="font-size: 48px; margin-bottom: 16px;">🛍️</div>
            <div style="font-size: 16px; margin-bottom: 8px;">暂无服务项目</div>
            <div style="font-size: 14px;">点击上方"添加服务项目"按钮添加服务</div>
          </div>
        `;
        return;
      }

      // 使用卡片式布局
      container.innerHTML = `
        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); gap: 20px; padding: 20px 0;">
          ${services.map(service => `
            <div style="
              background: #fff;
              border: 1px solid #e8e8e8;
              border-radius: 12px;
              padding: 20px;
              box-shadow: 0 2px 8px rgba(0,0,0,0.06);
              transition: all 0.3s ease;
              position: relative;
              overflow: hidden;
            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 16px rgba(0,0,0,0.12)'"
               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.06)'">

              <!-- 状态标识 -->
              <div style="
                position: absolute;
                top: 12px;
                right: 12px;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 600;
                ${service.is_active == 1 ?
                  'background: #d4edda; color: #155724;' :
                  'background: #f8d7da; color: #721c24;'
                }
              ">
                ${service.is_active == 1 ? '✅ 激活' : '❌ 停用'}
              </div>

              <!-- 服务图片 -->
              <div style="margin-bottom: 16px;">
                ${service.image ?
                  `<img src="${AdminCommon.format.imagePath(service.image)}" style="width: 100%; height: 120px; object-fit: cover; border-radius: 8px;">` :
                  `<div style="width: 100%; height: 120px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">🛍️</div>`
                }
              </div>

              <!-- 服务信息 -->
              <div style="margin-bottom: 16px;">
                <div style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 8px;">
                  ${service.name}
                </div>
                <div style="font-size: 13px; color: #888; margin-bottom: 4px;">
                  编号: ${service.service_code} | 类型: ${service.service_type_text}
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                  <span style="font-size: 14px; color: #666;">时长: ${service.duration_text}</span>
                  <span style="font-size: 16px; font-weight: 600; color: #e74c3c;">¥${service.price}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                  <span style="font-size: 13px; color: #888;">虚拟销量: ${service.virtual_sales}</span>
                  <span style="font-size: 13px; color: #666;">排序: ${service.sort_order}</span>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                <button onclick="AdminModules.service.moveUp(${service.id})" style="flex: 1; min-width: 50px; height: 32px; font-size: 12px; padding: 4px 8px; background: #17a2b8; color: white; border: none; border-radius: 6px; cursor: pointer;">↑上移</button>
                <button onclick="AdminModules.service.moveDown(${service.id})" style="flex: 1; min-width: 50px; height: 32px; font-size: 12px; padding: 4px 8px; background: #17a2b8; color: white; border: none; border-radius: 6px; cursor: pointer;">↓下移</button>
                ${service.is_active == 1 ?
                  `<button onclick="AdminModules.service.toggleStatus(${service.id}, 'deactivate')" style="flex: 1; min-width: 50px; height: 32px; font-size: 12px; padding: 4px 8px; background: #6c757d; color: white; border: none; border-radius: 6px; cursor: pointer;">停用</button>` :
                  `<button onclick="AdminModules.service.toggleStatus(${service.id}, 'activate')" style="flex: 1; min-width: 50px; height: 32px; font-size: 12px; padding: 4px 8px; background: #28a745; color: white; border: none; border-radius: 6px; cursor: pointer;">激活</button>`
                }
                <button onclick="AdminModules.service.delete(${service.id})" style="flex: 1; min-width: 50px; height: 32px; font-size: 12px; padding: 4px 8px; background: #dc3545; color: white; border: none; border-radius: 6px; cursor: pointer;">删除</button>
              </div>
            </div>
          `).join('')}
        </div>
      `;
    },

    showAddModal: function() {
      const modalHtml = `
        <div class="modal" id="serviceAddModal" style="display:flex;">
          <div class="modal-content" style="max-width: 600px; border-radius: 16px; overflow: hidden;">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 24px; text-align: center;">
              <h3 style="margin: 0; font-size: 20px; font-weight: 600;">🛍️ 添加服务项目</h3>
              <button type="button" class="modal-close" onclick="AdminModules.service.hideAddModal()" style="position: absolute; top: 16px; right: 16px; background: rgba(255,255,255,0.2); border: none; color: white; width: 32px; height: 32px; border-radius: 50%; cursor: pointer; font-size: 18px;">&times;</button>
            </div>
            <div class="modal-body" style="padding: 32px;">
              <form id="serviceAddForm" enctype="multipart/form-data" style="display: flex; flex-direction: column; gap: 20px;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                  <div class="form-group">
                    <label class="form-label" style="display: block; font-size: 15px; font-weight: 500; color: #333; margin-bottom: 8px;">
                      <span style="color: #f56c6c; margin-right: 4px;">*</span>项目名称
                    </label>
                    <input type="text" name="name" required maxlength="100" placeholder="请输入项目名称" style="width: 100%; height: 40px; border: 2px solid #e8e8e8; border-radius: 8px; padding: 0 12px; font-size: 14px; outline: none;" onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e8e8e8'">
                  </div>
                  <div class="form-group">
                    <label class="form-label" style="display: block; font-size: 15px; font-weight: 500; color: #333; margin-bottom: 8px;">
                      <span style="color: #f56c6c; margin-right: 4px;">*</span>项目编号
                    </label>
                    <input type="text" name="service_code" required maxlength="50" placeholder="如: SV001" style="width: 100%; height: 40px; border: 2px solid #e8e8e8; border-radius: 8px; padding: 0 12px; font-size: 14px; outline: none;" onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e8e8e8'">
                  </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px;">
                  <div class="form-group">
                    <label class="form-label" style="display: block; font-size: 15px; font-weight: 500; color: #333; margin-bottom: 8px;">
                      <span style="color: #f56c6c; margin-right: 4px;">*</span>项目时长(分钟)
                    </label>
                    <input type="number" name="duration" required min="1" max="999" placeholder="60" style="width: 100%; height: 40px; border: 2px solid #e8e8e8; border-radius: 8px; padding: 0 12px; font-size: 14px; outline: none;" onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e8e8e8'">
                  </div>
                  <div class="form-group">
                    <label class="form-label" style="display: block; font-size: 15px; font-weight: 500; color: #333; margin-bottom: 8px;">
                      <span style="color: #f56c6c; margin-right: 4px;">*</span>项目价格(元)
                    </label>
                    <input type="number" name="price" required min="0.01" step="0.01" placeholder="99.00" style="width: 100%; height: 40px; border: 2px solid #e8e8e8; border-radius: 8px; padding: 0 12px; font-size: 14px; outline: none;" onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e8e8e8'">
                  </div>
                  <div class="form-group">
                    <label class="form-label" style="display: block; font-size: 15px; font-weight: 500; color: #333; margin-bottom: 8px;">虚拟购买量</label>
                    <input type="number" name="virtual_sales" min="0" placeholder="0" style="width: 100%; height: 40px; border: 2px solid #e8e8e8; border-radius: 8px; padding: 0 12px; font-size: 14px; outline: none;" onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e8e8e8'">
                  </div>
              </div>

              <div class="form-group">
                <label class="form-label" style="display: block; font-size: 15px; font-weight: 500; color: #333; margin-bottom: 8px;">
                  <span style="color: #f56c6c; margin-right: 4px;">*</span>项目类型
                </label>
                <select name="service_type" required style="width: 100%; height: 40px; border: 2px solid #e8e8e8; border-radius: 8px; padding: 0 12px; font-size: 14px; outline: none;" onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e8e8e8'">
                  <option value="spa">🏨 会所项目</option>
                  <option value="studio">🏠 工作室项目</option>
                  <option value="ts">👩‍🦱 TS项目</option>
                  <option value="cd">👗 CD项目</option>
                </select>
              </div>

              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                </div>

                <div class="form-group">
                  <label class="form-label" style="display: block; font-size: 15px; font-weight: 500; color: #333; margin-bottom: 8px;">项目图片</label>
                  <input type="file" name="image" accept="image/*" style="width: 100%; height: 40px; border: 2px solid #e8e8e8; border-radius: 8px; padding: 8px 12px; font-size: 14px; outline: none;" onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e8e8e8'">
                  <div style="font-size: 12px; color: #888; margin-top: 6px;">
                    💡 支持JPG、PNG、GIF格式，大小不超过5MB
                  </div>
                </div>

                <div class="form-group">
                  <label class="form-label" style="display: block; font-size: 15px; font-weight: 500; color: #333; margin-bottom: 8px;">状态</label>
                  <select name="is_active" style="width: 100%; height: 40px; border: 2px solid #e8e8e8; border-radius: 8px; padding: 0 12px; font-size: 14px; outline: none;" onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e8e8e8'">
                    <option value="1">激活</option>
                    <option value="0">停用</option>
                  </select>
                </div>

                <div style="display: flex; gap: 12px;">
                  <button type="submit" style="flex: 1; height: 44px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 8px; color: white; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(102, 126, 234, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    ✅ 添加服务项目
                  </button>
                  <button type="button" onclick="AdminModules.service.hideAddModal()" style="flex: 1; height: 44px; background: #f8f9fa; border: 1px solid #e8e8e8; border-radius: 8px; color: #666; font-size: 16px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.background='#e9ecef'" onmouseout="this.style.background='#f8f9fa'">
                    ❌ 取消
                  </button>
                </div>

                <div id="service-add-msg" style="display: none; padding: 12px; border-radius: 8px; font-size: 14px; text-align: center;"></div>
              </form>
            </div>
          </div>
        </div>
      `;

      // 移除已存在的模态框
      const existingModal = document.getElementById('serviceAddModal');
      if (existingModal) {
        existingModal.remove();
      }

      document.body.insertAdjacentHTML('beforeend', modalHtml);

      // 绑定表单提交事件
      document.getElementById('serviceAddForm').addEventListener('submit', (e) => {
        e.preventDefault();
        const formData = new FormData(e.target);
        this.add(formData);
      });

      // 自动聚焦到项目名称输入框
      setTimeout(() => {
        const nameInput = document.querySelector('#serviceAddForm input[name="name"]');
        if (nameInput) nameInput.focus();
      }, 100);
    },

    hideAddModal: function() {
      const modal = document.getElementById('serviceAddModal');
      if (modal) {
        modal.remove();
      }
    },

    add: function(formData) {
      const msgDiv = document.getElementById('service-add-msg');
      const submitBtn = document.querySelector('#serviceAddForm button[type="submit"]');

      // 显示加载状态
      submitBtn.disabled = true;
      submitBtn.textContent = '⏳ 添加中...';
      msgDiv.style.display = 'none';

      AdminCommon.api.postForm('service_add.php', formData)
        .then(response => {
          submitBtn.disabled = false;
          submitBtn.innerHTML = '✅ 添加服务项目';

          msgDiv.style.display = 'block';
          msgDiv.textContent = response.msg || '操作完成';

          if (response.success) {
            msgDiv.style.background = '#d4edda';
            msgDiv.style.color = '#155724';
            msgDiv.style.border = '1px solid #c3e6cb';

            // 延迟关闭模态框并刷新列表
            setTimeout(() => {
              this.hideAddModal();
              this.loadList();
            }, 1000);
          } else {
            msgDiv.style.background = '#f8d7da';
            msgDiv.style.color = '#721c24';
            msgDiv.style.border = '1px solid #f5c6cb';
          }
        })
        .catch(error => {
          submitBtn.disabled = false;
          submitBtn.innerHTML = '✅ 添加服务项目';

          msgDiv.style.display = 'block';
          msgDiv.textContent = '网络错误，添加失败';
          msgDiv.style.background = '#f8d7da';
          msgDiv.style.color = '#721c24';
          msgDiv.style.border = '1px solid #f5c6cb';
        });
    },

    toggleStatus: function(id, action) {
      const service = AdminCommon.cache.services.find(s => s.id == id);
      if (!service) {
        AdminCommon.ui.alert('未找到服务信息');
        return;
      }

      const actionText = action === 'activate' ? '激活' : '停用';
      const confirmMsg = `确定要${actionText}服务项目「${service.name}」吗？`;

      if (confirm(confirmMsg)) {
        AdminCommon.api.postJSON('service_toggle.php', { id: id, action: action })
          .then(response => {
            AdminCommon.ui.alert(response.msg || '操作完成');
            if (response.success) {
              this.loadList();
            }
          })
          .catch(error => {
            AdminCommon.ui.alert('操作失败');
          });
      }
    },

    delete: function(id) {
      const service = AdminCommon.cache.services.find(s => s.id == id);
      if (!service) {
        AdminCommon.ui.alert('未找到服务信息');
        return;
      }

      const confirmMsg = `确定要删除服务项目「${service.name}」吗？\n\n⚠️ 删除后将无法恢复，相关图片也会被删除！`;

      if (confirm(confirmMsg)) {
        AdminCommon.api.postJSON('service_delete.php', { id: id })
          .then(response => {
            AdminCommon.ui.alert(response.msg || '操作完成');
            if (response.success) {
              this.loadList();
            }
          })
          .catch(error => {
            AdminCommon.ui.alert('操作失败');
          });
      }
    },

    // 上移服务
    moveUp: function(id) {
      this.moveService(id, 'up');
    },

    // 下移服务
    moveDown: function(id) {
      this.moveService(id, 'down');
    },

    // 移动服务排序
    moveService: function(id, direction) {
      const service = AdminCommon.cache.services.find(s => s.id == id);
      if (!service) {
        AdminCommon.ui.alert('未找到服务信息');
        return;
      }

      AdminCommon.api.postJSON('service_sort.php', { id: id, direction: direction })
        .then(response => {
          AdminCommon.ui.alert(response.msg || '操作完成');
          if (response.success) {
            this.loadList(); // 重新加载列表
          }
        })
        .catch(error => {
          AdminCommon.ui.alert('排序失败');
        });
    }
  },

  // 城市管理模块
  city: {
    loadList: function() {
      const container = document.getElementById('city-list-container');
      AdminCommon.ui.showLoading(container);

      AdminCommon.api.get('city_list.php')
        .then(data => {
          if (Array.isArray(data) && data.length > 0) {
            this.renderList(data);
          } else {
            AdminCommon.ui.showEmpty(container, '暂无城市数据');
          }
        })
        .catch(error => {
          AdminCommon.ui.showError(container, '加载城市数据失败');
        });
    },

    renderList: function(cities) {
      const container = document.getElementById('city-list-container');

      if (cities.length === 0) {
        container.innerHTML = `
          <div style="text-align: center; padding: 60px 20px; color: #999;">
            <div style="font-size: 48px; margin-bottom: 16px;">🏙️</div>
            <div style="font-size: 16px; margin-bottom: 8px;">暂无城市数据</div>
            <div style="font-size: 14px;">点击上方"新增城市"按钮添加城市</div>
          </div>
        `;
        return;
      }

      // 使用卡片式布局
      container.innerHTML = `
        <div style="margin-bottom: 20px; display: flex; gap: 12px; align-items: center;">
          <button
            class="btn btn-primary"
            onclick="AdminModules.city.showAddModal()"
            style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; padding: 10px 20px; border-radius: 8px; color: white; font-weight: 500;">
            ➕ 新增城市
          </button>
          <button
            class="btn btn-success"
            onclick="AdminModules.city.batchImport()"
            style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none; padding: 10px 20px; border-radius: 8px; color: white; font-weight: 500;">
            📥 批量导入热门城市
          </button>
          <button
            class="btn btn-warning"
            onclick="AdminModules.city.resetCities()"
            style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); border: none; padding: 10px 20px; border-radius: 8px; color: white; font-weight: 500;">
            🔄 重置为热门城市
          </button>
          <div style="color: #666; font-size: 14px;">
            共 ${cities.length} 个城市
            <span style="color: #ffc107;">⭐ ${cities.filter(city => city.is_hot == 1).length} 个热门</span>
          </div>
        </div>
        <div style="display: grid; grid-template-columns: repeat(10, 1fr); gap: 8px; padding: 20px 0;">
          ${cities.map(city => `
            <div style="
              background: #fff;
              border: 1px solid #e8e8e8;
              border-radius: 8px;
              padding: 12px 8px;
              text-align: center;
              position: relative;
              overflow: hidden;
              transition: all 0.2s ease;
              cursor: pointer;
              min-height: 80px;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.1)'; this.style.borderColor='#667eea'"
               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'; this.style.borderColor='#e8e8e8'">

              <!-- 城市信息 -->
              <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                <div style="
                  font-size: 12px;
                  color: #667eea;
                  margin-bottom: 4px;
                  font-weight: 500;
                ">
                  🏙️
                </div>
                <div style="
                  font-size: 13px;
                  font-weight: 600;
                  color: #333;
                  margin-bottom: 2px;
                  line-height: 1.2;
                  word-break: break-all;
                ">
                  ${city.name}
                  ${city.is_hot ? '<span style="color: #ffc107; font-size: 10px;">⭐</span>' : ''}
                </div>
                <div style="font-size: 10px; color: #999;">
                  #${city.id}
                </div>
              </div>

              <!-- 操作按钮 -->
              <div style="margin-top: 8px; display: flex; gap: 2px;">
                <button
                  onclick="AdminModules.city.toggleHot(${city.id}, ${city.is_hot ? 0 : 1})"
                  style="
                    flex: 1;
                    height: 24px;
                    font-size: 9px;
                    border-radius: 4px;
                    background: ${city.is_hot ? '#ffc107' : '#28a745'};
                    border: none;
                    color: ${city.is_hot ? '#212529' : 'white'};
                    cursor: pointer;
                    transition: all 0.2s ease;
                    padding: 0;
                  "
                  onmouseover="this.style.opacity='0.8'"
                  onmouseout="this.style.opacity='1'"
                  title="${city.is_hot ? '取消热门: ' + city.name : '设为热门: ' + city.name}">
                  ${city.is_hot ? '⭐' : '☆'}
                </button>
                <button
                  onclick="AdminModules.city.delete(${city.id})"
                  style="
                    flex: 1;
                    height: 24px;
                    font-size: 10px;
                    border-radius: 4px;
                    background: #dc3545;
                    border: none;
                    color: white;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    padding: 0;
                  "
                  onmouseover="this.style.background='#c82333'"
                  onmouseout="this.style.background='#dc3545'"
                  title="删除城市: ${city.name}">
                  🗑️
                </button>
              </div>
            </div>
          `).join('')}
        </div>
      `;
    },

    showAddModal: function() {
      const modalHtml = `
        <div class="modal" id="cityAddModal" style="display:flex;">
          <div class="modal-content" style="max-width: 480px; border-radius: 16px; overflow: hidden;">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 24px; text-align: center;">
              <h3 style="margin: 0; font-size: 20px; font-weight: 600;">🏙️ 新增城市</h3>
              <button type="button" class="modal-close" onclick="AdminModules.city.hideAddModal()" style="position: absolute; top: 16px; right: 16px; background: rgba(255,255,255,0.2); border: none; color: white; width: 32px; height: 32px; border-radius: 50%; cursor: pointer; font-size: 18px;">&times;</button>
            </div>
            <div class="modal-body" style="padding: 32px;">
              <form id="cityAddForm" style="display: flex; flex-direction: column; gap: 24px;">
                <div class="form-group">
                  <label class="form-label" style="display: block; font-size: 15px; font-weight: 500; color: #333; margin-bottom: 8px;">
                    <span style="color: #f56c6c; margin-right: 4px;">*</span>城市名称
                  </label>
                  <input
                    type="text"
                    class="form-control"
                    name="name"
                    required
                    maxlength="20"
                    placeholder="请输入城市名称"
                    style="
                      width: 100%;
                      height: 44px;
                      border: 2px solid #e8e8e8;
                      border-radius: 8px;
                      padding: 0 16px;
                      font-size: 15px;
                      transition: border-color 0.3s ease;
                      outline: none;
                    "
                    onfocus="this.style.borderColor='#667eea'"
                    onblur="this.style.borderColor='#e8e8e8'">
                  <div style="font-size: 12px; color: #888; margin-top: 6px;">
                    💡 请输入准确的城市名称，如：北京、上海、深圳等
                  </div>
                </div>

                <div style="display: flex; gap: 12px;">
                  <button
                    type="submit"
                    class="btn btn-primary"
                    style="
                      flex: 1;
                      height: 44px;
                      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                      border: none;
                      border-radius: 8px;
                      color: white;
                      font-size: 16px;
                      font-weight: 600;
                      cursor: pointer;
                      transition: all 0.3s ease;
                    "
                    onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(102, 126, 234, 0.4)'"
                    onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    ✅ 保存城市
                  </button>
                  <button
                    type="button"
                    onclick="AdminModules.city.hideAddModal()"
                    style="
                      flex: 1;
                      height: 44px;
                      background: #f8f9fa;
                      border: 1px solid #e8e8e8;
                      border-radius: 8px;
                      color: #666;
                      font-size: 16px;
                      cursor: pointer;
                      transition: all 0.3s ease;
                    "
                    onmouseover="this.style.background='#e9ecef'"
                    onmouseout="this.style.background='#f8f9fa'">
                    ❌ 取消
                  </button>
                </div>

                <div id="city-add-msg" style="display: none; padding: 12px; border-radius: 8px; font-size: 14px; text-align: center;"></div>
              </form>
            </div>
          </div>
        </div>
      `;

      // 移除已存在的模态框
      const existingModal = document.getElementById('cityAddModal');
      if (existingModal) {
        existingModal.remove();
      }

      document.body.insertAdjacentHTML('beforeend', modalHtml);

      // 绑定表单提交事件
      document.getElementById('cityAddForm').addEventListener('submit', (e) => {
        e.preventDefault();
        const formData = new FormData(e.target);
        this.add(formData);
      });

      // 自动聚焦到输入框
      setTimeout(() => {
        const nameInput = document.querySelector('#cityAddForm input[name="name"]');
        if (nameInput) nameInput.focus();
      }, 100);
    },

    hideAddModal: function() {
      const modal = document.getElementById('cityAddModal');
      if (modal) {
        modal.remove();
      }
    },

    add: function(formData) {
      const msgDiv = document.getElementById('city-add-msg');
      const submitBtn = document.querySelector('#cityAddForm button[type="submit"]');

      // 显示加载状态
      submitBtn.disabled = true;
      submitBtn.textContent = '⏳ 保存中...';
      msgDiv.style.display = 'none';

      AdminCommon.api.postForm('city_add.php', formData)
        .then(response => {
          submitBtn.disabled = false;
          submitBtn.innerHTML = '✅ 保存城市';

          msgDiv.style.display = 'block';
          msgDiv.textContent = response.msg || '操作完成';

          if (response.success) {
            msgDiv.style.background = '#d4edda';
            msgDiv.style.color = '#155724';
            msgDiv.style.border = '1px solid #c3e6cb';

            // 延迟关闭模态框并刷新列表
            setTimeout(() => {
              this.hideAddModal();
              this.loadList();
            }, 1000);
          } else {
            msgDiv.style.background = '#f8d7da';
            msgDiv.style.color = '#721c24';
            msgDiv.style.border = '1px solid #f5c6cb';
          }
        })
        .catch(error => {
          submitBtn.disabled = false;
          submitBtn.innerHTML = '✅ 保存城市';

          msgDiv.style.display = 'block';
          msgDiv.textContent = '网络错误，保存失败';
          msgDiv.style.background = '#f8d7da';
          msgDiv.style.color = '#721c24';
          msgDiv.style.border = '1px solid #f5c6cb';
        });
    },

    delete: function(id) {
      // 找到要删除的城市信息
      const cityCards = document.querySelectorAll('[onclick*="AdminModules.city.delete(' + id + ')"]');
      let cityName = '该城市';

      if (cityCards.length > 0) {
        const card = cityCards[0].closest('div');
        const nameElement = card.querySelector('div[style*="font-size: 18px"]');
        if (nameElement) {
          cityName = nameElement.textContent.trim();
        }
      }

      const confirmMsg = `🗑️ 确定要删除城市"${cityName}"吗？\n\n⚠️ 删除后将无法恢复，请谨慎操作！`;

      if (confirm(confirmMsg)) {
        // 显示删除中状态
        const deleteBtn = document.querySelector('[onclick*="AdminModules.city.delete(' + id + ')"]');
        if (deleteBtn) {
          deleteBtn.disabled = true;
          deleteBtn.innerHTML = '⏳ 删除中...';
          deleteBtn.style.opacity = '0.6';
        }

        AdminCommon.api.postJSON('city_delete.php', { id: id })
          .then(response => {
            if (response.success) {
              // 显示成功动画
              if (deleteBtn) {
                deleteBtn.innerHTML = '✅ 已删除';
                deleteBtn.style.background = '#28a745';
              }

              // 延迟刷新列表，让用户看到反馈
              setTimeout(() => {
                this.loadList();
              }, 800);
            } else {
              // 恢复按钮状态
              if (deleteBtn) {
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = '🗑️ 删除';
                deleteBtn.style.opacity = '1';
              }
              alert('删除失败：' + (response.msg || '未知错误'));
            }
          })
          .catch(error => {
            // 恢复按钮状态
            if (deleteBtn) {
              deleteBtn.disabled = false;
              deleteBtn.innerHTML = '🗑️ 删除';
              deleteBtn.style.opacity = '1';
            }
            alert('网络错误，删除失败');
          });
      }
    },

    batchImport: function() {
      const confirmMsg = `📥 批量导入热门城市数据\n\n这将导入中国主要的热门大城市（约120个城市）\n包括直辖市、省会城市、副省级城市和经济发达的地级市\n已存在的城市将被跳过，不会重复添加\n\n确定要开始导入吗？`;

      if (confirm(confirmMsg)) {
        // 显示导入进度模态框
        this.showImportModal();

        // 开始导入
        AdminCommon.api.get('batch_add_cities.php')
          .then(response => {
            this.updateImportProgress(response);
          })
          .catch(error => {
            this.updateImportProgress({
              success: false,
              msg: '导入失败: ' + error.message
            });
          });
      }
    },

    showImportModal: function() {
      const modalHtml = `
        <div class="modal" id="cityImportModal" style="display:flex;">
          <div class="modal-content" style="max-width: 600px; border-radius: 16px; overflow: hidden;">
            <div class="modal-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 24px; text-align: center;">
              <h3 style="margin: 0; font-size: 20px; font-weight: 600;">📥 批量导入热门城市</h3>
            </div>
            <div class="modal-body" style="padding: 32px; text-align: center;">
              <div id="import-progress" style="margin-bottom: 20px;">
                <div style="font-size: 48px; margin-bottom: 16px;">⏳</div>
                <div style="font-size: 18px; color: #333; margin-bottom: 8px;">正在导入城市数据...</div>
                <div style="font-size: 14px; color: #666;">请稍候，这可能需要几秒钟</div>
              </div>
              <div id="import-result" style="display: none;"></div>
              <div id="import-actions" style="display: none; margin-top: 20px;">
                <button
                  class="btn btn-primary"
                  onclick="AdminModules.city.hideImportModal(); AdminModules.city.loadList();"
                  style="background: #007bff; border: none; padding: 10px 20px; border-radius: 8px; color: white;">
                  ✅ 完成
                </button>
              </div>
            </div>
          </div>
        </div>
      `;

      // 移除已存在的模态框
      const existingModal = document.getElementById('cityImportModal');
      if (existingModal) {
        existingModal.remove();
      }

      document.body.insertAdjacentHTML('beforeend', modalHtml);
    },

    updateImportProgress: function(response) {
      const progressDiv = document.getElementById('import-progress');
      const resultDiv = document.getElementById('import-result');
      const actionsDiv = document.getElementById('import-actions');

      progressDiv.style.display = 'none';
      resultDiv.style.display = 'block';
      actionsDiv.style.display = 'block';

      if (response.success) {
        const data = response.data;
        resultDiv.innerHTML = `
          <div style="font-size: 48px; margin-bottom: 16px;">✅</div>
          <div style="font-size: 18px; color: #28a745; margin-bottom: 16px; font-weight: 600;">导入完成！</div>
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: left;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
              <div style="text-align: center; padding: 12px; background: white; border-radius: 6px;">
                <div style="font-size: 24px; font-weight: bold; color: #28a745;">${data.success}</div>
                <div style="font-size: 12px; color: #666;">新增成功</div>
              </div>
              <div style="text-align: center; padding: 12px; background: white; border-radius: 6px;">
                <div style="font-size: 24px; font-weight: bold; color: #ffc107;">${data.skipped}</div>
                <div style="font-size: 12px; color: #666;">已存在跳过</div>
              </div>
            </div>
            <div style="font-size: 14px; color: #333;">
              <div><strong>总计:</strong> ${data.total} 个城市</div>
              <div><strong>新增:</strong> ${data.success} 个</div>
              <div><strong>跳过:</strong> ${data.skipped} 个</div>
              <div><strong>错误:</strong> ${data.errors} 个</div>
            </div>
            ${data.errors > 0 ? `
              <div style="margin-top: 12px; padding: 8px; background: #fff3cd; border-radius: 4px; font-size: 12px;">
                <strong>错误详情:</strong><br>
                ${data.error_details.slice(0, 5).join('<br>')}
                ${data.error_details.length > 5 ? '<br>...' : ''}
              </div>
            ` : ''}
          </div>
        `;
      } else {
        resultDiv.innerHTML = `
          <div style="font-size: 48px; margin-bottom: 16px;">❌</div>
          <div style="font-size: 18px; color: #dc3545; margin-bottom: 16px; font-weight: 600;">导入失败</div>
          <div style="background: #f8d7da; padding: 16px; border-radius: 8px; color: #721c24;">
            ${response.msg || '未知错误'}
          </div>
        `;
      }
    },

    hideImportModal: function() {
      const modal = document.getElementById('cityImportModal');
      if (modal) {
        modal.remove();
      }
    },

    resetCities: function() {
      const confirmMsg = `🔄 重置城市数据\n\n⚠️ 警告：这将删除所有现有城市数据！\n\n操作内容：\n1. 清空所有现有城市（包括${document.querySelectorAll('.city-card, [style*="city-card"]').length || '当前所有'}个城市）\n2. 重新导入120个精选热门城市\n3. 包含经纬度坐标和热门标记\n\n此操作不可撤销，确定要继续吗？`;

      if (confirm(confirmMsg)) {
        // 显示重置进度模态框
        this.showResetModal();

        // 开始重置
        AdminCommon.api.get('reset_cities.php')
          .then(response => {
            this.updateResetProgress(response);
          })
          .catch(error => {
            this.updateResetProgress({
              success: false,
              msg: '重置失败: ' + error.message
            });
          });
      }
    },

    showResetModal: function() {
      const modalHtml = `
        <div class="modal" id="cityResetModal" style="display:flex;">
          <div class="modal-content" style="max-width: 600px; border-radius: 16px; overflow: hidden;">
            <div class="modal-header" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white; padding: 24px; text-align: center;">
              <h3 style="margin: 0; font-size: 20px; font-weight: 600;">🔄 重置城市数据</h3>
            </div>
            <div class="modal-body" style="padding: 32px; text-align: center;">
              <div id="reset-progress" style="margin-bottom: 20px;">
                <div style="font-size: 48px; margin-bottom: 16px;">⏳</div>
                <div style="font-size: 18px; color: #333; margin-bottom: 8px;">正在重置城市数据...</div>
                <div style="font-size: 14px; color: #666;">清空现有数据并导入热门城市</div>
              </div>
              <div id="reset-result" style="display: none;"></div>
              <div id="reset-actions" style="display: none; margin-top: 20px;">
                <button
                  class="btn btn-primary"
                  onclick="AdminModules.city.hideResetModal(); AdminModules.city.loadList();"
                  style="background: #007bff; border: none; padding: 10px 20px; border-radius: 8px; color: white;">
                  ✅ 完成
                </button>
              </div>
            </div>
          </div>
        </div>
      `;

      // 移除已存在的模态框
      const existingModal = document.getElementById('cityResetModal');
      if (existingModal) {
        existingModal.remove();
      }

      document.body.insertAdjacentHTML('beforeend', modalHtml);
    },

    updateResetProgress: function(response) {
      const progressDiv = document.getElementById('reset-progress');
      const resultDiv = document.getElementById('reset-result');
      const actionsDiv = document.getElementById('reset-actions');

      progressDiv.style.display = 'none';
      resultDiv.style.display = 'block';
      actionsDiv.style.display = 'block';

      if (response.success) {
        const data = response.data;
        resultDiv.innerHTML = `
          <div style="font-size: 48px; margin-bottom: 16px;">✅</div>
          <div style="font-size: 18px; color: #28a745; margin-bottom: 16px; font-weight: 600;">重置完成！</div>
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: left;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
              <div style="text-align: center; padding: 12px; background: white; border-radius: 6px;">
                <div style="font-size: 24px; font-weight: bold; color: #28a745;">${data.success}</div>
                <div style="font-size: 12px; color: #666;">导入成功</div>
              </div>
              <div style="text-align: center; padding: 12px; background: white; border-radius: 6px;">
                <div style="font-size: 24px; font-weight: bold; color: #ffc107;">${data.hot_count}</div>
                <div style="font-size: 12px; color: #666;">热门城市</div>
              </div>
            </div>
            <div style="font-size: 14px; color: #333;">
              <div><strong>总计:</strong> ${data.total} 个城市</div>
              <div><strong>成功:</strong> ${data.success} 个</div>
              <div><strong>热门:</strong> ${data.hot_count} 个（直辖市+省会城市）</div>
              <div><strong>错误:</strong> ${data.errors} 个</div>
            </div>
            ${data.errors > 0 ? `
              <div style="margin-top: 12px; padding: 8px; background: #fff3cd; border-radius: 4px; font-size: 12px;">
                <strong>错误详情:</strong><br>
                ${data.error_details.slice(0, 5).join('<br>')}
                ${data.error_details.length > 5 ? '<br>...' : ''}
              </div>
            ` : ''}
          </div>
        `;
      } else {
        resultDiv.innerHTML = `
          <div style="font-size: 48px; margin-bottom: 16px;">❌</div>
          <div style="font-size: 18px; color: #dc3545; margin-bottom: 16px; font-weight: 600;">重置失败</div>
          <div style="background: #f8d7da; padding: 16px; border-radius: 8px; color: #721c24;">
            ${response.msg || '未知错误'}
          </div>
        `;
      }
    },

    hideResetModal: function() {
      const modal = document.getElementById('cityResetModal');
      if (modal) {
        modal.remove();
      }
    },

    toggleHot: function(id, isHot) {
      const actionText = isHot ? '设为热门城市' : '取消热门城市';

      if (confirm(`确定要${actionText}吗？`)) {
        // 使用原生fetch方法，避免依赖AdminCommon.api.postRawJSON
        fetch('city_set_hot.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            id: id,
            is_hot: isHot
          })
        })
          .then(response => response.json())
          .then(response => {
            if (response.success) {
              alert(response.msg);
              this.loadList(); // 重新加载列表
            } else {
              alert('操作失败: ' + response.msg);
            }
          })
          .catch(error => {
            alert('网络错误，操作失败');
          });
      }
    }
  },

  // 轮播图管理模块
  banner: {
    loadList: function() {
      const container = document.getElementById('banner-list-container');
      container.innerHTML = `
        <div id="banner-list" style="display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:18px;">
          <div style="text-align:center;color:#bbb;grid-column:1/-1;">正在加载...</div>
        </div>
      `;

      AdminCommon.api.get('banner_list.php')
        .then(res => {
          if(res.success && Array.isArray(res.data)){
            this.renderList(res.data);
          } else {
            AdminCommon.ui.showEmpty('banner-list', '暂无轮播图');
          }
        })
        .catch(() => {
          AdminCommon.ui.showError('banner-list', '加载轮播图失败');
        });
    },

    renderList: function(banners) {
      const container = document.getElementById('banner-list');
      if(banners.length === 0) {
        container.innerHTML = '<div style="text-align:center;color:#bbb;grid-column:1/-1;">暂无轮播图</div>';
        return;
      }

      container.innerHTML = banners.map(banner => {
        // 处理图片路径
        let imgSrc = banner.img;
        if (imgSrc.startsWith('uploads/')) {
          imgSrc = '../' + imgSrc;
        } else if (imgSrc.startsWith('/w7/')) {
          imgSrc = '..' + imgSrc.substring(3);
        }

        return `
          <div style="border:1px solid #e0e0e0;border-radius:8px;overflow:hidden;background:#fff;">
            <img src="${imgSrc}" style="width:100%;height:160px;object-fit:cover;" onerror="this.onerror=null;this.src='../images/lbt.png';" />
            <div style="padding:12px;">
              <div style="font-size:14px;color:#666;margin-bottom:8px;">ID: ${banner.id}</div>
              <div style="font-size:14px;color:#666;margin-bottom:8px;word-break:break-all;">链接: ${banner.link || '无'}</div>
              <div style="display:flex;gap:8px;margin-top:12px;">
                <button class="btn btn-primary" onclick="AdminModules.banner.showEditModal(${banner.id})" style="flex:1;">编辑</button>
                <button class="btn btn-danger" onclick="AdminModules.banner.delete(${banner.id})" style="flex:1;">删除</button>
              </div>
            </div>
          </div>
        `;
      }).join('');
    },

    showAddModal: function() {
      const modalHtml = `
        <div class="modal" id="bannerAddModal" style="display:flex;">
          <div class="modal-content">
            <div class="modal-header">
              <h3 style="margin:0;">新增轮播图</h3>
              <button type="button" class="modal-close" onclick="AdminModules.banner.hideAddModal()">&times;</button>
            </div>
            <div class="modal-body">
              <form id="banner-add-form">
                <div class="form-group">
                  <label class="form-label">选择图片</label>
                  <input type="file" class="form-control" name="banner" accept="image/*" required>
                </div>
                <div class="form-group">
                  <label class="form-label">跳转链接（可选）</label>
                  <input type="url" class="form-control" name="link" placeholder="https://example.com">
                </div>
                <button type="submit" class="btn btn-primary">上传</button>
                <div id="banner-add-msg" style="color:#ff6a6a;font-size:15px;display:none;margin-top:8px;"></div>
              </form>
            </div>
          </div>
        </div>
      `;

      // 移除已存在的模态框
      const existingModal = document.getElementById('bannerAddModal');
      if (existingModal) {
        existingModal.remove();
      }

      document.body.insertAdjacentHTML('beforeend', modalHtml);

      // 绑定表单提交事件
      document.getElementById('banner-add-form').addEventListener('submit', (e) => {
        e.preventDefault();
        this.add(new FormData(e.target));
      });
    },

    hideAddModal: function() {
      const modal = document.getElementById('bannerAddModal');
      if (modal) {
        modal.remove();
      }
    },

    showEditModal: function(id) {
      // 先获取轮播图详情
      AdminCommon.api.get(`banner_detail.php?id=${id}`)
        .then(res => {
          if (!res.success) {
            AdminCommon.ui.alert(res.msg || '获取轮播图信息失败');
            return;
          }

          const banner = res.data;
          this.renderEditModal(banner);
        })
        .catch(() => {
          AdminCommon.ui.alert('获取轮播图信息失败');
        });
    },

    renderEditModal: function(banner) {
      // 处理图片路径用于预览
      let imgSrc = banner.img;
      if (imgSrc.startsWith('uploads/')) {
        imgSrc = '../' + imgSrc;
      } else if (imgSrc.startsWith('/w7/')) {
        imgSrc = '..' + imgSrc.substring(3);
      }

      const modalHtml = `
        <div class="modal" id="bannerEditModal" style="display:flex;">
          <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
              <h3 style="margin:0;">编辑轮播图</h3>
              <button type="button" class="modal-close" onclick="AdminModules.banner.hideEditModal()">&times;</button>
            </div>
            <div class="modal-body">
              <form id="banner-edit-form">
                <input type="hidden" name="id" value="${banner.id}">

                <div class="form-group">
                  <label class="form-label">当前图片</label>
                  <div style="margin-bottom: 10px;">
                    <img src="${imgSrc}" style="max-width: 100%; height: 200px; object-fit: cover; border-radius: 8px; border: 1px solid #ddd;"
                         onerror="this.onerror=null;this.src='../images/lbt.png';">
                  </div>
                </div>

                <div class="form-group">
                  <label class="form-label">更换图片（可选）</label>
                  <input type="file" class="form-control" name="banner" accept="image/*">
                  <small style="color: #666; font-size: 12px;">支持 JPG、PNG、GIF 格式，最大 5MB</small>
                </div>

                <div class="form-group">
                  <label class="form-label">跳转链接</label>
                  <input type="url" class="form-control" name="link" value="${banner.link || ''}" placeholder="https://example.com">
                </div>

                <div style="display: flex; gap: 10px;">
                  <button type="submit" class="btn btn-primary" style="flex: 1;">保存修改</button>
                  <button type="button" class="btn btn-secondary" onclick="AdminModules.banner.hideEditModal()" style="flex: 1;">取消</button>
                </div>

                <div id="banner-edit-msg" style="color:#ff6a6a;font-size:15px;display:none;margin-top:8px;"></div>
              </form>
            </div>
          </div>
        </div>
      `;

      // 移除已存在的编辑模态框
      const existingModal = document.getElementById('bannerEditModal');
      if (existingModal) {
        existingModal.remove();
      }

      document.body.insertAdjacentHTML('beforeend', modalHtml);

      // 绑定表单提交事件
      document.getElementById('banner-edit-form').addEventListener('submit', (e) => {
        e.preventDefault();
        this.edit(new FormData(e.target));
      });
    },

    hideEditModal: function() {
      const modal = document.getElementById('bannerEditModal');
      if (modal) {
        modal.remove();
      }
    },

    edit: function(formData) {
      const msg = document.getElementById('banner-edit-msg');
      msg.style.display = 'none';

      AdminCommon.api.postForm('banner_edit.php', formData)
        .then(res => {
          msg.textContent = res.msg || (res.success ? '更新成功' : '更新失败');
          msg.style.color = res.success ? '#00c6a2' : '#ff6a6a';
          msg.style.display = 'block';

          if(res.success) {
            setTimeout(() => {
              this.hideEditModal();
              this.loadList();
            }, 800);
          }
        })
        .catch(() => {
          msg.textContent = '网络错误，更新失败';
          msg.style.color = '#ff6a6a';
          msg.style.display = 'block';
        });
    },

    add: function(formData) {
      const msg = document.getElementById('banner-add-msg');
      msg.style.display = 'none';

      AdminCommon.api.postForm('banner_upload.php', formData)
        .then(res => {
          msg.textContent = res.msg || (res.success ? '上传成功' : '上传失败');
          msg.style.color = res.success ? '#00c6a2' : '#ff6a6a';
          msg.style.display = 'block';

          if(res.success) {
            setTimeout(() => {
              this.hideAddModal();
              this.loadList();
            }, 800);
          }
        })
        .catch(() => {
          msg.textContent = '网络错误，上传失败';
          msg.style.color = '#ff6a6a';
          msg.style.display = 'block';
        });
    },

    delete: function(id) {
      AdminCommon.ui.confirm('确定要删除这个轮播图吗？', () => {
        AdminCommon.api.postJSON('banner_delete.php', { id: id })
          .then(res => {
            AdminCommon.ui.alert(res.msg || '操作完成');
            if(res.success) {
              this.loadList();
            }
          })
          .catch(() => {
            AdminCommon.ui.alert('删除失败');
          });
      });
    }
  },

  // 密码管理模块
  password: {
    loadForm: function() {
      const container = document.getElementById('password-form-container');
      container.innerHTML = `
        <form id="password-change-form" style="display:flex;flex-direction:column;gap:18px;">
          <label for="current-password" style="font-size:15px;color:#888;">
            当前密码
            <input type="password" id="current-password" name="current_password" required
                   style="width:100%;height:40px;border-radius:8px;border:1px solid #eee;padding:0 12px;font-size:16px;margin-top:6px;"
                   placeholder="请输入当前密码" />
          </label>
          <label for="new-password" style="font-size:15px;color:#888;">
            新密码
            <input type="password" id="new-password" name="new_password" required minlength="6"
                   style="width:100%;height:40px;border-radius:8px;border:1px solid #eee;padding:0 12px;font-size:16px;margin-top:6px;"
                   placeholder="请输入新密码（至少6位）" />
          </label>
          <label for="confirm-password" style="font-size:15px;color:#888;">
            确认新密码
            <input type="password" id="confirm-password" name="confirm_password" required minlength="6"
                   style="width:100%;height:40px;border-radius:8px;border:1px solid #eee;padding:0 12px;font-size:16px;margin-top:6px;"
                   placeholder="请再次输入新密码" />
          </label>
          <button type="submit" style="height:44px;background:#2196f3;color:#fff;font-size:17px;font-weight:bold;border:none;border-radius:10px;cursor:pointer;">
            修改密码
          </button>
          <div id="password-change-msg" style="color:#ff6a6a;font-size:15px;display:none;"></div>
        </form>
        <div style="font-size:13px;color:#888;margin-top:18px;line-height:1.5;">
          <strong>安全提示：</strong><br>
          • 密码长度至少6位<br>
          • 建议使用字母、数字组合<br>
          • 修改后请牢记新密码
        </div>
      `;

      // 绑定表单事件
      setTimeout(() => {
        const passwordForm = document.getElementById('password-change-form');
        const msg = document.getElementById('password-change-msg');

        if(passwordForm){
          passwordForm.onsubmit = function(e){
            e.preventDefault();

            const currentPassword = document.getElementById('current-password').value.trim();
            const newPassword = document.getElementById('new-password').value.trim();
            const confirmPassword = document.getElementById('confirm-password').value.trim();

            msg.style.display = 'none';

            // 前端验证
            if(!currentPassword){
              msg.textContent = '请输入当前密码';
              msg.style.color = '#ff6a6a';
              msg.style.display = 'block';
              return;
            }

            if(!newPassword){
              msg.textContent = '请输入新密码';
              msg.style.color = '#ff6a6a';
              msg.style.display = 'block';
              return;
            }

            if(newPassword.length < 6){
              msg.textContent = '新密码长度不能少于6位';
              msg.style.color = '#ff6a6a';
              msg.style.display = 'block';
              return;
            }

            if(newPassword !== confirmPassword){
              msg.textContent = '两次输入的新密码不一致';
              msg.style.color = '#ff6a6a';
              msg.style.display = 'block';
              return;
            }

            if(currentPassword === newPassword){
              msg.textContent = '新密码不能与当前密码相同';
              msg.style.color = '#ff6a6a';
              msg.style.display = 'block';
              return;
            }

            // 提交修改
            const submitBtn = passwordForm.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.textContent = '修改中...';

            AdminCommon.api.postJSON('admin_password.php', {
              current_password: currentPassword,
              new_password: newPassword,
              confirm_password: confirmPassword
            })
            .then(res => {
              submitBtn.disabled = false;
              submitBtn.textContent = '修改密码';

              msg.textContent = res.msg || (res.success ? '密码修改成功' : '修改失败');
              msg.style.display = 'block';

              if(res.success){
                msg.style.color = '#00c6a2';
                // 清空表单
                passwordForm.reset();
                // 提示用户重新登录
                setTimeout(() => {
                  if(confirm('密码修改成功！为了安全，请重新登录。')){
                    window.location.href = 'admin_logout.php';
                  }
                }, 1500);
              }else{
                msg.style.color = '#ff6a6a';
              }
            })
            .catch(() => {
              submitBtn.disabled = false;
              submitBtn.textContent = '修改密码';
              msg.textContent = '网络错误，修改失败';
              msg.style.color = '#ff6a6a';
              msg.style.display = 'block';
            });
          };
        }
      }, 10);
    }
  },

  // 高德key管理模块
  amap: {
    loadForm: function() {
      const container = document.getElementById('amap-form-container');
      container.innerHTML = `
        <form id="amap-key-form" style="display:flex;flex-direction:column;gap:18px;">
          <label for="amap-key-input" style="font-size:15px;color:#888;">请输入高德地图API Key：</label>
          <input type="text" id="amap-key-input" name="amap_key" style="height:40px;border-radius:8px;border:1px solid #eee;padding:0 12px;font-size:16px;" placeholder="请输入Key" />
          <button type="submit" style="height:44px;background:#2196f3;color:#fff;font-size:17px;font-weight:bold;border:none;border-radius:10px;cursor:pointer;">保存</button>
          <div id="amap-key-msg" style="color:#ff6a6a;font-size:15px;display:none;"></div>
        </form>
        <div style="font-size:13px;color:#888;margin-top:18px;">
          高德地图Key用于地图选址、定位等功能。请前往
          <a href="https://console.amap.com/dev/key/app" target="_blank" style="color:#2196f3;">高德开放平台</a>
          申请。
        </div>
      `;

      // 绑定事件
      setTimeout(() => {
        // 加载当前key
        AdminCommon.api.get('amap_key.php')
          .then(res => {
            if(res.success && res.amap_key){
              document.getElementById('amap-key-input').value = res.amap_key;
            }
          })
          .catch(() => {
            console.log('加载高德key失败');
          });

        // 绑定表单提交
        document.getElementById('amap-key-form').onsubmit = function(e){
          e.preventDefault();
          const key = document.getElementById('amap-key-input').value.trim();
          const msg = document.getElementById('amap-key-msg');
          msg.style.display = 'none';

          AdminCommon.api.postJSON('amap_key.php', { amap_key: key })
            .then(res => {
              msg.textContent = res.msg || (res.success ? '保存成功' : '保存失败');
              msg.style.display = 'block';
              msg.style.color = res.success ? '#00c6a2' : '#ff6a6a';
            })
            .catch(() => {
              msg.textContent = '保存失败';
              msg.style.display = 'block';
              msg.style.color = '#ff6a6a';
            });
        };
      }, 10);
    }
  },

  // 项目管理模块
  project: {
    loadForm: function() {
      const container = document.getElementById('project-form-container');
      container.innerHTML = `
        <form id="project-article-form" style="max-width:480px;margin:0 auto;background:#fff;border-radius:16px;box-shadow:0 2px 8px rgba(0,0,0,0.06);padding:32px 28px;display:flex;flex-direction:column;gap:22px;">
          <div style="display:flex;align-items:center;gap:8px;">
            <span style="color:#ff6a6a;font-size:18px;">*</span>
            <label for="project-title" style="font-size:15px;color:#444;">文章标题</label>
            <input id="project-title" type="text" maxlength="20" placeholder="请输入文章标题" style="flex:1;height:38px;border-radius:8px;border:1px solid #e0e0e0;padding:0 12px;font-size:15px;margin-left:8px;" />
            <span id="project-title-count" style="font-size:13px;color:#bbb;margin-left:8px;">0/20</span>
          </div>
          <div style="display:flex;flex-direction:column;gap:6px;">
            <label for="project-content" style="font-size:15px;color:#444;">内容</label>
            <textarea id="project-content" rows="7" maxlength="1000" placeholder="请输入内容" style="border-radius:8px;border:1px solid #e0e0e0;padding:10px 12px;font-size:15px;resize:vertical;"></textarea>
            <span id="project-content-count" style="font-size:13px;color:#bbb;align-self:flex-end;">0/1000</span>
          </div>
          <button type="submit" style="height:44px;background:#2196f3;color:#fff;font-size:17px;font-weight:bold;border:none;border-radius:10px;cursor:pointer;">保存</button>
          <div id="project-article-msg" style="color:#ff6a6a;font-size:15px;display:none;"></div>
        </form>
      `;

      // 绑定事件
      setTimeout(() => {
        const titleInput = document.getElementById('project-title');
        const titleCount = document.getElementById('project-title-count');
        const contentInput = document.getElementById('project-content');
        const contentCount = document.getElementById('project-content-count');
        const msg = document.getElementById('project-article-msg');

        titleInput.oninput = function(){
          titleCount.textContent = titleInput.value.length + '/20';
        };

        contentInput.oninput = function(){
          contentCount.textContent = contentInput.value.length + '/1000';
        };

        // 读取已保存内容
        AdminCommon.api.get('project_article.php')
          .then(res => {
            if(res.success && res.data){
              titleInput.value = res.data.title || '';
              contentInput.value = res.data.content || '';
              titleCount.textContent = titleInput.value.length + '/20';
              contentCount.textContent = contentInput.value.length + '/1000';
            }
          })
          .catch(() => {
            console.log('加载项目文章内容失败');
          });

        document.getElementById('project-article-form').onsubmit = function(e){
          e.preventDefault();
          msg.style.display = 'none';

          if(!titleInput.value.trim()){
            msg.textContent = '请输入文章标题';
            msg.style.display = 'block';
            return;
          }

          if(!contentInput.value.trim()){
            msg.textContent = '请输入内容';
            msg.style.display = 'block';
            return;
          }

          AdminCommon.api.postJSON('project_article.php', {
            title: titleInput.value,
            content: contentInput.value
          })
          .then(res => {
            msg.textContent = res.msg || (res.success ? '保存成功' : '保存失败');
            msg.style.display = 'block';
            msg.style.color = res.success ? '#00c6a2' : '#ff6a6a';
          })
          .catch(() => {
            msg.textContent = '保存失败';
            msg.style.display = 'block';
            msg.style.color = '#ff6a6a';
          });
        };
      }, 10);
    }
  },

  // 路费管理模块
  taxi: {
    loadForm: function() {
      const container = document.getElementById('taxi-form-container');
      container.innerHTML = `
        <form id="taxi-article-form" style="max-width:480px;margin:0 auto;background:#fff;border-radius:16px;box-shadow:0 2px 8px rgba(0,0,0,0.06);padding:32px 28px;display:flex;flex-direction:column;gap:22px;">
          <div style="display:flex;align-items:center;gap:8px;">
            <span style="color:#ff6a6a;font-size:18px;">*</span>
            <label for="taxi-title" style="font-size:15px;color:#444;">文章标题</label>
            <input id="taxi-title" type="text" maxlength="20" placeholder="请输入文章标题" style="flex:1;height:38px;border-radius:8px;border:1px solid #e0e0e0;padding:0 12px;font-size:15px;margin-left:8px;" />
            <span id="taxi-title-count" style="font-size:13px;color:#bbb;margin-left:8px;">0/20</span>
          </div>
          <div style="display:flex;flex-direction:column;gap:6px;">
            <label for="taxi-content" style="font-size:15px;color:#444;">内容</label>
            <textarea id="taxi-content" rows="7" maxlength="1000" placeholder="请输入内容" style="border-radius:8px;border:1px solid #e0e0e0;padding:10px 12px;font-size:15px;resize:vertical;"></textarea>
            <span id="taxi-content-count" style="font-size:13px;color:#bbb;align-self:flex-end;">0/1000</span>
          </div>
          <button type="submit" style="height:44px;background:#2196f3;color:#fff;font-size:17px;font-weight:bold;border:none;border-radius:10px;cursor:pointer;">保存</button>
          <div id="taxi-article-msg" style="color:#ff6a6a;font-size:15px;display:none;"></div>
        </form>
      `;

      // 绑定事件
      setTimeout(() => {
        const titleInput = document.getElementById('taxi-title');
        const titleCount = document.getElementById('taxi-title-count');
        const contentInput = document.getElementById('taxi-content');
        const contentCount = document.getElementById('taxi-content-count');
        const msg = document.getElementById('taxi-article-msg');

        titleInput.oninput = function(){
          titleCount.textContent = titleInput.value.length + '/20';
        };

        contentInput.oninput = function(){
          contentCount.textContent = contentInput.value.length + '/1000';
        };

        // 读取已保存内容
        AdminCommon.api.get('taxi_article.php')
          .then(res => {
            if(res.success && res.data){
              titleInput.value = res.data.title || '';
              contentInput.value = res.data.content || '';
              titleCount.textContent = titleInput.value.length + '/20';
              contentCount.textContent = contentInput.value.length + '/1000';
            }
          })
          .catch(() => {
            console.log('加载路费文章内容失败');
          });

        document.getElementById('taxi-article-form').onsubmit = function(e){
          e.preventDefault();
          msg.style.display = 'none';

          if(!titleInput.value.trim()){
            msg.textContent = '请输入文章标题';
            msg.style.display = 'block';
            return;
          }

          if(!contentInput.value.trim()){
            msg.textContent = '请输入内容';
            msg.style.display = 'block';
            return;
          }

          AdminCommon.api.postJSON('taxi_article.php', {
            title: titleInput.value,
            content: contentInput.value
          })
          .then(res => {
            msg.textContent = res.msg || (res.success ? '保存成功' : '保存失败');
            msg.style.display = 'block';
            msg.style.color = res.success ? '#00c6a2' : '#ff6a6a';
          })
          .catch(() => {
            msg.textContent = '保存失败';
            msg.style.display = 'block';
            msg.style.color = '#ff6a6a';
          });
        };
      }, 10);
    }
  },

  // 推荐管理模块
  recommend: {
    currentTab: 'system',
    recommendEnabled: false,
    systemRecommendEnabled: false,
    manualRecommendEnabled: false,

    init: function() {
      // 加载推荐功能状态
      this.loadRecommendStatus();
      this.loadSystemRecommendStatus();
      this.loadManualRecommendStatus();
    },

    // 加载推荐功能状态
    loadRecommendStatus: function() {
      // 这里可以从配置文件或数据库加载推荐功能开关状态
      // 暂时使用localStorage模拟
      const status = localStorage.getItem('recommend_enabled');
      this.recommendEnabled = status === 'true';

      const toggle = document.getElementById('recommend-toggle');
      const statusText = document.getElementById('recommend-status-text');

      if (toggle) {
        toggle.checked = this.recommendEnabled;
      }

      if (statusText) {
        statusText.textContent = `推荐功能：${this.recommendEnabled ? '已开启' : '已关闭'}`;
        statusText.style.color = this.recommendEnabled ? '#4caf50' : '#f44336';
      }
    },

    // 加载系统推荐状态
    loadSystemRecommendStatus: function() {
      const status = localStorage.getItem('system_recommend_enabled');
      this.systemRecommendEnabled = status === 'true';

      const toggle = document.getElementById('system-recommend-toggle');
      const statusText = document.getElementById('system-recommend-status-text');

      if (toggle) {
        toggle.checked = this.systemRecommendEnabled;
      }

      if (statusText) {
        statusText.textContent = `系统推荐：${this.systemRecommendEnabled ? '已开启' : '已关闭'}`;
        statusText.style.color = this.systemRecommendEnabled ? '#4caf50' : '#f44336';
      }
    },

    // 加载人工推荐状态
    loadManualRecommendStatus: function() {
      const status = localStorage.getItem('manual_recommend_enabled');
      this.manualRecommendEnabled = status === 'true';

      const toggle = document.getElementById('manual-recommend-toggle');
      const statusText = document.getElementById('manual-recommend-status-text');

      if (toggle) {
        toggle.checked = this.manualRecommendEnabled;
      }

      if (statusText) {
        statusText.textContent = `人工推荐：${this.manualRecommendEnabled ? '已开启' : '已关闭'}`;
        statusText.style.color = this.manualRecommendEnabled ? '#4caf50' : '#f44336';
      }
    },

    // 切换推荐功能开关
    toggleRecommendFunction: function() {
      const toggle = document.getElementById('recommend-toggle');
      const statusText = document.getElementById('recommend-status-text');

      this.recommendEnabled = toggle.checked;
      localStorage.setItem('recommend_enabled', this.recommendEnabled.toString());

      statusText.textContent = `推荐功能：${this.recommendEnabled ? '已开启' : '已关闭'}`;
      statusText.style.color = this.recommendEnabled ? '#4caf50' : '#f44336';

      AdminCommon.ui.alert(
        this.recommendEnabled ? '推荐功能已开启' : '推荐功能已关闭'
      );
    },

    // 切换系统推荐开关
    toggleSystemRecommend: function() {
      const toggle = document.getElementById('system-recommend-toggle');
      const statusText = document.getElementById('system-recommend-status-text');

      this.systemRecommendEnabled = toggle.checked;
      localStorage.setItem('system_recommend_enabled', this.systemRecommendEnabled.toString());

      statusText.textContent = `系统推荐：${this.systemRecommendEnabled ? '已开启' : '已关闭'}`;
      statusText.style.color = this.systemRecommendEnabled ? '#4caf50' : '#f44336';

      AdminCommon.ui.alert(
        this.systemRecommendEnabled ? '系统推荐已开启，将按规则自动推荐技师' : '系统推荐已关闭，不再自动推荐'
      );
    },

    // 切换人工推荐开关
    toggleManualRecommend: function() {
      const toggle = document.getElementById('manual-recommend-toggle');
      const statusText = document.getElementById('manual-recommend-status-text');

      this.manualRecommendEnabled = toggle.checked;
      localStorage.setItem('manual_recommend_enabled', this.manualRecommendEnabled.toString());

      statusText.textContent = `人工推荐：${this.manualRecommendEnabled ? '已开启' : '已关闭'}`;
      statusText.style.color = this.manualRecommendEnabled ? '#4caf50' : '#f44336';

      AdminCommon.ui.alert(
        this.manualRecommendEnabled ? '人工推荐已开启，手动推荐的技师将生效' : '人工推荐已关闭，手动推荐将不生效'
      );
    },

    // 切换选项卡
    switchTab: function(tab) {
      this.currentTab = tab;

      // 更新选项卡按钮状态
      document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      document.getElementById(`tab-${tab}`).classList.add('active');

      // 更新内容显示
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
      });
      document.getElementById(`${tab}-recommend`).classList.add('active');
    },

    // 刷新系统推荐
    refreshSystemRecommend: function() {
      if (!this.systemRecommendEnabled) {
        AdminCommon.ui.alert('请先开启系统推荐功能');
        return;
      }

      AdminCommon.ui.alert('系统推荐已刷新，将按最新规则重新排序技师列表');

      // 这里可以调用API刷新推荐算法
      console.log('刷新系统推荐');
    },

    // 显示已推荐技师（人工推荐）
    showRecommendedTechs: function() {
      const container = document.getElementById('manual-recommend-content');
      AdminCommon.ui.showLoading(container, '加载已推荐技师...');

      // 获取已推荐的技师（只显示人工推荐的）
      AdminCommon.api.get('tech_list.php')
        .then(data => {
          if (data.success && Array.isArray(data.data)) {
            const recommendedTechs = data.data.filter(tech =>
              tech.is_recommended == 1 && tech.status == 1
            );
            this.renderRecommendedTechsList(recommendedTechs);
          } else {
            AdminCommon.ui.showError(container, '加载推荐技师失败');
          }
        })
        .catch(error => {
          AdminCommon.ui.showError(container, '网络错误，请稍后再试');
        });
    },

    // 渲染已推荐技师列表
    renderRecommendedTechsList: function(techs) {
      const container = document.getElementById('manual-recommend-content');

      if (techs.length === 0) {
        container.innerHTML = `
          <div class="text-center text-muted">
            <div style="font-size: 48px; margin-bottom: 16px;">⭐</div>
            <div>暂无推荐技师</div>
          </div>
        `;
        return;
      }

      const techsHTML = techs.map(tech => `
        <div class="recommended-tech-item">
          <div class="tech-info">
            <img src="${AdminCommon.format.imagePath(tech.workimg)}" alt="${tech.nick}" class="tech-avatar-small">
            <div class="tech-details">
              <div class="tech-name">${tech.nick || '技师' + tech.id}</div>
              <div class="tech-meta">
                <span>城市: ${tech.city || '未知'}</span>
                <span>单量: ${tech.virtual_orders || 0}</span>
                <span>状态: ${tech.is_resting == 1 ? '休息中' : '工作中'}</span>
              </div>
            </div>
          </div>
          <div class="tech-actions">
            <button class="btn btn-danger btn-sm" onclick="AdminModules.recommend.unrecommendTech(${tech.id})">取消推荐</button>
          </div>
        </div>
      `).join('');

      container.innerHTML = `
        <div class="recommended-techs-list">
          <div style="margin-bottom: 16px; color: #666;">
            共 ${techs.length} 个推荐技师
          </div>
          ${techsHTML}
        </div>
      `;
    },

    // 显示可推荐技师
    showAvailableTechs: function() {
      const container = document.getElementById('manual-recommend-content');
      AdminCommon.ui.showLoading(container, '加载可推荐技师...');

      // 获取未推荐的技师
      AdminCommon.api.get('tech_list.php')
        .then(data => {
          if (data.success && Array.isArray(data.data)) {
            const availableTechs = data.data.filter(tech =>
              tech.status == 1 && tech.is_recommended != 1
            );
            this.renderAvailableTechsList(availableTechs);
          } else {
            AdminCommon.ui.showError(container, '加载技师列表失败');
          }
        })
        .catch(error => {
          AdminCommon.ui.showError(container, '网络错误，请稍后再试');
        });
    },

    // 渲染可推荐技师列表
    renderAvailableTechsList: function(techs) {
      const container = document.getElementById('manual-recommend-content');

      if (techs.length === 0) {
        container.innerHTML = `
          <div class="text-center text-muted">
            <div style="font-size: 48px; margin-bottom: 16px;">✅</div>
            <div>所有技师都已推荐</div>
          </div>
        `;
        return;
      }

      const techsHTML = techs.map(tech => `
        <div class="available-tech-item">
          <div class="tech-info">
            <img src="${AdminCommon.format.imagePath(tech.workimg)}" alt="${tech.nick}" class="tech-avatar-small">
            <div class="tech-details">
              <div class="tech-name">${tech.nick || '技师' + tech.id}</div>
              <div class="tech-meta">
                <span>城市: ${tech.city || '未知'}</span>
                <span>单量: ${tech.virtual_orders || 0}</span>
                <span>状态: ${tech.is_resting == 1 ? '休息中' : '工作中'}</span>
              </div>
            </div>
          </div>
          <div class="tech-actions">
            <button class="btn btn-success btn-sm" onclick="AdminModules.recommend.recommendTech(${tech.id})">设为推荐</button>
          </div>
        </div>
      `).join('');

      container.innerHTML = `
        <div class="available-techs-list">
          <div style="margin-bottom: 16px; color: #666;">
            共 ${techs.length} 个可推荐技师
          </div>
          ${techsHTML}
        </div>
      `;
    },

    // 推荐技师
    recommendTech: function(techId) {
      AdminCommon.ui.confirm('确定要将该技师设为推荐吗？', () => {
        AdminCommon.api.postJSON('tech_recommend.php', { id: techId, action: 'recommend' })
          .then(response => {
            AdminCommon.ui.alert(response.msg || '操作完成');
            if (response.success) {
              // 刷新当前列表
              this.showAvailableTechs();
            }
          })
          .catch(error => {
            AdminCommon.ui.alert('操作失败');
          });
      });
    },

    // 取消推荐技师
    unrecommendTech: function(techId) {
      AdminCommon.ui.confirm('确定要取消该技师的推荐状态吗？', () => {
        AdminCommon.api.postJSON('tech_recommend.php', { id: techId, action: 'unrecommend' })
          .then(response => {
            AdminCommon.ui.alert(response.msg || '操作完成');
            if (response.success) {
              // 刷新当前列表
              this.showRecommendedTechs();
            }
          })
          .catch(error => {
            AdminCommon.ui.alert('操作失败');
          });
      });
    },

    // 批量管理推荐
    batchManageRecommend: function() {
      AdminCommon.ui.alert('批量管理功能开发中...');
    }
  },

  // 公告管理模块
  announcement: {
    currentTab: 'home',
    homeAnnouncementEnabled: false,
    homeScrollEnabled: false,
    homeScrollSpeed: 15,
    techAnnouncementEnabled: false,
    techScrollEnabled: false,
    techScrollSpeed: 15,

    init: function() {
      // 加载公告设置
      this.loadAnnouncementSettings();
    },

    // 加载公告设置
    loadAnnouncementSettings: function() {
      // 首页公告设置
      this.homeAnnouncementEnabled = localStorage.getItem('home_announcement_enabled') === 'true';
      this.homeScrollEnabled = localStorage.getItem('home_scroll_enabled') === 'true';
      this.homeScrollSpeed = parseInt(localStorage.getItem('home_scroll_speed')) || 15;

      // 技师页公告设置
      this.techAnnouncementEnabled = localStorage.getItem('tech_announcement_enabled') === 'true';
      this.techScrollEnabled = localStorage.getItem('tech_scroll_enabled') === 'true';
      this.techScrollSpeed = parseInt(localStorage.getItem('tech_scroll_speed')) || 15;

      // 更新界面状态
      this.updateHomeAnnouncementStatus();
      this.updateTechAnnouncementStatus();

      // 加载公告内容
      this.loadAnnouncementContent();
    },

    // 更新首页公告状态
    updateHomeAnnouncementStatus: function() {
      const toggle = document.getElementById('home-announcement-toggle');
      const status = document.getElementById('home-announcement-status');
      const scrollToggle = document.getElementById('home-scroll-toggle');
      const scrollStatus = document.getElementById('home-scroll-status');
      const speedControl = document.getElementById('home-speed-control');
      const speedSlider = document.getElementById('home-scroll-speed');
      const speedValue = document.getElementById('home-speed-value');

      if (toggle) {
        toggle.checked = this.homeAnnouncementEnabled;
      }
      if (status) {
        status.textContent = `公告显示：${this.homeAnnouncementEnabled ? '已开启' : '已关闭'}`;
        status.style.color = this.homeAnnouncementEnabled ? '#4caf50' : '#f44336';
      }
      if (scrollToggle) {
        scrollToggle.checked = this.homeScrollEnabled;
      }
      if (scrollStatus) {
        scrollStatus.textContent = `滚动显示：${this.homeScrollEnabled ? '已开启' : '已关闭'}`;
        scrollStatus.style.color = this.homeScrollEnabled ? '#4caf50' : '#f44336';
      }
      if (speedControl) {
        speedControl.style.display = this.homeScrollEnabled ? 'flex' : 'none';
      }
      if (speedSlider) {
        speedSlider.value = this.homeScrollSpeed;
      }
      if (speedValue) {
        speedValue.textContent = this.homeScrollSpeed + '秒';
      }
    },

    // 更新技师页公告状态
    updateTechAnnouncementStatus: function() {
      const toggle = document.getElementById('tech-announcement-toggle');
      const status = document.getElementById('tech-announcement-status');
      const scrollToggle = document.getElementById('tech-scroll-toggle');
      const scrollStatus = document.getElementById('tech-scroll-status');
      const speedControl = document.getElementById('tech-speed-control');
      const speedSlider = document.getElementById('tech-scroll-speed');
      const speedValue = document.getElementById('tech-speed-value');

      if (toggle) {
        toggle.checked = this.techAnnouncementEnabled;
      }
      if (status) {
        status.textContent = `公告显示：${this.techAnnouncementEnabled ? '已开启' : '已关闭'}`;
        status.style.color = this.techAnnouncementEnabled ? '#4caf50' : '#f44336';
      }
      if (scrollToggle) {
        scrollToggle.checked = this.techScrollEnabled;
      }
      if (scrollStatus) {
        scrollStatus.textContent = `滚动显示：${this.techScrollEnabled ? '已开启' : '已关闭'}`;
        scrollStatus.style.color = this.techScrollEnabled ? '#4caf50' : '#f44336';
      }
      if (speedControl) {
        speedControl.style.display = this.techScrollEnabled ? 'flex' : 'none';
      }
      if (speedSlider) {
        speedSlider.value = this.techScrollSpeed;
      }
      if (speedValue) {
        speedValue.textContent = this.techScrollSpeed + '秒';
      }
    },

    // 加载公告内容
    loadAnnouncementContent: function() {
      const homeText = localStorage.getItem('home_announcement_text') || '';
      const techText = localStorage.getItem('tech_announcement_text') || '';

      const homeTextarea = document.getElementById('home-announcement-text');
      const techTextarea = document.getElementById('tech-announcement-text');

      if (homeTextarea) {
        homeTextarea.value = homeText;
      }
      if (techTextarea) {
        techTextarea.value = techText;
      }
    },

    // 切换选项卡
    switchTab: function(tab) {
      this.currentTab = tab;

      // 更新选项卡按钮状态
      document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      document.getElementById(`tab-${tab}`).classList.add('active');

      // 更新内容显示
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
      });
      document.getElementById(`${tab}-announcement`).classList.add('active');
    },

    // 切换首页公告开关
    toggleHomeAnnouncement: function() {
      const toggle = document.getElementById('home-announcement-toggle');
      this.homeAnnouncementEnabled = toggle.checked;
      localStorage.setItem('home_announcement_enabled', this.homeAnnouncementEnabled.toString());
      this.updateHomeAnnouncementStatus();

      AdminCommon.ui.alert(
        this.homeAnnouncementEnabled ? '首页公告已开启' : '首页公告已关闭'
      );
    },

    // 切换首页滚动开关
    toggleHomeScroll: function() {
      const toggle = document.getElementById('home-scroll-toggle');
      this.homeScrollEnabled = toggle.checked;
      localStorage.setItem('home_scroll_enabled', this.homeScrollEnabled.toString());
      this.updateHomeAnnouncementStatus();

      AdminCommon.ui.alert(
        this.homeScrollEnabled ? '首页公告滚动已开启' : '首页公告滚动已关闭'
      );
    },

    // 切换技师页公告开关
    toggleTechAnnouncement: function() {
      const toggle = document.getElementById('tech-announcement-toggle');
      this.techAnnouncementEnabled = toggle.checked;
      localStorage.setItem('tech_announcement_enabled', this.techAnnouncementEnabled.toString());
      this.updateTechAnnouncementStatus();

      AdminCommon.ui.alert(
        this.techAnnouncementEnabled ? '技师页公告已开启' : '技师页公告已关闭'
      );
    },

    // 切换技师页滚动开关
    toggleTechScroll: function() {
      const toggle = document.getElementById('tech-scroll-toggle');
      this.techScrollEnabled = toggle.checked;
      localStorage.setItem('tech_scroll_enabled', this.techScrollEnabled.toString());
      this.updateTechAnnouncementStatus();

      AdminCommon.ui.alert(
        this.techScrollEnabled ? '技师页公告滚动已开启' : '技师页公告滚动已关闭'
      );
    },

    // 更新首页滚动速度
    updateHomeScrollSpeed: function() {
      const speedSlider = document.getElementById('home-scroll-speed');
      const speedValue = document.getElementById('home-speed-value');

      this.homeScrollSpeed = parseInt(speedSlider.value);
      localStorage.setItem('home_scroll_speed', this.homeScrollSpeed.toString());

      if (speedValue) {
        speedValue.textContent = this.homeScrollSpeed + '秒';
      }
    },

    // 更新技师页滚动速度
    updateTechScrollSpeed: function() {
      const speedSlider = document.getElementById('tech-scroll-speed');
      const speedValue = document.getElementById('tech-speed-value');

      this.techScrollSpeed = parseInt(speedSlider.value);
      localStorage.setItem('tech_scroll_speed', this.techScrollSpeed.toString());

      if (speedValue) {
        speedValue.textContent = this.techScrollSpeed + '秒';
      }
    },

    // 保存首页公告
    saveHomeAnnouncement: function() {
      const textarea = document.getElementById('home-announcement-text');
      const content = textarea.value.trim();

      if (!content) {
        AdminCommon.ui.alert('请输入公告内容');
        return;
      }

      localStorage.setItem('home_announcement_text', content);
      AdminCommon.ui.alert('首页公告保存成功');
    },

    // 保存技师页公告
    saveTechAnnouncement: function() {
      const textarea = document.getElementById('tech-announcement-text');
      const content = textarea.value.trim();

      if (!content) {
        AdminCommon.ui.alert('请输入公告内容');
        return;
      }

      localStorage.setItem('tech_announcement_text', content);
      AdminCommon.ui.alert('技师页公告保存成功');
    },

    // 预览首页公告效果
    previewHomeAnnouncement: function() {
      const textarea = document.getElementById('home-announcement-text');
      const content = textarea.value.trim();

      if (!content) {
        AdminCommon.ui.alert('请先输入公告内容');
        return;
      }

      // 创建预览模态框
      this.showAnnouncementPreview('首页公告预览', content, this.homeScrollEnabled, this.homeScrollSpeed);
    },

    // 预览技师页公告效果
    previewTechAnnouncement: function() {
      const textarea = document.getElementById('tech-announcement-text');
      const content = textarea.value.trim();

      if (!content) {
        AdminCommon.ui.alert('请先输入公告内容');
        return;
      }

      // 创建预览模态框
      this.showAnnouncementPreview('技师页公告预览', content, this.techScrollEnabled, this.techScrollSpeed);
    },

    // 显示公告预览
    showAnnouncementPreview: function(title, content, isScrolling, scrollSpeed) {
      const existingModal = document.getElementById('announcement-preview-modal');
      if (existingModal) {
        existingModal.remove();
      }

      scrollSpeed = scrollSpeed || 15;
      const scrollClass = isScrolling ? 'scrolling' : '';
      const modalHTML = `
        <div id="announcement-preview-modal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.8); display: flex; justify-content: center; align-items: center; z-index: 3000; padding: 20px;">
          <div style="background: white; border-radius: 16px; max-width: 500px; width: 100%; max-height: 80vh; overflow-y: auto;">
            <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px 24px; border-bottom: 1px solid #eee;">
              <h3 style="margin: 0; color: #333;">${title}</h3>
              <button onclick="this.closest('#announcement-preview-modal').remove()" style="background: none; border: none; font-size: 24px; color: #999; cursor: pointer;">&times;</button>
            </div>
            <div style="padding: 24px;">
              <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 12px 16px; margin-bottom: 16px; overflow: hidden;">
                <div class="announcement-content ${scrollClass}" style="color: #856404; font-size: 14px; line-height: 1.5;">
                  ${content}
                </div>
              </div>
              <div style="color: #666; font-size: 12px;">
                ${isScrolling ? `✅ 滚动效果已开启 (${scrollSpeed}秒/轮)` : '❌ 滚动效果已关闭'}
              </div>
            </div>
          </div>
        </div>
        <style>
          .announcement-content.scrolling {
            animation: scroll-text ${scrollSpeed}s linear infinite;
            white-space: nowrap;
          }
          @keyframes scroll-text {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
          }
        </style>
      `;

      document.body.insertAdjacentHTML('beforeend', modalHTML);
    }
  }
};
