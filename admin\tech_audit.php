<?php
// 技师审核接口
header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';
$id = isset($_POST['id']) ? intval($_POST['id']) : 0;
$action = isset($_POST['action']) ? $_POST['action'] : '';
$reason = isset($_POST['reason']) ? trim($_POST['reason']) : '';
if (!$id || !in_array($action, ['approve','reject'])) {
    echo json_encode(['success'=>false,'msg'=>'参数错误']);
    exit;
}
if ($action=='approve') {
    $stmt = $pdo->prepare("UPDATE technician SET status='approved' WHERE id=?");
    $ok = $stmt->execute([$id]);
    echo json_encode(['success'=>$ok,'msg'=>$ok?'审核通过':'操作失败']);
    exit;
}
if ($action=='reject') {
    $stmt = $pdo->prepare("UPDATE technician SET status='rejected', reject_reason=? WHERE id=?");
    $ok = $stmt->execute([$reason, $id]);
    echo json_encode(['success'=>$ok,'msg'=>$ok?'已驳回':'操作失败']);
    exit;
}
echo json_encode(['success'=>false,'msg'=>'未知操作']);
