<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .result.success {
            border-left: 4px solid #28a745;
        }
        .result.error {
            border-left: 4px solid #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .btn-test {
            background: #28a745;
        }
        .btn-test:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ 热门城市功能最终修复验证</h1>
        
        <div class="success">
            <h4>🔧 修复说明</h4>
            <p>已将热门城市功能改为使用原生fetch方法，不再依赖AdminCommon.api.postRawJSON，避免了缓存和加载顺序问题。</p>
        </div>
        
        <div class="test-section">
            <h3>📋 获取城市列表</h3>
            <button onclick="loadCities()">加载城市列表</button>
            <div id="cities-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>⭐ 测试热门城市功能</h3>
            <p style="color: #666; font-size: 14px;">
                使用原生fetch方法，直接发送JSON数据到API
            </p>
            <button class="btn-test" onclick="testSetHot()">测试设为热门</button>
            <button class="btn-test" onclick="testCancelHot()">测试取消热门</button>
            <div id="hot-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🎯 模拟后台操作</h3>
            <p style="color: #666; font-size: 14px;">
                模拟后台管理界面中的热门城市切换操作
            </p>
            <button onclick="simulateAdminToggle()">模拟后台切换操作</button>
            <div id="admin-result" class="result"></div>
        </div>
    </div>

    <script>
        let testCities = [];
        
        function loadCities() {
            const resultDiv = document.getElementById('cities-result');
            resultDiv.textContent = '正在加载城市列表...';
            resultDiv.className = 'result';
            
            fetch('admin/city_list.php')
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data)) {
                        testCities = data;
                        const hotCities = data.filter(city => city.is_hot == 1);
                        
                        resultDiv.textContent = `✅ 城市列表加载成功

总城市数: ${data.length}
热门城市: ${hotCities.length} 个

前5个城市:
${data.slice(0, 5).map(city => `${city.id}. ${city.name} ${city.is_hot == 1 ? '⭐' : ''}`).join('\n')}`;
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.textContent = '❌ 加载失败: ' + JSON.stringify(data, null, 2);
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '❌ 网络错误: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function testSetHot() {
            if (testCities.length === 0) {
                alert('请先加载城市列表');
                return;
            }
            
            const normalCity = testCities.find(city => city.is_hot != 1);
            if (!normalCity) {
                alert('没有普通城市可以设为热门');
                return;
            }
            
            testHotAPI(normalCity.id, 1, '设为热门');
        }
        
        function testCancelHot() {
            if (testCities.length === 0) {
                alert('请先加载城市列表');
                return;
            }
            
            const hotCity = testCities.find(city => city.is_hot == 1);
            if (!hotCity) {
                alert('没有热门城市可以取消');
                return;
            }
            
            testHotAPI(hotCity.id, 0, '取消热门');
        }
        
        function testHotAPI(cityId, isHot, action) {
            const resultDiv = document.getElementById('hot-result');
            const city = testCities.find(c => c.id == cityId);
            
            resultDiv.textContent = `正在测试${action}...
城市: ${city.name} (ID: ${cityId})`;
            resultDiv.className = 'result';
            
            // 使用与后台管理相同的方法
            fetch('admin/city_set_hot.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id: cityId,
                    is_hot: isHot
                })
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.textContent = `${action}测试结果:

城市: ${city.name} (ID: ${cityId})
操作: ${action}

API响应:
${JSON.stringify(data, null, 2)}

${data.success ? '✅ 操作成功！' : '❌ 操作失败！'}`;
                resultDiv.className = data.success ? 'result success' : 'result error';
                
                if (data.success) {
                    // 更新本地数据
                    const cityIndex = testCities.findIndex(c => c.id == cityId);
                    if (cityIndex !== -1) {
                        testCities[cityIndex].is_hot = isHot;
                    }
                }
            })
            .catch(error => {
                resultDiv.textContent = `${action}测试失败:

网络错误: ${error.message}

请检查：
1. 是否已登录后台管理系统
2. API文件是否存在`;
                resultDiv.className = 'result error';
            });
        }
        
        function simulateAdminToggle() {
            const resultDiv = document.getElementById('admin-result');
            
            if (testCities.length === 0) {
                resultDiv.textContent = '请先加载城市列表';
                resultDiv.className = 'result error';
                return;
            }
            
            const testCity = testCities[0];
            const newHotStatus = testCity.is_hot == 1 ? 0 : 1;
            const actionText = newHotStatus ? '设为热门城市' : '取消热门城市';
            
            resultDiv.textContent = `模拟后台管理操作...

城市: ${testCity.name}
当前状态: ${testCity.is_hot == 1 ? '热门' : '普通'}
操作: ${actionText}`;
            resultDiv.className = 'result';
            
            // 模拟后台管理中的toggleHot方法
            const toggleHot = function(id, isHot) {
                return fetch('admin/city_set_hot.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: id,
                        is_hot: isHot
                    })
                })
                .then(response => response.json());
            };
            
            toggleHot(testCity.id, newHotStatus)
                .then(response => {
                    resultDiv.textContent += `

模拟结果:
${JSON.stringify(response, null, 2)}

${response.success ? '✅ 后台操作模拟成功！' : '❌ 后台操作模拟失败！'}

这证明后台管理界面的热门城市功能现在应该可以正常工作了。`;
                    resultDiv.className = response.success ? 'result success' : 'result error';
                })
                .catch(error => {
                    resultDiv.textContent += `

模拟失败: ${error.message}`;
                    resultDiv.className = 'result error';
                });
        }
        
        // 页面加载时自动加载城市列表
        window.onload = function() {
            loadCities();
        };
    </script>
</body>
</html>
