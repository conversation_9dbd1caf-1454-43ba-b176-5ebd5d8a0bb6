<?php
// debug_tech_images.php - 调试技师图片路径

session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo "未登录";
    exit;
}

header('Content-Type: text/html; charset=utf-8');

try {
    require_once '../db.php';
    
    if (!$pdo) {
        echo "数据库连接失败";
        exit;
    }

    // 获取所有技师的图片信息
    $stmt = $pdo->prepare("SELECT id, nick, workimg, lifeimg, video FROM technician ORDER BY id DESC LIMIT 10");
    $stmt->execute();
    $techs = $stmt->fetchAll();
    
    echo "<h2>技师图片路径调试信息</h2>";
    echo "<style>
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .path { font-family: monospace; background: #f8f9fa; padding: 2px 4px; }
        .error { color: red; }
        .success { color: green; }
    </style>";
    
    echo "<table>";
    echo "<tr><th>ID</th><th>昵称</th><th>工作照</th><th>生活照</th><th>视频</th><th>文件检查</th></tr>";
    
    foreach ($techs as $tech) {
        echo "<tr>";
        echo "<td>{$tech['id']}</td>";
        echo "<td>" . ($tech['nick'] ?: '未设置') . "</td>";
        
        // 工作照
        echo "<td>";
        if ($tech['workimg']) {
            echo "<div class='path'>{$tech['workimg']}</div>";
            $workimgPath = '../' . $tech['workimg'];
            if (file_exists($workimgPath)) {
                echo "<span class='success'>✓ 文件存在</span>";
            } else {
                echo "<span class='error'>✗ 文件不存在</span>";
                echo "<br><small>检查路径: $workimgPath</small>";
            }
        } else {
            echo "无";
        }
        echo "</td>";
        
        // 生活照
        echo "<td>";
        if ($tech['lifeimg']) {
            $lifeimgs = explode(',', $tech['lifeimg']);
            foreach ($lifeimgs as $index => $img) {
                $img = trim($img);
                if ($img) {
                    echo "<div class='path'>$img</div>";
                    $imgPath = '../' . $img;
                    if (file_exists($imgPath)) {
                        echo "<span class='success'>✓ 存在</span>";
                    } else {
                        echo "<span class='error'>✗ 不存在</span>";
                        echo "<br><small>检查路径: $imgPath</small>";
                    }
                    echo "<br>";
                }
            }
        } else {
            echo "无";
        }
        echo "</td>";
        
        // 视频
        echo "<td>";
        if ($tech['video']) {
            echo "<div class='path'>{$tech['video']}</div>";
            $videoPath = '../' . $tech['video'];
            if (file_exists($videoPath)) {
                echo "<span class='success'>✓ 文件存在</span>";
            } else {
                echo "<span class='error'>✗ 文件不存在</span>";
                echo "<br><small>检查路径: $videoPath</small>";
            }
        } else {
            echo "无";
        }
        echo "</td>";
        
        // 文件检查总结
        echo "<td>";
        $totalFiles = 0;
        $existingFiles = 0;
        
        if ($tech['workimg']) {
            $totalFiles++;
            if (file_exists('../' . $tech['workimg'])) $existingFiles++;
        }
        
        if ($tech['lifeimg']) {
            $lifeimgs = explode(',', $tech['lifeimg']);
            foreach ($lifeimgs as $img) {
                $img = trim($img);
                if ($img) {
                    $totalFiles++;
                    if (file_exists('../' . $img)) $existingFiles++;
                }
            }
        }
        
        if ($tech['video']) {
            $totalFiles++;
            if (file_exists('../' . $tech['video'])) $existingFiles++;
        }
        
        if ($totalFiles == 0) {
            echo "无文件";
        } else {
            echo "$existingFiles/$totalFiles 存在";
            if ($existingFiles == $totalFiles) {
                echo " <span class='success'>✓</span>";
            } else {
                echo " <span class='error'>✗</span>";
            }
        }
        echo "</td>";
        
        echo "</tr>";
    }
    
    echo "</table>";
    
    // 检查 uploads 目录
    echo "<h3>uploads 目录检查</h3>";
    $uploadsDir = '../uploads/';
    if (is_dir($uploadsDir)) {
        echo "<p class='success'>✓ uploads 目录存在</p>";
        
        $files = scandir($uploadsDir);
        $imageFiles = array_filter($files, function($file) {
            return preg_match('/\.(jpg|jpeg|png|gif|mp4)$/i', $file);
        });
        
        echo "<p>目录中的文件数量: " . count($imageFiles) . "</p>";
        
        if (count($imageFiles) > 0) {
            echo "<h4>最近的文件:</h4>";
            echo "<ul>";
            $recentFiles = array_slice($imageFiles, -10);
            foreach ($recentFiles as $file) {
                $filePath = $uploadsDir . $file;
                $fileSize = filesize($filePath);
                $fileTime = date('Y-m-d H:i:s', filemtime($filePath));
                echo "<li><code>$file</code> - " . number_format($fileSize) . " bytes - $fileTime</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p class='error'>✗ uploads 目录不存在</p>";
    }

} catch (Exception $e) {
    echo "<p class='error'>错误: " . $e->getMessage() . "</p>";
}
?>

<br><br>
<a href="javascript:history.back()">返回</a>
