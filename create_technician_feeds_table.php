<?php
// create_technician_feeds_table.php - 创建技师动态表
require_once 'db.php';

try {
    // 创建技师动态表
    $sql = "CREATE TABLE IF NOT EXISTS technician_feeds (
        id INT AUTO_INCREMENT PRIMARY KEY,
        technician_id INT NOT NULL,
        technician_name VARCHAR(100) NOT NULL,
        age INT DEFAULT NULL,
        height INT DEFAULT NULL,
        weight INT DEFAULT NULL,
        service_years INT DEFAULT NULL,
        city VARCHAR(100) NOT NULL,
        previous_city VARCHAR(100) DEFAULT NULL,
        action_type ENUM('join', 'move') NOT NULL DEFAULT 'join',
        content TEXT,
        avatar VARCHAR(255) DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        is_active TINYINT(1) DEFAULT 1,
        INDEX idx_technician_id (technician_id),
        INDEX idx_city (city),
        INDEX idx_created_at (created_at),
        INDEX idx_action_type (action_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "技师动态表创建成功！\n";
    
    // 创建技师动态点赞表
    $sql2 = "CREATE TABLE IF NOT EXISTS technician_feed_likes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        feed_id INT NOT NULL,
        user_id VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_like (feed_id, user_id),
        FOREIGN KEY (feed_id) REFERENCES technician_feeds(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql2);
    echo "技师动态点赞表创建成功！\n";
    
    // 创建技师动态评论表
    $sql3 = "CREATE TABLE IF NOT EXISTS technician_feed_comments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        feed_id INT NOT NULL,
        user_id VARCHAR(50) NOT NULL,
        content TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active TINYINT(1) DEFAULT 1,
        FOREIGN KEY (feed_id) REFERENCES technician_feeds(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql3);
    echo "技师动态评论表创建成功！\n";
    
    // 插入一些示例数据
    $sampleFeeds = [
        [
            'technician_id' => 1,
            'technician_name' => '李大宝',
            'age' => 22,
            'height' => 175,
            'weight' => 60,
            'service_years' => 16,
            'city' => '三亚市',
            'action_type' => 'join',
            'content' => '技师李大宝 22/175/60/16 到三亚了',
            'avatar' => 'images/tx.jpg'
        ],
        [
            'technician_id' => 2,
            'technician_name' => '王小明',
            'age' => 25,
            'height' => 180,
            'weight' => 70,
            'service_years' => 20,
            'city' => '海口市',
            'action_type' => 'join',
            'content' => '技师王小明 25/180/70/20 到海口了',
            'avatar' => 'images/tx.jpg'
        ],
        [
            'technician_id' => 1,
            'technician_name' => '李大宝',
            'age' => 22,
            'height' => 175,
            'weight' => 60,
            'service_years' => 16,
            'city' => '海口市',
            'previous_city' => '三亚市',
            'action_type' => 'move',
            'content' => '技师李大宝 22/175/60/16 到海口了',
            'avatar' => 'images/tx.jpg'
        ],
        [
            'technician_id' => 3,
            'technician_name' => '张伟',
            'age' => 28,
            'height' => 178,
            'weight' => 65,
            'service_years' => 25,
            'city' => '儋州市',
            'action_type' => 'join',
            'content' => '技师张伟 28/178/65/25 到儋州了',
            'avatar' => 'images/tx.jpg'
        ]
    ];
    
    $insertStmt = $pdo->prepare("
        INSERT INTO technician_feeds 
        (technician_id, technician_name, age, height, weight, service_years, city, previous_city, action_type, content, avatar) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($sampleFeeds as $feed) {
        $insertStmt->execute([
            $feed['technician_id'],
            $feed['technician_name'],
            $feed['age'],
            $feed['height'],
            $feed['weight'],
            $feed['service_years'],
            $feed['city'],
            $feed['previous_city'] ?? null,
            $feed['action_type'],
            $feed['content'],
            $feed['avatar']
        ]);
    }
    
    echo "示例技师动态数据插入成功！\n";
    echo "数据库初始化完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
