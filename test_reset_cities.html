<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>城市数据重置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
            color: #856404;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .reset-btn {
            background: #ffc107;
            color: #212529;
        }
        .reset-btn:hover {
            background: #e0a800;
        }
        .danger-btn {
            background: #dc3545;
        }
        .danger-btn:hover {
            background: #c82333;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 城市数据重置测试</h1>
        
        <div class="warning">
            <h4>⚠️ 重要提醒</h4>
            <p>重置功能将<strong>删除所有现有城市数据</strong>，然后导入120个精选热门城市。此操作不可撤销！</p>
            <p>请确保您有管理员权限，并且已经备份了重要数据。</p>
        </div>
        
        <div class="test-section">
            <h3>📊 当前城市统计</h3>
            <button onclick="loadCurrentStats()">查看当前城市统计</button>
            <div id="current-stats" class="stats-grid"></div>
            <div id="current-cities-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🔄 重置操作</h3>
            <p style="color: #666; font-size: 14px;">
                重置操作将：<br>
                1. 删除所有现有城市（当前717个）<br>
                2. 导入120个精选热门城市<br>
                3. 包含经纬度坐标和热门标记<br>
                4. 重置自增ID从1开始
            </p>
            <button class="reset-btn" onclick="confirmReset()">🔄 执行重置操作</button>
            <div id="reset-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>✅ 重置后验证</h3>
            <button onclick="verifyReset()">验证重置结果</button>
            <div id="verify-stats" class="stats-grid"></div>
            <div id="verify-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🗑️ 紧急清空（仅测试用）</h3>
            <p style="color: #dc3545; font-size: 14px;">
                <strong>危险操作：</strong>仅清空所有城市数据，不导入新数据。仅用于测试！
            </p>
            <button class="danger-btn" onclick="emergencyClear()">🗑️ 清空所有城市</button>
            <div id="clear-result" class="result"></div>
        </div>
    </div>

    <script>
        function loadCurrentStats() {
            const statsDiv = document.getElementById('current-stats');
            const resultDiv = document.getElementById('current-cities-result');
            
            statsDiv.innerHTML = '<div style="text-align: center; color: #999;">正在加载...</div>';
            resultDiv.textContent = '正在获取城市统计...';
            resultDiv.className = 'result';
            
            fetch('admin/city_list.php')
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data)) {
                        const total = data.length;
                        const hotCities = data.filter(city => city.is_hot == 1);
                        const normalCities = data.filter(city => city.is_hot != 1);
                        
                        // 统计各类城市
                        const directMunicipalities = data.filter(city => 
                            ['北京市', '上海市', '天津市', '重庆市'].includes(city.name)
                        );
                        
                        statsDiv.innerHTML = `
                            <div class="stat-card">
                                <div class="stat-number">${total}</div>
                                <div class="stat-label">总城市数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${hotCities.length}</div>
                                <div class="stat-label">热门城市</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${normalCities.length}</div>
                                <div class="stat-label">普通城市</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${directMunicipalities.length}</div>
                                <div class="stat-label">直辖市</div>
                            </div>
                        `;
                        
                        resultDiv.textContent = `当前城市详情:
总数: ${total} 个
热门城市: ${hotCities.length} 个
普通城市: ${normalCities.length} 个

前20个城市:
${data.slice(0, 20).map(city => `${city.id}. ${city.name} ${city.is_hot == 1 ? '(热门)' : ''}`).join('\n')}

${total > 20 ? '...' : ''}`;
                        resultDiv.className = 'result success';
                    } else {
                        statsDiv.innerHTML = '<div style="text-align: center; color: #f56c6c;">加载失败</div>';
                        resultDiv.textContent = '获取城市数据失败: ' + JSON.stringify(data, null, 2);
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    statsDiv.innerHTML = '<div style="text-align: center; color: #f56c6c;">网络错误</div>';
                    resultDiv.textContent = '网络错误: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function confirmReset() {
            const confirmMsg = `🔄 确认重置城市数据\n\n⚠️ 这将删除所有现有城市数据！\n\n操作内容：\n1. 清空所有现有城市\n2. 重新导入120个精选热门城市\n3. 包含经纬度坐标和热门标记\n\n此操作不可撤销，确定要继续吗？`;
            
            if (confirm(confirmMsg)) {
                executeReset();
            }
        }
        
        function executeReset() {
            const resultDiv = document.getElementById('reset-result');
            resultDiv.textContent = '正在执行重置操作，请稍候...';
            resultDiv.className = 'result';
            
            fetch('admin/reset_cities.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const resetData = data.data;
                        resultDiv.textContent = `重置操作完成！

操作统计:
- 总计: ${resetData.total} 个城市
- 成功导入: ${resetData.success} 个
- 热门城市: ${resetData.hot_count} 个
- 错误: ${resetData.errors} 个

${resetData.errors > 0 ? `错误详情:\n${resetData.error_details.join('\n')}` : ''}

✅ 城市数据已重置为120个精选热门城市！`;
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.textContent = '重置操作失败: ' + (data.msg || '未知错误');
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '网络错误: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function verifyReset() {
            const statsDiv = document.getElementById('verify-stats');
            const resultDiv = document.getElementById('verify-result');
            
            statsDiv.innerHTML = '<div style="text-align: center; color: #999;">正在验证...</div>';
            resultDiv.textContent = '正在验证重置结果...';
            resultDiv.className = 'result';
            
            fetch('admin/city_list.php')
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data)) {
                        const total = data.length;
                        const hotCities = data.filter(city => city.is_hot == 1);
                        const normalCities = data.filter(city => city.is_hot != 1);
                        
                        // 检查关键城市
                        const keyHotCities = ['北京市', '上海市', '广州市', '深圳市', '杭州市'];
                        const foundKeyCities = keyHotCities.filter(cityName => 
                            data.some(city => city.name === cityName)
                        );
                        
                        statsDiv.innerHTML = `
                            <div class="stat-card">
                                <div class="stat-number">${total}</div>
                                <div class="stat-label">总城市数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${hotCities.length}</div>
                                <div class="stat-label">热门城市</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${normalCities.length}</div>
                                <div class="stat-label">普通城市</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${foundKeyCities.length}/5</div>
                                <div class="stat-label">关键城市</div>
                            </div>
                        `;
                        
                        const isSuccess = total === 120 && hotCities.length >= 30 && foundKeyCities.length === 5;
                        
                        resultDiv.textContent = `验证结果:

总城市数量: ${total} (期望: 120)
热门城市数量: ${hotCities.length} (期望: 35+)
普通城市数量: ${normalCities.length} (期望: 85+)

关键城市检查:
${keyHotCities.map(city => `${foundKeyCities.includes(city) ? '✅' : '❌'} ${city}`).join('\n')}

热门城市样本:
${hotCities.slice(0, 10).map(city => `- ${city.name}`).join('\n')}

${isSuccess ? '✅ 重置成功！城市数据符合预期' : '⚠️ 重置可能不完整，请检查数据'}`;
                        resultDiv.className = isSuccess ? 'result success' : 'result error';
                    } else {
                        statsDiv.innerHTML = '<div style="text-align: center; color: #f56c6c;">验证失败</div>';
                        resultDiv.textContent = '验证失败: ' + JSON.stringify(data, null, 2);
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    statsDiv.innerHTML = '<div style="text-align: center; color: #f56c6c;">网络错误</div>';
                    resultDiv.textContent = '验证错误: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function emergencyClear() {
            const confirmMsg = `🗑️ 紧急清空所有城市\n\n⚠️ 危险操作！\n\n这将删除所有城市数据，不导入新数据。\n数据库将变为空！\n\n确定要继续吗？`;
            
            if (confirm(confirmMsg)) {
                const resultDiv = document.getElementById('clear-result');
                resultDiv.textContent = '正在清空所有城市数据...';
                resultDiv.className = 'result';
                
                // 这里可以添加清空API调用
                resultDiv.textContent = '清空功能未实现（安全考虑）\n如需清空，请直接在数据库中执行：DELETE FROM city';
                resultDiv.className = 'result error';
            }
        }
        
        // 页面加载时自动加载当前统计
        window.onload = function() {
            loadCurrentStats();
        };
    </script>
</body>
</html>
