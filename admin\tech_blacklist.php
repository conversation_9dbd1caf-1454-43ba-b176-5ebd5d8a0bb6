<?php
// tech_blacklist.php - 技师拉黑/解除拉黑
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

try {
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $action = isset($_POST['action']) ? trim($_POST['action']) : '';
    
    if ($id <= 0) {
        echo json_encode(['success' => false, 'msg' => '无效的技师ID']);
        exit;
    }

    if (!in_array($action, ['blacklist', 'unblacklist'])) {
        echo json_encode(['success' => false, 'msg' => '无效的操作类型']);
        exit;
    }

    // 查询技师信息
    $stmt = $pdo->prepare("SELECT id, nick, phone, status, is_black FROM technician WHERE id = ?");
    $stmt->execute([$id]);
    $tech = $stmt->fetch();
    
    if (!$tech) {
        echo json_encode(['success' => false, 'msg' => '技师不存在']);
        exit;
    }

    // 只允许对已通过的技师进行拉黑操作
    if ($tech['status'] !== 'approved' && $tech['status'] != 1) {
        echo json_encode(['success' => false, 'msg' => '只能对已通过审核的技师进行拉黑操作']);
        exit;
    }

    if ($action === 'blacklist') {
        // 拉黑操作
        if ($tech['is_black'] == 1) {
            echo json_encode(['success' => false, 'msg' => '该技师已经被拉黑']);
            exit;
        }

        $stmt = $pdo->prepare("UPDATE technician SET is_black = 1 WHERE id = ?");
        $result = $stmt->execute([$id]);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'msg' => "技师「{$tech['nick']}」已被拉黑"
            ]);
        } else {
            echo json_encode(['success' => false, 'msg' => '拉黑操作失败']);
        }

    } else if ($action === 'unblacklist') {
        // 解除拉黑操作
        if ($tech['is_black'] == 0) {
            echo json_encode(['success' => false, 'msg' => '该技师未被拉黑']);
            exit;
        }

        $stmt = $pdo->prepare("UPDATE technician SET is_black = 0 WHERE id = ?");
        $result = $stmt->execute([$id]);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'msg' => "技师「{$tech['nick']}」已解除拉黑"
            ]);
        } else {
            echo json_encode(['success' => false, 'msg' => '解除拉黑操作失败']);
        }
    }

} catch (Exception $e) {
    error_log("Tech blacklist error: " . $e->getMessage());
    echo json_encode(['success' => false, 'msg' => '操作失败: ' . $e->getMessage()]);
}
?>
