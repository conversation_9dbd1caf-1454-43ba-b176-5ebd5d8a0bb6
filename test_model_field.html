<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试"你的型号"字段功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .form-preview {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group select, .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .highlight {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        .highlight strong {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 测试"你的型号"字段功能</h1>
        
        <div class="section">
            <h3>📋 功能说明</h3>
            <p>在profile.html页面的技师申请表单中，在尺寸字段后面新增了"你的型号"选择字段。</p>
            
            <div class="highlight">
                <strong>新增字段：</strong><br>
                • 字段名称：你的型号<br>
                • 字段类型：下拉选择<br>
                • 选项：1、0、0.5、不10<br>
                • 位置：尺寸字段后面<br>
                • 必填：是
            </div>
        </div>
        
        <div class="section">
            <h3>🎯 测试步骤</h3>
            <div style="margin: 15px 0;">
                <button class="btn" onclick="openProfilePage()">📱 1. 打开个人中心</button>
                <button class="btn" onclick="openAdminPanel()">👨‍💼 2. 打开后台管理</button>
                <button class="btn success" onclick="testDatabaseField()">🗄️ 3. 测试数据库字段</button>
            </div>
            
            <div class="info">
                <strong>测试流程：</strong><br>
                1. 打开个人中心页面，点击"申请技师"<br>
                2. 在表单中查看"你的型号"字段是否正确显示<br>
                3. 填写完整表单并提交，验证数据是否正确保存<br>
                4. 在后台管理中查看技师信息，确认"你的型号"字段显示正常
            </div>
            
            <div id="test-result"></div>
        </div>
        
        <div class="section">
            <h3>📝 表单预览</h3>
            <p>以下是新增"你的型号"字段后的表单预览：</p>
            
            <div class="form-preview">
                <h4 style="text-align: center; margin-bottom: 20px;">技师申请表单</h4>
                
                <div class="form-group">
                    <label>平台昵称</label>
                    <input type="text" placeholder="请输入平台昵称" value="示例技师">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>年纪</label>
                        <input type="number" placeholder="年纪" value="25">
                    </div>
                    <div class="form-group">
                        <label>身高(cm)</label>
                        <input type="number" placeholder="身高cm" value="175">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>体重(kg)</label>
                        <input type="number" placeholder="体重kg" value="65">
                    </div>
                    <div class="form-group">
                        <label>尺寸</label>
                        <input type="text" placeholder="尺寸" value="C">
                    </div>
                </div>
                
                <div class="form-group highlight">
                    <label><strong>你的型号 (新增字段)</strong></label>
                    <select>
                        <option value="">你的型号 请选择</option>
                        <option value="1">1</option>
                        <option value="0" selected>0</option>
                        <option value="0.5">0.5</option>
                        <option value="不10">不10</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>所在城市</label>
                    <select>
                        <option value="">请选择所在城市</option>
                        <option value="三亚市" selected>三亚市</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>住址</label>
                    <input type="text" placeholder="详细住址" value="示例酒店">
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>🗄️ 数据库验证</h3>
            <div style="margin: 15px 0;">
                <button class="btn" onclick="checkDatabaseStructure()">🔍 检查表结构</button>
                <button class="btn" onclick="testModelField()">🧪 测试字段功能</button>
            </div>
            <div id="database-result">
                <div class="info">点击按钮开始数据库验证</div>
            </div>
        </div>
    </div>

    <script>
        // 打开个人中心页面
        function openProfilePage() {
            window.open('profile.html', '_blank');
            
            document.getElementById('test-result').innerHTML = `
                <div class="info">
                    📱 已打开个人中心页面<br>
                    请按照以下步骤测试：<br>
                    1. 点击"申请技师"按钮<br>
                    2. 查看表单中是否有"你的型号"字段<br>
                    3. 验证字段选项：1、0、0.5、不10<br>
                    4. 填写完整表单并提交测试
                </div>
            `;
        }
        
        // 打开后台管理
        function openAdminPanel() {
            window.open('admin/admin_login.html', '_blank');
            
            document.getElementById('test-result').innerHTML = `
                <div class="info">
                    👨‍💼 已打开后台管理页面<br>
                    请按照以下步骤验证：<br>
                    1. 登录后台管理系统<br>
                    2. 进入"技师管理"页面<br>
                    3. 查看技师详情中是否显示"你的型号"<br>
                    4. 编辑技师信息，验证"你的型号"字段功能
                </div>
            `;
        }
        
        // 测试数据库字段
        async function testDatabaseField() {
            try {
                const response = await fetch('add_model_field.php');
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('test-result').innerHTML = `
                        <div class="success">
                            ✅ 数据库字段验证成功<br>
                            • 操作结果：${result.message}<br>
                            • 执行动作：${result.action === 'added' ? '新增字段' : '字段已存在'}<br>
                            • 字段状态：正常
                        </div>
                    `;
                } else {
                    document.getElementById('test-result').innerHTML = `
                        <div class="error">❌ 数据库字段验证失败：${result.error}</div>
                    `;
                }
            } catch (error) {
                document.getElementById('test-result').innerHTML = `
                    <div class="error">❌ 请求失败：${error.message}</div>
                `;
            }
        }
        
        // 检查数据库表结构
        async function checkDatabaseStructure() {
            const resultContainer = document.getElementById('database-result');
            resultContainer.innerHTML = '<div class="info">正在检查数据库表结构...</div>';
            
            try {
                const response = await fetch('add_model_field.php');
                const result = await response.json();
                
                resultContainer.innerHTML = `
                    <div class="success">
                        ✅ 数据库表结构检查完成<br>
                        • model字段状态：${result.action === 'skip' ? '已存在' : '已添加'}<br>
                        • 字段类型：VARCHAR(32)<br>
                        • 字段位置：size字段后面<br>
                        • 默认值：空字符串
                    </div>
                `;
            } catch (error) {
                resultContainer.innerHTML = `
                    <div class="error">❌ 检查失败：${error.message}</div>
                `;
            }
        }
        
        // 测试字段功能
        function testModelField() {
            const resultContainer = document.getElementById('database-result');
            
            resultContainer.innerHTML = `
                <div class="info">
                    🧪 字段功能测试说明<br><br>
                    <strong>前端测试：</strong><br>
                    • profile.html - 技师申请表单包含"你的型号"字段<br>
                    • 字段类型：下拉选择框<br>
                    • 选项：1、0、0.5、不10<br>
                    • 验证：必填字段<br><br>
                    
                    <strong>后端测试：</strong><br>
                    • technician_apply.php - 处理model字段提交<br>
                    • admin/tech_edit.php - 支持model字段编辑<br>
                    • 数据库：technician表包含model字段<br><br>
                    
                    <strong>管理后台：</strong><br>
                    • 技师详情显示"你的型号"<br>
                    • 技师编辑表单包含"你的型号"选择<br>
                    • 数据保存和更新正常
                </div>
            `;
        }
        
        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testDatabaseField, 1000);
        });
    </script>
</body>
</html>
