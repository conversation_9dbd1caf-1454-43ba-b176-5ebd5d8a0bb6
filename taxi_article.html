<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>路费说明</title>
  <style>
    body { background:#f7f8fa; font-family:'<PERSON><PERSON>','PingFang SC','Microsoft YaHei',sans-serif; margin:0; }
    .article-wrap { max-width:600px; margin:32px auto 0 auto; background:#fff; border-radius:16px; box-shadow:0 2px 8px rgba(0,0,0,0.06); padding:32px 24px; }
    .article-title { font-size:22px; font-weight:bold; color:#222; margin-bottom:18px; text-align:center; }
    .article-content { font-size:16px; color:#444; line-height:1.8; white-space:pre-wrap; }
    .back-btn { display:inline-block; margin-top:28px; background:#2196f3; color:#fff; border:none; border-radius:8px; padding:8px 28px; font-size:16px; cursor:pointer; text-align:center; }
    @media (max-width:700px) {
      .article-wrap { max-width:98vw; padding:18px 6vw; }
      .article-title { font-size:18px; }
      .article-content { font-size:15px; }
    }
  </style>
</head>
<body>
  <div class="article-wrap">
    <div class="article-title" id="taxi-title" style="font-size:26px;font-weight:bold;color:#00c6a2;letter-spacing:1px;text-align:center;">路费说明</div>
    <div class="article-content" id="taxi-content" style="font-size:18px;color:#222;line-height:2.1;font-weight:500;white-space:pre-line;">加载中...</div>
    <button class="back-btn" onclick="window.history.back()">返回</button>
  </div>
  <script>
    fetch('admin/taxi_article.php')
      .then(r=>r.json())
      .then(function(res){
        if(res.success && res.data){
          document.getElementById('taxi-title').textContent = res.data.title || '路费说明';
          document.getElementById('taxi-content').textContent = res.data.content || '暂无内容';
        }else{
          document.getElementById('taxi-content').textContent = '暂无内容';
        }
      })
      .catch(function(){
        document.getElementById('taxi-content').textContent = '加载失败，请稍后重试';
      });
  </script>
</body>
</html>
