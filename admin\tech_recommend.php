<?php
// tech_recommend.php - 技师推荐状态切换
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

try {
    // 支持两种数据格式：JSON和表单数据
    $id = 0;
    $action = '';

    // 尝试从POST表单数据获取
    if (isset($_POST['id']) && isset($_POST['action'])) {
        $id = intval($_POST['id']);
        $action = trim($_POST['action']);
    } else {
        // 尝试从JSON数据获取
        $input = json_decode(file_get_contents('php://input'), true);
        if ($input) {
            $id = isset($input['id']) ? intval($input['id']) : 0;
            $action = isset($input['action']) ? trim($input['action']) : '';
        }
    }
    
    if ($id <= 0) {
        echo json_encode(['success' => false, 'msg' => '无效的技师ID']);
        exit;
    }

    if (!in_array($action, ['recommend', 'unrecommend'])) {
        echo json_encode(['success' => false, 'msg' => '无效的操作类型']);
        exit;
    }

    // 查询技师信息
    $stmt = $pdo->prepare("SELECT id, nick, status, is_recommended FROM technician WHERE id = ?");
    $stmt->execute([$id]);
    $technician = $stmt->fetch();
    
    if (!$technician) {
        echo json_encode(['success' => false, 'msg' => '技师不存在']);
        exit;
    }

    // 检查技师状态，只有已通过的技师才能设置推荐
    if ($technician['status'] !== 'approved' && $technician['status'] != 1) {
        echo json_encode(['success' => false, 'msg' => '只有已通过审核的技师才能设置推荐']);
        exit;
    }

    $newRecommendStatus = ($action === 'recommend') ? 1 : 0;
    $actionText = ($action === 'recommend') ? '设为推荐' : '取消推荐';
    
    // 如果是设为推荐，检查推荐技师数量限制（可选）
    if ($action === 'recommend') {
        $stmt = $pdo->query("SELECT COUNT(*) FROM technician WHERE is_recommended = 1");
        $recommendCount = $stmt->fetchColumn();
        
        // 可以设置推荐技师数量上限，比如最多20个
        $maxRecommendCount = 20;
        if ($recommendCount >= $maxRecommendCount) {
            echo json_encode(['success' => false, 'msg' => "推荐技师数量已达上限({$maxRecommendCount}个)，请先取消其他技师的推荐状态"]);
            exit;
        }
    }

    // 更新推荐状态
    $stmt = $pdo->prepare("UPDATE technician SET is_recommended = ? WHERE id = ?");
    $result = $stmt->execute([$newRecommendStatus, $id]);
    
    if ($result) {
        echo json_encode([
            'success' => true, 
            'msg' => "技师「{$technician['nick']}」{$actionText}成功",
            'data' => [
                'id' => $id,
                'is_recommended' => $newRecommendStatus,
                'is_recommended_text' => $newRecommendStatus ? '推荐' : '普通'
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'msg' => $actionText . '失败']);
    }

} catch (Exception $e) {
    error_log("Tech recommend error: " . $e->getMessage());
    echo json_encode(['success' => false, 'msg' => '操作失败: ' . $e->getMessage()]);
}
?>
