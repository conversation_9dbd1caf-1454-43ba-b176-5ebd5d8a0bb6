<?php
// 简化版技师编辑 - 专门处理生活照上传问题
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300);

ob_start();

function sendJsonError($message) {
    ob_clean();
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['success' => false, 'msg' => $message]);
    exit;
}

function sendJsonSuccess($message, $data = null) {
    ob_clean();
    header('Content-Type: application/json; charset=utf-8');
    $response = ['success' => true, 'msg' => $message];
    if ($data) $response['data'] = $data;
    echo json_encode($response);
    exit;
}

session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    sendJsonError('未登录');
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendJsonError('请求方法错误');
}

try {
    require_once '../db.php';
    if (!$pdo) {
        sendJsonError('数据库连接失败');
    }

    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    if ($id <= 0) {
        sendJsonError('无效的技师ID');
    }

    // 查询现有技师信息
    $stmt = $pdo->prepare("SELECT * FROM technician WHERE id = ?");
    $stmt->execute([$id]);
    $existingTech = $stmt->fetch();
    
    if (!$existingTech) {
        sendJsonError('技师不存在');
    }

    // 获取基本信息
    $nick = isset($_POST['nick']) ? trim($_POST['nick']) : $existingTech['nick'];
    $phone = isset($_POST['phone']) ? trim($_POST['phone']) : $existingTech['phone'];
    
    // 保持原有文件路径
    $lifeimg_str = $existingTech['lifeimg'];
    
    $debugInfo = [];
    
    // 简化的生活照处理
    if (isset($_FILES['lifeimg'])) {
        $debugInfo['files_received'] = true;
        $debugInfo['files_structure'] = $_FILES['lifeimg'];
        
        $uploadDir = __DIR__ . '/../uploads/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // 检查是否是多文件格式
        if (is_array($_FILES['lifeimg']['name'])) {
            $debugInfo['is_array'] = true;
            $files = $_FILES['lifeimg'];
            $lifeimg_paths = [];
            
            // 删除旧文件
            if ($lifeimg_str) {
                $oldFiles = explode(',', $lifeimg_str);
                foreach ($oldFiles as $oldFile) {
                    $oldFile = trim($oldFile);
                    if ($oldFile && file_exists($uploadDir . basename($oldFile))) {
                        unlink($uploadDir . basename($oldFile));
                    }
                }
            }
            
            // 处理每个文件
            for ($i = 0; $i < count($files['name']); $i++) {
                if (empty($files['name'][$i])) continue;
                
                $debugInfo['file_' . $i] = [
                    'name' => $files['name'][$i],
                    'error' => $files['error'][$i],
                    'size' => $files['size'][$i]
                ];
                
                if ($files['error'][$i] === UPLOAD_ERR_OK) {
                    $extension = strtolower(pathinfo($files['name'][$i], PATHINFO_EXTENSION));
                    $allowedExts = ['jpg', 'jpeg', 'png', 'gif'];
                    
                    if (in_array($extension, $allowedExts) && $files['size'][$i] <= 5 * 1024 * 1024) {
                        $timestamp = time() . '_' . $i;
                        $filename = 'life_simple_' . $timestamp . '.' . $extension;
                        $filepath = $uploadDir . $filename;
                        
                        if (move_uploaded_file($files['tmp_name'][$i], $filepath)) {
                            $lifeimg_paths[] = 'uploads/' . $filename;
                            $debugInfo['file_' . $i]['uploaded'] = true;
                            $debugInfo['file_' . $i]['path'] = 'uploads/' . $filename;
                        } else {
                            $debugInfo['file_' . $i]['upload_failed'] = true;
                        }
                    } else {
                        $debugInfo['file_' . $i]['validation_failed'] = true;
                    }
                }
            }
            
            if (!empty($lifeimg_paths)) {
                $lifeimg_str = implode(',', $lifeimg_paths);
                $debugInfo['final_paths'] = $lifeimg_str;
            }
        } else {
            $debugInfo['is_array'] = false;
            $debugInfo['single_file'] = $_FILES['lifeimg'];
        }
    } else {
        $debugInfo['files_received'] = false;
    }

    // 更新数据库
    $stmt = $pdo->prepare("UPDATE technician SET nick=?, phone=?, lifeimg=? WHERE id=?");
    $result = $stmt->execute([$nick, $phone, $lifeimg_str, $id]);

    if ($result) {
        sendJsonSuccess('简化版修改成功', $debugInfo);
    } else {
        sendJsonError('数据库更新失败');
    }

} catch (Exception $e) {
    error_log("简化版技师编辑错误: " . $e->getMessage());
    sendJsonError('处理失败: ' . $e->getMessage());
}
?>
