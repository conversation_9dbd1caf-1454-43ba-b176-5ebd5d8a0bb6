<?php
// banner_list.php - 前端获取轮播图列表（无需登录）
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

$bannerFile = __DIR__ . '/admin/banner.json';

try {
    if (file_exists($bannerFile)) {
        $banners = json_decode(file_get_contents($bannerFile), true);
        if ($banners === null) {
            echo json_encode(['success' => false, 'msg' => '轮播图数据格式错误']);
            exit;
        }
        
        // 只返回启用的轮播图，按排序顺序
        // 如果没有is_active字段，默认认为是启用的
        $activeBanners = array_filter($banners, function($banner) {
            return !isset($banner['is_active']) || $banner['is_active'] == 1;
        });
        
        // 按sort_order排序
        usort($activeBanners, function($a, $b) {
            $sortA = isset($a['sort_order']) ? (int)$a['sort_order'] : 0;
            $sortB = isset($b['sort_order']) ? (int)$b['sort_order'] : 0;
            return $sortA - $sortB;
        });
        
        echo json_encode(['success' => true, 'data' => array_values($activeBanners)]);
    } else {
        echo json_encode(['success' => true, 'data' => []]);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'msg' => '获取轮播图列表失败: ' . $e->getMessage()]);
}
?>
