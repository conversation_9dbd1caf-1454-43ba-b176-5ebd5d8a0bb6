-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： 127.0.0.1
-- 生成日期： 2025-07-29 10:35:12
-- 服务器版本： 10.4.32-MariaDB
-- PHP 版本： 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `7spa`
--

-- --------------------------------------------------------

--
-- 表的结构 `carousel`
--

CREATE TABLE `carousel` (
  `id` int(10) UNSIGNED NOT NULL,
  `image` varchar(128) NOT NULL,
  `link` varchar(128) DEFAULT NULL,
  `title` varchar(64) DEFAULT NULL,
  `sort` int(10) UNSIGNED DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- 表的结构 `city`
--

CREATE TABLE `city` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `lng` varchar(32) DEFAULT NULL,
  `lat` varchar(32) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `is_hot` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- 转存表中的数据 `city`
--

INSERT INTO `city` (`id`, `name`, `lng`, `lat`, `created_at`, `is_hot`) VALUES
(1, '北京市', '116.4074', '39.9042', '2025-07-20 15:49:39', 0),
(2, '上海市', '121.4737', '31.2304', '2025-07-20 15:49:39', 0),
(3, '天津市', '117.1901', '39.1084', '2025-07-20 15:49:39', 0),
(4, '重庆市', '106.5516', '29.563', '2025-07-20 15:49:39', 0),
(5, '广州市', '113.2644', '23.1291', '2025-07-20 15:49:39', 0),
(6, '深圳市', '114.0579', '22.5431', '2025-07-20 15:49:39', 0),
(7, '杭州市', '120.1551', '30.2741', '2025-07-20 15:49:39', 0),
(8, '南京市', '118.7969', '32.0603', '2025-07-20 15:49:39', 0),
(9, '武汉市', '114.2985', '30.5844', '2025-07-20 15:49:39', 0),
(10, '成都市', '104.0665', '30.5723', '2025-07-20 15:49:39', 0),
(11, '西安市', '108.9398', '34.3416', '2025-07-20 15:49:39', 0),
(12, '郑州市', '113.6254', '34.7466', '2025-07-20 15:49:39', 0),
(13, '济南市', '117.0009', '36.6758', '2025-07-20 15:49:39', 0),
(14, '青岛市', '120.3826', '36.0671', '2025-07-20 15:49:39', 0),
(15, '大连市', '121.6147', '38.914', '2025-07-20 15:49:39', 0),
(16, '沈阳市', '123.4315', '41.8057', '2025-07-20 15:49:39', 0),
(17, '长春市', '125.3245', '43.8171', '2025-07-20 15:49:39', 0),
(18, '哈尔滨市', '126.5358', '45.8023', '2025-07-20 15:49:39', 0),
(19, '石家庄市', '114.5149', '38.0428', '2025-07-20 15:49:39', 0),
(20, '太原市', '112.5489', '37.8706', '2025-07-20 15:49:39', 0),
(21, '呼和浩特市', '111.7519', '40.8414', '2025-07-20 15:49:39', 0),
(22, '南昌市', '115.8921', '28.6765', '2025-07-20 15:49:39', 0),
(23, '合肥市', '117.2272', '31.8206', '2025-07-20 15:49:39', 0),
(24, '福州市', '119.2965', '26.0745', '2025-07-20 15:49:39', 0),
(25, '厦门市', '118.0894', '24.4798', '2025-07-20 15:49:39', 0),
(26, '长沙市', '112.9388', '28.2282', '2025-07-20 15:49:39', 0),
(27, '南宁市', '108.3669', '22.817', '2025-07-20 15:49:39', 0),
(28, '海口市', '110.3312', '20.0311', '2025-07-20 15:49:39', 1),
(29, '贵阳市', '106.7135', '26.5783', '2025-07-20 15:49:39', 0),
(30, '昆明市', '102.8329', '24.8801', '2025-07-20 15:49:39', 0),
(31, '拉萨市', '91.1409', '29.6456', '2025-07-20 15:49:39', 0),
(32, '兰州市', '103.8236', '36.0581', '2025-07-20 15:49:39', 0),
(33, '西宁市', '101.7782', '36.6171', '2025-07-20 15:49:39', 0),
(34, '银川市', '106.2309', '38.4872', '2025-07-20 15:49:39', 0),
(35, '乌鲁木齐市', '87.6177', '43.7928', '2025-07-20 15:49:39', 0),
(36, '苏州市', '120.6519', '31.3989', '2025-07-20 15:49:40', 0),
(37, '无锡市', '120.3019', '31.5747', '2025-07-20 15:49:40', 0),
(38, '常州市', '119.9772', '31.7728', '2025-07-20 15:49:40', 0),
(39, '徐州市', '117.1836', '34.2616', '2025-07-20 15:49:40', 0),
(40, '扬州市', '119.4215', '32.3932', '2025-07-20 15:49:40', 0),
(41, '泰州市', '119.9153', '32.4849', '2025-07-20 15:49:40', 0),
(42, '南通市', '120.8644', '32.0162', '2025-07-20 15:49:40', 0),
(43, '盐城市', '120.1633', '33.3775', '2025-07-20 15:49:40', 0),
(44, '淮安市', '119.0153', '33.5975', '2025-07-20 15:49:40', 0),
(45, '宁波市', '121.544', '29.8683', '2025-07-20 15:49:40', 0),
(46, '温州市', '120.6994', '27.9944', '2025-07-20 15:49:40', 0),
(47, '嘉兴市', '120.7554', '30.7469', '2025-07-20 15:49:40', 0),
(48, '湖州市', '120.0865', '30.8936', '2025-07-20 15:49:40', 0),
(49, '绍兴市', '120.582', '29.9971', '2025-07-20 15:49:40', 0),
(50, '金华市', '119.6491', '29.0895', '2025-07-20 15:49:40', 0),
(51, '台州市', '121.4287', '28.6561', '2025-07-20 15:49:40', 0),
(52, '丽水市', '119.922', '28.4517', '2025-07-20 15:49:40', 0),
(53, '佛山市', '113.122', '23.0288', '2025-07-20 15:49:40', 0),
(54, '东莞市', '113.7518', '23.0489', '2025-07-20 15:49:40', 0),
(55, '中山市', '113.3823', '22.5211', '2025-07-20 15:49:40', 0),
(56, '珠海市', '113.5767', '22.2707', '2025-07-20 15:49:40', 0),
(57, '泉州市', '118.5751', '24.9139', '2025-07-20 15:49:40', 0),
(58, '漳州市', '117.6758', '24.5109', '2025-07-20 15:49:40', 0),
(59, '莆田市', '119.0077', '25.431', '2025-07-20 15:49:40', 0),
(60, '三明市', '117.6087', '26.2654', '2025-07-20 15:49:40', 0),
(61, '龙岩市', '117.0297', '25.0918', '2025-07-20 15:49:40', 0),
(62, '宁德市', '119.527', '26.659', '2025-07-20 15:49:40', 0),
(63, '烟台市', '121.3914', '37.5393', '2025-07-20 15:49:40', 0),
(64, '潍坊市', '119.107', '36.7093', '2025-07-20 15:49:40', 0),
(65, '临沂市', '118.3269', '35.1045', '2025-07-20 15:49:40', 0),
(66, '济宁市', '116.5873', '35.4154', '2025-07-20 15:49:40', 0),
(67, '泰安市', '117.1289', '36.1948', '2025-07-20 15:49:40', 0),
(68, '威海市', '122.1201', '37.5097', '2025-07-20 15:49:40', 0),
(69, '日照市', '119.461', '35.4164', '2025-07-20 15:49:40', 0),
(70, '淄博市', '118.0371', '36.8134', '2025-07-20 15:49:40', 0),
(71, '枣庄市', '117.5578', '34.8564', '2025-07-20 15:49:40', 0),
(72, '东营市', '118.6748', '37.4341', '2025-07-20 15:49:40', 0),
(73, '聊城市', '115.9851', '36.456', '2025-07-20 15:49:40', 0),
(74, '德州市', '116.3073', '37.4341', '2025-07-20 15:49:40', 0),
(75, '滨州市', '118.0371', '37.3835', '2025-07-20 15:49:40', 0),
(76, '洛阳市', '112.454', '34.6197', '2025-07-20 15:49:40', 0),
(77, '开封市', '114.3411', '34.7971', '2025-07-20 15:49:40', 0),
(78, '新乡市', '113.9268', '35.303', '2025-07-20 15:49:40', 0),
(79, '焦作市', '113.2418', '35.2158', '2025-07-20 15:49:40', 0),
(80, '安阳市', '114.3927', '36.1034', '2025-07-20 15:49:40', 0),
(81, '平顶山市', '113.1929', '33.7453', '2025-07-20 15:49:40', 0),
(82, '宜昌市', '111.2905', '30.7019', '2025-07-20 15:49:40', 0),
(83, '襄阳市', '112.1441', '32.042', '2025-07-20 15:49:41', 0),
(84, '荆州市', '112.2386', '30.3269', '2025-07-20 15:49:41', 0),
(85, '黄石市', '115.077', '30.22', '2025-07-20 15:49:41', 0),
(86, '十堰市', '110.7982', '32.6469', '2025-07-20 15:49:41', 0),
(87, '株洲市', '113.1518', '27.8274', '2025-07-20 15:49:41', 0),
(88, '湘潭市', '112.9445', '27.8294', '2025-07-20 15:49:41', 0),
(89, '衡阳市', '112.6072', '26.8934', '2025-07-20 15:49:41', 0),
(90, '岳阳市', '113.1287', '29.357', '2025-07-20 15:49:41', 0),
(91, '常德市', '111.699', '29.0402', '2025-07-20 15:49:41', 0),
(92, '张家界市', '110.479', '29.1274', '2025-07-20 15:49:41', 0),
(93, '柳州市', '109.4281', '24.3146', '2025-07-20 15:49:41', 0),
(94, '桂林市', '110.2993', '25.2342', '2025-07-20 15:49:41', 0),
(95, '北海市', '109.1193', '21.4733', '2025-07-20 15:49:41', 0),
(96, '三亚市', '109.5082', '18.2479', '2025-07-20 15:49:41', 1),
(97, '绵阳市', '104.6419', '31.464', '2025-07-20 15:49:41', 0),
(98, '德阳市', '104.3982', '31.127', '2025-07-20 15:49:41', 0),
(99, '宜宾市', '104.6308', '28.7602', '2025-07-20 15:49:41', 0),
(100, '遵义市', '106.9348', '27.7253', '2025-07-20 15:49:41', 0),
(101, '大理市', '100.267', '25.6064', '2025-07-20 15:49:41', 0),
(102, '丽江市', '100.233', '26.8721', '2025-07-20 15:49:41', 0),
(103, '宝鸡市', '107.1826', '34.364', '2025-07-20 15:49:41', 0),
(104, '咸阳市', '108.7093', '34.3336', '2025-07-20 15:49:41', 0),
(105, '包头市', '109.9402', '40.6522', '2025-07-20 15:49:41', 0),
(106, '鄂尔多斯市', '109.99', '39.8172', '2025-07-20 15:49:41', 0),
(107, '大庆市', '125.1031', '46.5907', '2025-07-20 15:49:41', 0),
(108, '齐齐哈尔市', '123.918', '47.342', '2025-07-20 15:49:41', 0),
(109, '吉林市', '126.5502', '43.8436', '2025-07-20 15:49:41', 0),
(110, '鞍山市', '122.9951', '41.1106', '2025-07-20 15:49:41', 0),
(111, '抚顺市', '123.957', '41.8654', '2025-07-20 15:49:41', 0),
(112, '唐山市', '118.1758', '39.6304', '2025-07-20 15:49:41', 0),
(113, '保定市', '115.4648', '38.8971', '2025-07-20 15:49:41', 0),
(114, '廊坊市', '116.7039', '39.5231', '2025-07-20 15:49:41', 0),
(115, '秦皇岛市', '119.5881', '39.9425', '2025-07-20 15:49:41', 0),
(116, '大同市', '113.3007', '40.0769', '2025-07-20 15:49:41', 0),
(117, '芜湖市', '118.376', '31.3262', '2025-07-20 15:49:41', 0),
(118, '蚌埠市', '117.3889', '32.9169', '2025-07-20 15:49:41', 0),
(119, '九江市', '115.9929', '29.7196', '2025-07-20 15:49:41', 0),
(120, '赣州市', '114.94', '25.8312', '2025-07-20 15:49:41', 0),
(121, '克拉玛依市', '84.8739', '45.5959', '2025-07-20 15:49:41', 0);

-- --------------------------------------------------------

--
-- 表的结构 `feed`
--

CREATE TABLE `feed` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `content` text NOT NULL,
  `images` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- 转存表中的数据 `feed`
--

INSERT INTO `feed` (`id`, `user_id`, `content`, `images`, `created_at`) VALUES
(1, 1, 'nihao', '[\"uploads\\/feed_20250712070608_8969.gif\"]', '2025-07-12 05:06:08'),
(2, 1, 'hhhhhh', '[\"uploads\\/feed_20250712070732_4867.gif\"]', '2025-07-12 05:07:32'),
(3, 1, '今天很开心', '', '2025-07-12 05:09:07'),
(4, 1, '今天很开心', '[\"uploads\\/feed_20250712071140_5820.gif\"]', '2025-07-12 05:11:40'),
(5, 1, '今天很开心', '', '2025-07-12 05:13:56'),
(6, 1, '你好啊  今天是7.12号', '[\"uploads\\/feed_20250712071600_7757.gif\"]', '2025-07-12 05:16:00'),
(7, 1, '测试', '[\"uploads\\/feed_20250712071618_4944.mp4\"]', '2025-07-12 05:16:18');

-- --------------------------------------------------------

--
-- 表的结构 `feeds`
--

CREATE TABLE `feeds` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content` text NOT NULL COMMENT '动态内容',
  `images` text DEFAULT NULL COMMENT '图片列表(JSON格式)',
  `video` varchar(255) DEFAULT NULL COMMENT '视频文件',
  `location` varchar(100) DEFAULT NULL COMMENT '位置信息',
  `likes_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `comments_count` int(11) DEFAULT 0 COMMENT '评论数',
  `is_public` tinyint(1) DEFAULT 1 COMMENT '是否公开(1=公开,0=私密)',
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户动态表';

-- --------------------------------------------------------

--
-- 表的结构 `project_article`
--

CREATE TABLE `project_article` (
  `id` int(11) NOT NULL,
  `title` varchar(50) NOT NULL,
  `content` text NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- 转存表中的数据 `project_article`
--

INSERT INTO `project_article` (`id`, `title`, `content`, `created_at`, `updated_at`) VALUES
(1, '项目表介绍', '看不懂项目表的看这里:\n \n⭐⭐⭐ 会所--可以选人技师有地方 也可以上门--上门需要加往返路费，不清楚多少的可以看技师打车记录（这里是会所价目表 👇 👇 👇）\n1:中式按摩300  就是不脱衣服按摩 不带推油.\n2:泰式按摩480  脱衣服按摩推油和飞机.\n3:日式按摩1300  就是快餐  没有按摩推油.\n4:全套按摩1700  就是按摩推油 加快餐.\n\n⭐⭐⭐工作室--不能选人-300以上可以上门-（这里是工作室价目表 👇 👇 👇）\n1:中式按摩200  就是按摩带推油.\n2:泰式按摩300  脱衣服按摩推油和飞机.\n3:日式按摩800  就是快餐  没有按摩推油.\n4:全套按摩1000  就是按摩推油 加快餐.', '2025-07-17 01:22:45', '2025-07-17 02:24:58');

-- --------------------------------------------------------

--
-- 表的结构 `services`
--

CREATE TABLE `services` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '项目名称',
  `image` varchar(255) DEFAULT NULL COMMENT '项目图片',
  `service_code` varchar(50) NOT NULL COMMENT '项目编号',
  `duration` int(11) NOT NULL COMMENT '项目时长(分钟)',
  `price` decimal(10,2) NOT NULL COMMENT '项目价格',
  `virtual_sales` int(11) DEFAULT 0 COMMENT '虚拟购买量',
  `service_type` varchar(20) DEFAULT 'spa' COMMENT '项目类型',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序顺序',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活(1=激活,0=停用)',
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='服务项目表';

--
-- 转存表中的数据 `services`
--

INSERT INTO `services` (`id`, `name`, `image`, `service_code`, `duration`, `price`, `virtual_sales`, `service_type`, `sort_order`, `is_active`, `created_at`, `updated_at`) VALUES
(6, '中式按摩', 'uploads/services/service_20250719_140624_7579.jpg', '项目一', 40, 300.00, 300, 'spa', 1, 1, '2025-07-19 20:06:24', '2025-07-19 20:06:24'),
(7, '泰式按摩', 'uploads/services/service_20250719_140649_1565.jpg', '项目二', 60, 480.00, 2000, 'spa', 2, 1, '2025-07-19 20:06:49', '2025-07-19 20:06:49'),
(8, '日式快餐', 'uploads/services/service_20250719_140742_1687.jpg', '项目三', 90, 1300.00, 900, 'spa', 3, 1, '2025-07-19 20:07:42', '2025-07-19 20:07:42'),
(9, '全套套餐', 'uploads/services/service_20250719_140816_8534.jpg', '项目四', 90, 1700.00, 800, 'spa', 4, 1, '2025-07-19 20:08:16', '2025-07-19 20:08:16'),
(10, '过夜套餐', 'uploads/services/service_20250719_140855_4010.jpg', '项目五', 600, 3500.00, 400, 'spa', 5, 1, '2025-07-19 20:08:55', '2025-07-19 20:08:55'),
(11, '伴游套餐', 'uploads/services/service_20250719_140954_9695.jpg', '项目六', 999, 4000.00, 5, 'spa', 6, 1, '2025-07-19 20:09:54', '2025-07-19 20:09:54'),
(12, '中式按摩', 'uploads/services/service_20250719_141714_7969.jpg', '项目一', 60, 200.00, 200, 'studio', 7, 1, '2025-07-19 20:17:14', '2025-07-19 20:17:14'),
(13, '泰式按摩', 'uploads/services/service_20250719_141745_6548.jpg', '项目二', 60, 300.00, 300, 'studio', 8, 1, '2025-07-19 20:17:45', '2025-07-19 20:17:45'),
(14, '快餐项目', 'uploads/services/service_20250719_141819_5492.jpg', '项目三', 60, 600.00, 200, 'studio', 9, 1, '2025-07-19 20:18:19', '2025-07-19 20:18:19'),
(15, '全套套餐', 'uploads/services/service_20250719_141847_1009.jpg', '项目四', 90, 1000.00, 100, 'studio', 10, 1, '2025-07-19 20:18:47', '2025-07-19 20:18:47'),
(16, '日式快餐', 'uploads/services/service_20250719_141945_9737.jpg', '项目一', 60, 3000.00, 50, 'ts', 11, 1, '2025-07-19 20:19:45', '2025-07-19 20:19:45'),
(17, '过夜套餐', 'uploads/services/service_20250719_142019_9965.jpg', '项目二', 999, 6000.00, 400, 'ts', 12, 1, '2025-07-19 20:20:19', '2025-07-19 20:20:19'),
(18, '口飞套餐', 'uploads/services/service_20250719_142108_3741.jpg', '项目一', 60, 500.00, 200, 'cd', 13, 1, '2025-07-19 20:21:08', '2025-07-19 20:21:08'),
(19, '日式快餐', 'uploads/services/service_20250719_142133_4916.jpg', '项目二', 60, 1000.00, 300, 'cd', 14, 1, '2025-07-19 20:21:33', '2025-07-19 20:21:33');

-- --------------------------------------------------------

--
-- 表的结构 `taxi_article`
--

CREATE TABLE `taxi_article` (
  `id` int(11) NOT NULL,
  `title` varchar(50) NOT NULL,
  `content` text NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- 转存表中的数据 `taxi_article`
--

INSERT INTO `taxi_article` (`id`, `title`, `content`, `created_at`, `updated_at`) VALUES
(1, '路费说明', '技师上门加收往返路费，\n不清楚路费的可以看技师的打车支付费用。\n\nPS:1-亚龙湾顾客 快餐起才上门 泰式一下不上门包含泰式\n   \n   2-海棠湾，崖州顾客 全套加路费才上门，其他项目不参与', '2025-07-17 00:55:25', '2025-07-17 02:25:26');

-- --------------------------------------------------------

--
-- 表的结构 `technician`
--

CREATE TABLE `technician` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `info` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `nick` varchar(32) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `age` varchar(8) DEFAULT NULL,
  `height` varchar(8) DEFAULT NULL,
  `weight` varchar(8) DEFAULT NULL,
  `size` varchar(16) DEFAULT NULL,
  `model` varchar(32) DEFAULT '',
  `city` varchar(32) DEFAULT NULL,
  `hotel` varchar(64) DEFAULT NULL,
  `workimg` varchar(128) DEFAULT NULL,
  `lifeimg` text DEFAULT NULL,
  `video` varchar(128) DEFAULT NULL,
  `apply_time` datetime DEFAULT current_timestamp(),
  `status` varchar(16) DEFAULT 'pending',
  `reject_reason` varchar(255) DEFAULT '',
  `password` varchar(64) DEFAULT '',
  `last_visit` datetime DEFAULT NULL,
  `is_black` tinyint(1) DEFAULT 0,
  `is_recommended` tinyint(1) DEFAULT 0 COMMENT '是否推荐(1=推荐,0=普通)',
  `virtual_orders` int(11) DEFAULT 0 COMMENT '虚拟单量',
  `is_resting` tinyint(1) DEFAULT 0 COMMENT '是否休息(1=休息中,0=工作中)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- 转存表中的数据 `technician`
--

INSERT INTO `technician` (`id`, `user_id`, `info`, `created_at`, `nick`, `phone`, `age`, `height`, `weight`, `size`, `model`, `city`, `hotel`, `workimg`, `lifeimg`, `video`, `apply_time`, `status`, `reject_reason`, `password`, `last_visit`, `is_black`, `is_recommended`, `virtual_orders`, `is_resting`) VALUES
(51, 0, NULL, '2025-07-19 11:22:02', '李大宝099', '13168001069', '35', '170', '70', '15', '0.5', '三亚市', '三亚市商品街一巷', 'uploads/work_1752924122286.jpg', 'uploads/life_20250728_182836_1511_0.jpg', 'uploads/video_1752924122450.mp4', '2025-07-19 19:22:02', 'approved', '', '', NULL, 0, 0, 200, 0),
(74, 0, NULL, '2025-07-28 16:17:20', '风273', '13002996386', '26', '175', '60', '17', '0.5', '海口市', '海口人民公园', 'uploads/work_1753719440936.jpg', 'uploads/life_1753719440900_0.jpg,uploads/life_1753719440720_1.jpg,uploads/life_1753719440403_2.jpg', '', '2025-07-29 00:17:20', 'approved', '', '', NULL, 0, 0, 200, 0),
(75, 0, NULL, '2025-07-28 16:20:29', '帝惰264', '15303242211', '27', '177', '70', '18', '0.5', '海口市', '海口人民公园', 'uploads/work_1753719629703.jpg', 'uploads/life_1753719629127_0.jpeg,uploads/life_1753719629735_1.jpg', '', '2025-07-29 00:20:29', 'approved', '', '', NULL, 0, 0, 200, 0),
(76, 0, NULL, '2025-07-28 16:23:19', '小鹿272', '15272918454', '26', '178', '70', '17', '1', '海口市', '海口人民公园', 'uploads/work_1753719799950.jpeg', 'uploads/life_1753719799578_0.jpeg,uploads/life_1753719799717_1.jpeg,uploads/life_1753719799532_2.jpeg', '', '2025-07-29 00:23:19', 'approved', '', '', NULL, 0, 0, 200, 0),
(77, 0, NULL, '2025-07-28 16:26:51', '无恙276', '13006025213', '20', '175', '55', '17', '0.5', '海口市', '海口人民公园', 'uploads/work_1753720011478.jpg', 'uploads/life_1753720011831_0.jpeg,uploads/life_1753720011168_1.jpg,uploads/life_1753720011625_2.jpg,uploads/life_1753720011909_3.jpeg', '', '2025-07-29 00:26:51', 'approved', '', '', NULL, 0, 0, 200, 0);

-- --------------------------------------------------------

--
-- 表的结构 `technician_feeds`
--

CREATE TABLE `technician_feeds` (
  `id` int(11) NOT NULL,
  `technician_id` int(11) NOT NULL,
  `technician_name` varchar(100) NOT NULL,
  `age` int(11) DEFAULT NULL,
  `height` int(11) DEFAULT NULL,
  `weight` int(11) DEFAULT NULL,
  `service_years` int(11) DEFAULT NULL,
  `city` varchar(100) NOT NULL,
  `previous_city` varchar(100) DEFAULT NULL,
  `action_type` enum('join','move') NOT NULL DEFAULT 'join',
  `content` text DEFAULT NULL,
  `avatar` varchar(255) DEFAULT 'images/tx.jpg',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `technician_feeds`
--

INSERT INTO `technician_feeds` (`id`, `technician_id`, `technician_name`, `age`, `height`, `weight`, `service_years`, `city`, `previous_city`, `action_type`, `content`, `avatar`, `created_at`, `updated_at`, `is_active`) VALUES
(1, 1, '李大宝', 22, 175, 60, 16, '三亚市', NULL, 'join', '技师李大宝 22/175/60/16 到三亚了', 'images/tx.jpg', '2025-07-24 09:47:38', '2025-07-24 09:47:38', 1),
(2, 2, '王小明', 25, 180, 70, 20, '海口市', NULL, 'join', '技师王小明 25/180/70/20 到海口了', 'images/tx.jpg', '2025-07-24 09:47:38', '2025-07-24 09:47:38', 1),
(3, 1, '李大宝', 22, 175, 60, 16, '海口市', '三亚市', 'move', '技师李大宝 22/175/60/16 到海口了', 'images/tx.jpg', '2025-07-24 09:47:38', '2025-07-24 09:47:38', 1),
(4, 3, '张伟', 28, 178, 65, 25, '儋州市', NULL, 'join', '技师张伟 28/178/65/25 到儋州了', 'images/tx.jpg', '2025-07-24 09:47:38', '2025-07-24 09:47:38', 1),
(5, 4, '刘强', 24, 172, 58, 18, '琼海市', NULL, 'join', '技师刘强 24/172/58/18 到琼海了', 'images/tx.jpg', '2025-07-24 09:47:38', '2025-07-24 09:47:38', 1),
(6, 2, '王小明', 25, 180, 70, 20, '三亚市', '海口市', 'move', '技师王小明 25/180/70/20 到三亚了', 'images/tx.jpg', '2025-07-24 09:47:38', '2025-07-24 09:47:38', 1),
(7, 1, '李大宝', 22, 175, 60, 16, '三亚市', NULL, 'join', '技师李大宝 22/175/60/16 到三亚市了', 'images/tx.jpg', '2025-07-24 09:59:49', '2025-07-24 09:59:49', 1),
(8, 1, '李大宝', 22, 175, 60, 16, '三亚市', NULL, 'join', '技师李大宝 22/175/60/16 到三亚市了', 'images/tx.jpg', '2025-07-24 09:59:54', '2025-07-24 09:59:54', 1),
(9, 1, '李大宝', 22, 175, 60, 16, '三亚市', NULL, 'join', '技师李大宝 22/175/60/16 到三亚市了', 'images/tx.jpg', '2025-07-24 09:59:55', '2025-07-24 09:59:55', 1),
(10, 1, '李大宝', 22, 175, 60, 16, '三亚市', NULL, 'join', '技师李大宝 22/175/60/16 到三亚市了', 'images/tx.jpg', '2025-07-24 09:59:55', '2025-07-24 09:59:55', 1),
(11, 1, '李大宝', 22, 175, 60, 16, '三亚市', NULL, 'join', '技师李大宝 22/175/60/16 到三亚市了', 'images/tx.jpg', '2025-07-24 10:00:01', '2025-07-24 10:00:01', 1),
(12, 333, '测试技师64', 25, 180, 70, 20, '测试城市', NULL, 'join', '技师测试技师64 25/180/70/20 到测试城市了', 'images/tx.jpg', '2025-07-24 10:45:43', '2025-07-24 10:45:43', 1),
(13, 52, '测试技师001', 25, 165, 50, 0, '赣州市', '北京市', 'move', '技师测试技师001 25/165/50/C 到赣州市了', 'images/tx.jpg', '2025-07-24 10:49:16', '2025-07-24 10:49:16', 1),
(14, 52, '测试技师001', 25, 165, 50, 0, '儋州市', '赣州市', 'move', '技师测试技师001 25/165/50/C 到儋州市了', 'images/tx.jpg', '2025-07-24 10:51:11', '2025-07-24 10:51:11', 1),
(15, 72, '王明飞', 29, 185, 62, 18, '海口市', '三亚市', 'move', '技师王明飞 29/185/62/18 到海口市了', 'images/tx.jpg', '2025-07-24 10:58:33', '2025-07-24 10:58:33', 1),
(16, 52, '测试技师001', 25, 165, 50, 18, '北京市', '儋州市', 'move', '技师测试技师001 25/165/50/18 到北京市了', 'images/tx.jpg', '2025-07-25 12:50:23', '2025-07-25 12:50:23', 1);

-- --------------------------------------------------------

--
-- 表的结构 `user`
--

CREATE TABLE `user` (
  `id` int(10) UNSIGNED NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password` varchar(64) NOT NULL,
  `password_plain` varchar(255) DEFAULT '',
  `nickname` varchar(32) DEFAULT NULL,
  `avatar` varchar(128) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_black` tinyint(1) DEFAULT 0,
  `last_visit` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- 转存表中的数据 `user`
--

INSERT INTO `user` (`id`, `phone`, `password`, `password_plain`, `nickname`, `avatar`, `created_at`, `is_black`, `last_visit`) VALUES
(1, '13168001069', '$2y$10$hz9vV4PqsbgQPk6jS2lideG12/v33UuxawsoaY/h/pAgEuvTdqjXO', '123456', '李大宝', 'images/am.jpg', '2025-07-11 17:38:36', 0, '2025-07-16 20:18:54'),
(2, '17325017428', '$2y$10$hz9vV4PqsbgQPk6jS2lideG12/v33UuxawsoaY/h/pAgEuvTdqjXO', '123456', NULL, NULL, '2025-07-16 13:54:10', 0, NULL),
(3, '15790464298', '$2y$10$F7fUko.CSTMwNnXsqTVeC.xJ7Bx37ytkfBKPkIQhSvDi3ZxgUlFdC', '123456', NULL, NULL, '2025-07-16 18:44:31', 0, NULL),
(4, '13100000000', '$2y$10$MHyKd6TzLSDpLNjcCRQzoueAlWChKLPq5apq7Wd2xA.YYcbyAhQLG', '123456', NULL, NULL, '2025-07-16 18:45:09', 0, NULL),
(5, '13168001067', '$2y$10$FIhNeIdd05hhypcdUOiDienGJFAdWiiKlVJBSmybv3bvvBzBi6VsG', '123456', NULL, NULL, '2025-07-25 11:36:24', 0, NULL),
(6, '13002996386', '$2y$10$eRocnoyfpzNPAjNkYzBs1OycX2ecX.2U0D6vvCgFJeQwZ..TiSkeO', '123456', NULL, NULL, '2025-07-28 16:11:39', 0, NULL),
(7, '15303242211', '$2y$10$AC9YaVjcgXRaH0Jw1V22lOWTKpx6DUZYUTNIW90xCOsyhG7IC6qrq', '123456', NULL, NULL, '2025-07-28 16:19:02', 0, NULL),
(8, '15272918454', '$2y$10$wLeP.9y7e6Ei.D7xHKQhqOFSkrxe/.ff9ME6Z88I/mpm5oWFoSqI6', '123456', NULL, NULL, '2025-07-28 16:21:40', 0, NULL),
(9, '13006025213', '$2y$10$6kA.ssdVyBMYAx42vlGWvOWq2fTZH9A9Wxd.9tZAjUzxSirlM9h2a', '123456', NULL, NULL, '2025-07-28 16:24:42', 0, NULL);

--
-- 转储表的索引
--

--
-- 表的索引 `carousel`
--
ALTER TABLE `carousel`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `city`
--
ALTER TABLE `city`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `feed`
--
ALTER TABLE `feed`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- 表的索引 `feeds`
--
ALTER TABLE `feeds`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_is_public` (`is_public`);

--
-- 表的索引 `project_article`
--
ALTER TABLE `project_article`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `taxi_article`
--
ALTER TABLE `taxi_article`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `technician`
--
ALTER TABLE `technician`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- 表的索引 `technician_feeds`
--
ALTER TABLE `technician_feeds`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_technician_id` (`technician_id`),
  ADD KEY `idx_city` (`city`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- 表的索引 `user`
--
ALTER TABLE `user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uniq_phone` (`phone`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `carousel`
--
ALTER TABLE `carousel`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `city`
--
ALTER TABLE `city`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=122;

--
-- 使用表AUTO_INCREMENT `feed`
--
ALTER TABLE `feed`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- 使用表AUTO_INCREMENT `feeds`
--
ALTER TABLE `feeds`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- 使用表AUTO_INCREMENT `project_article`
--
ALTER TABLE `project_article`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- 使用表AUTO_INCREMENT `services`
--
ALTER TABLE `services`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- 使用表AUTO_INCREMENT `taxi_article`
--
ALTER TABLE `taxi_article`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- 使用表AUTO_INCREMENT `technician`
--
ALTER TABLE `technician`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=78;

--
-- 使用表AUTO_INCREMENT `technician_feeds`
--
ALTER TABLE `technician_feeds`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- 使用表AUTO_INCREMENT `user`
--
ALTER TABLE `user`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
