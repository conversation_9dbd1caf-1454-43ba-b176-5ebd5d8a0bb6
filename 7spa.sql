-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： 127.0.0.1
-- 生成日期： 2025-07-16 20:01:00
-- 服务器版本： 10.4.32-MariaDB
-- PHP 版本： 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `7spa`
--

-- --------------------------------------------------------

--
-- 表的结构 `carousel`
--

CREATE TABLE `carousel` (
  `id` int(10) UNSIGNED NOT NULL,
  `image` varchar(128) NOT NULL,
  `link` varchar(128) DEFAULT NULL,
  `title` varchar(64) DEFAULT NULL,
  `sort` int(10) UNSIGNED DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- 表的结构 `feed`
--

CREATE TABLE `feed` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `content` text NOT NULL,
  `images` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- 转存表中的数据 `feed`
--

INSERT INTO `feed` (`id`, `user_id`, `content`, `images`, `created_at`) VALUES
(1, 1, 'nihao', '[\"uploads\\/feed_20250712070608_8969.gif\"]', '2025-07-12 05:06:08'),
(2, 1, 'hhhhhh', '[\"uploads\\/feed_20250712070732_4867.gif\"]', '2025-07-12 05:07:32'),
(3, 1, '今天很开心', '', '2025-07-12 05:09:07'),
(4, 1, '今天很开心', '[\"uploads\\/feed_20250712071140_5820.gif\"]', '2025-07-12 05:11:40'),
(5, 1, '今天很开心', '', '2025-07-12 05:13:56'),
(6, 1, '你好啊  今天是7.12号', '[\"uploads\\/feed_20250712071600_7757.gif\"]', '2025-07-12 05:16:00'),
(7, 1, '测试', '[\"uploads\\/feed_20250712071618_4944.mp4\"]', '2025-07-12 05:16:18');

-- --------------------------------------------------------

--
-- 表的结构 `project_article`
--

CREATE TABLE `project_article` (
  `id` int(11) NOT NULL,
  `title` varchar(50) NOT NULL,
  `content` text NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- 转存表中的数据 `project_article`
--

INSERT INTO `project_article` (`id`, `title`, `content`, `created_at`, `updated_at`) VALUES
(1, '项目表介绍', '项目表介绍项目表介绍项目表介绍项目表介绍', '2025-07-17 01:22:45', '2025-07-17 01:43:24');

-- --------------------------------------------------------

--
-- 表的结构 `taxi_article`
--

CREATE TABLE `taxi_article` (
  `id` int(11) NOT NULL,
  `title` varchar(50) NOT NULL,
  `content` text NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- 转存表中的数据 `taxi_article`
--

INSERT INTO `taxi_article` (`id`, `title`, `content`, `created_at`, `updated_at`) VALUES
(1, '路费说明', '路费说明路费说明路费说明', '2025-07-17 00:55:25', '2025-07-17 01:21:31');

-- --------------------------------------------------------

--
-- 表的结构 `technician`
--

CREATE TABLE `technician` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `info` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `nick` varchar(32) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `age` varchar(8) DEFAULT NULL,
  `height` varchar(8) DEFAULT NULL,
  `weight` varchar(8) DEFAULT NULL,
  `size` varchar(16) DEFAULT NULL,
  `city` varchar(32) DEFAULT NULL,
  `hotel` varchar(64) DEFAULT NULL,
  `workimg` varchar(128) DEFAULT NULL,
  `lifeimg` text DEFAULT NULL,
  `video` varchar(128) DEFAULT NULL,
  `apply_time` datetime DEFAULT current_timestamp(),
  `status` varchar(16) DEFAULT 'pending',
  `reject_reason` varchar(255) DEFAULT '',
  `password` varchar(64) DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- 转存表中的数据 `technician`
--

INSERT INTO `technician` (`id`, `user_id`, `info`, `created_at`, `nick`, `phone`, `age`, `height`, `weight`, `size`, `city`, `hotel`, `workimg`, `lifeimg`, `video`, `apply_time`, `status`, `reject_reason`, `password`) VALUES
(2, 0, NULL, '2025-07-16 13:57:42', '李帅', '17325017428', '22', '175', '60', '18', '三亚市', '商品街一巷', 'uploads/work_1752674262780.jpg', 'uploads/life_1752674262494_0.jpg', 'uploads/video_1752674262319.mp4', '2025-07-16 21:57:42', 'pending', '', '');

-- --------------------------------------------------------

--
-- 表的结构 `user`
--

CREATE TABLE `user` (
  `id` int(10) UNSIGNED NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password` varchar(64) NOT NULL,
  `password_plain` varchar(255) DEFAULT '',
  `nickname` varchar(32) DEFAULT NULL,
  `avatar` varchar(128) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_black` tinyint(1) DEFAULT 0,
  `last_visit` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- 转存表中的数据 `user`
--

INSERT INTO `user` (`id`, `phone`, `password`, `password_plain`, `nickname`, `avatar`, `created_at`, `is_black`, `last_visit`) VALUES
(1, '13168001069', '$2y$10$hz9vV4PqsbgQPk6jS2lideG12/v33UuxawsoaY/h/pAgEuvTdqjXO', '123456', '李大宝', 'images/am.jpg', '2025-07-11 17:38:36', 0, '2025-07-16 20:18:54'),
(2, '17325017428', '$2y$10$hz9vV4PqsbgQPk6jS2lideG12/v33UuxawsoaY/h/pAgEuvTdqjXO', '123456', NULL, NULL, '2025-07-16 13:54:10', 0, NULL);

--
-- 转储表的索引
--

--
-- 表的索引 `carousel`
--
ALTER TABLE `carousel`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `feed`
--
ALTER TABLE `feed`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- 表的索引 `project_article`
--
ALTER TABLE `project_article`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `taxi_article`
--
ALTER TABLE `taxi_article`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `technician`
--
ALTER TABLE `technician`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- 表的索引 `user`
--
ALTER TABLE `user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uniq_phone` (`phone`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `carousel`
--
ALTER TABLE `carousel`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `feed`
--
ALTER TABLE `feed`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- 使用表AUTO_INCREMENT `project_article`
--
ALTER TABLE `project_article`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- 使用表AUTO_INCREMENT `taxi_article`
--
ALTER TABLE `taxi_article`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- 使用表AUTO_INCREMENT `technician`
--
ALTER TABLE `technician`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- 使用表AUTO_INCREMENT `user`
--
ALTER TABLE `user`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
