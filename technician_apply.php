<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);
header('Content-Type: application/json; charset=utf-8');
ob_clean(); // 清理输出缓冲，防止多余内容影响 JSON

// 数据库配置
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = '7spa'; // 请根据实际数据库名修改

// 连接数据库
$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
if ($conn->connect_error) {
    echo json_encode(['success'=>false, 'msg'=>'数据库连接失败']);
    exit;
}

// 检查是否是状态查询请求
$action = $_POST['action'] ?? '';
if ($action === 'status') {
    $phone = $_POST['phone'] ?? '';
    if (!$phone) {
        echo json_encode(['success'=>false, 'msg'=>'手机号不能为空']);
        exit;
    }

    // 查询用户申请状态
    $stmt = $conn->prepare("SELECT status FROM technician WHERE phone = ? ORDER BY id DESC LIMIT 1");
    $stmt->bind_param('s', $phone);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($row = $result->fetch_assoc()) {
        echo json_encode(['success'=>true, 'status'=>$row['status']]);
    } else {
        echo json_encode(['success'=>true, 'status'=>'none']); // 未申请过
    }
    $stmt->close();
    $conn->close();
    exit;
}
// 处理申请提交
$nick = $_POST['nick'] ?? '';
$phone = $_POST['phone'] ?? '';
$password = $_POST['password'] ?? '';
$age = $_POST['age'] ?? '';
$height = $_POST['height'] ?? '';
$weight = $_POST['weight'] ?? '';
$size = $_POST['size'] ?? '';
$model = $_POST['model'] ?? '';
$city = $_POST['city'] ?? '';
$hotel = $_POST['hotel'] ?? '';

// 基本验证
if (!$nick || !$phone || !$age || !$height || !$weight || !$size || $model === '' || !$city || !$hotel) {
    echo json_encode(['success'=>false, 'msg'=>'请填写完整信息']);
    exit;
}

// 检查是否已经申请过
$stmt = $conn->prepare("SELECT id, status FROM technician WHERE phone = ? ORDER BY id DESC LIMIT 1");
$stmt->bind_param('s', $phone);
$stmt->execute();
$result = $stmt->get_result();

$existingRecord = null;
if ($row = $result->fetch_assoc()) {
    $existingRecord = $row;
    $status = $row['status'];
    if ($status === 'pending') {
        echo json_encode(['success'=>false, 'msg'=>'您已提交过技师申请，请等待审核']);
        exit;
    } elseif ($status === 'approved') {
        echo json_encode(['success'=>false, 'msg'=>'您已通过技师审核，无需重复申请']);
        exit;
    }
    // 如果是rejected状态，允许重新申请，稍后会更新现有记录
}
$stmt->close();

// 文件上传处理
$upload_dir = 'uploads/';
if (!is_dir($upload_dir)) mkdir($upload_dir, 0777, true);
// 工作照
$workimg_path = '';
if (isset($_FILES['workimg']) && $_FILES['workimg']['error'] == 0) {
    $ext = pathinfo($_FILES['workimg']['name'], PATHINFO_EXTENSION);
    $workimg_path = $upload_dir . 'work_' . time() . rand(100,999) . '.' . $ext;
    move_uploaded_file($_FILES['workimg']['tmp_name'], $workimg_path);
}
// 生活照（多张）
$lifeimg_paths = [];
if (isset($_FILES['lifeimg'])) {
    if (is_array($_FILES['lifeimg']['name'])) {
        foreach ($_FILES['lifeimg']['name'] as $i => $name) {
            if ($_FILES['lifeimg']['error'][$i] == 0) {
                $ext = pathinfo($name, PATHINFO_EXTENSION);
                $path = $upload_dir . 'life_' . time() . rand(100,999) . '_' . $i . '.' . $ext;
                move_uploaded_file($_FILES['lifeimg']['tmp_name'][$i], $path);
                $lifeimg_paths[] = $path;
            }
        }
    }
}
$lifeimg_str = implode(',', $lifeimg_paths);
// 自动建表，确保字段齐全
$conn->query("CREATE TABLE IF NOT EXISTS technician (
  id INT AUTO_INCREMENT PRIMARY KEY,
  nick VARCHAR(64),
  phone VARCHAR(32),
  password VARCHAR(64),
  age INT,
  height INT,
  weight INT,
  size VARCHAR(32),
  model VARCHAR(32),
  city VARCHAR(32),
  hotel VARCHAR(64),
  workimg VARCHAR(255),
  lifeimg TEXT,
  video VARCHAR(255),
  status VARCHAR(16) DEFAULT 'pending',
  apply_time DATETIME DEFAULT CURRENT_TIMESTAMP
) CHARSET=utf8mb4;");
// 视频
$video_path = '';
if (isset($_FILES['video']) && $_FILES['video']['error'] == 0) {
    $ext = pathinfo($_FILES['video']['name'], PATHINFO_EXTENSION);
    $video_path = $upload_dir . 'video_' . time() . rand(100,999) . '.' . $ext;
    move_uploaded_file($_FILES['video']['tmp_name'], $video_path);
}
// 保存到数据库
$status = 'pending';
if ($existingRecord && $existingRecord['status'] === 'rejected') {
    // 如果是被驳回的用户重新申请，更新现有记录
    $stmt = $conn->prepare("UPDATE technician SET nick=?, password=?, age=?, height=?, weight=?, size=?, model=?, city=?, hotel=?, workimg=?, lifeimg=?, video=?, status=?, apply_time=NOW(), reject_reason=NULL WHERE id=?");
    $stmt->bind_param('sssssssssssssi', $nick, $password, $age, $height, $weight, $size, $model, $city, $hotel, $workimg_path, $lifeimg_str, $video_path, $status, $existingRecord['id']);
    $ok = $stmt->execute();
} else {
    // 新用户申请，插入新记录
    $stmt = $conn->prepare("INSERT INTO technician (nick, phone, password, age, height, weight, size, model, city, hotel, workimg, lifeimg, video, status, apply_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
    $stmt->bind_param('ssssssssssssss', $nick, $phone, $password, $age, $height, $weight, $size, $model, $city, $hotel, $workimg_path, $lifeimg_str, $video_path, $status);
    $ok = $stmt->execute();
}
if ($ok) {
    echo json_encode(['success'=>true, 'msg'=>'提交成功，等待后台审核']);
} else {
    echo json_encode([
        'success'=>false,
        'msg'=>'保存失败，请重试',
        'error'=>$stmt->error
    ]);
}
$stmt->close();
$conn->close();
?>