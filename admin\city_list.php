<?php
// city_list.php
// 返回城市列表，格式：[{id,name,lng,lat,created_at,is_hot}]

// 禁用错误输出到浏览器，防止HTML错误信息污染JSON响应
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// 开启输出缓冲，确保只输出JSON
ob_start();

header('Content-Type: application/json; charset=utf-8');

// 注册致命错误处理器
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        echo json_encode([]);
        exit;
    }
});

try {
    require_once '../db.php';

    // 检查数据库连接
    if ($pdo === null) {
        ob_clean();
        echo json_encode([]);
        exit;
    }
} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    ob_clean();
    echo json_encode([]);
    exit;
}

// 获取城市名称的拼音首字母
function getCityPinyinFirstLetter($cityName) {
    // 移除"市"、"县"、"区"等后缀
    $cleanName = preg_replace('/[市县区]$/', '', $cityName);
    $firstChar = mb_substr($cleanName, 0, 1, 'UTF-8');

    // 中文字符到拼音首字母的映射表
    $pinyinMap = [
        '阿' => 'A', '安' => 'A', '鞍' => 'A', '澳' => 'A',
        '北' => 'B', '包' => 'B', '保' => 'B', '蚌' => 'B', '本' => 'B', '滨' => 'B', '亳' => 'B', '白' => 'B', '百' => 'B', '巴' => 'B', '博' => 'B',
        '重' => 'C', '成' => 'C', '长' => 'C', '常' => 'C', '承' => 'C', '沧' => 'C', '滁' => 'C', '池' => 'C', '潮' => 'C', '崇' => 'C', '赤' => 'C', '朝' => 'C', '昌' => 'C', '郴' => 'C', '楚' => 'C', '滨' => 'C', '慈' => 'C',
        '大' => 'D', '东' => 'D', '德' => 'D', '丹' => 'D', '定' => 'D', '都' => 'D', '儋' => 'D', '达' => 'D', '大' => 'D', '德' => 'D', '迪' => 'D', '敦' => 'D', '东' => 'D',
        '鄂' => 'E', '恩' => 'E', '额' => 'E',
        '福' => 'F', '佛' => 'F', '抚' => 'F', '阜' => 'F', '防' => 'F', '丰' => 'F', '凤' => 'F', '富' => 'F', '抚' => 'F',
        '广' => 'G', '贵' => 'G', '桂' => 'G', '赣' => 'G', '格' => 'G', '固' => 'G', '高' => 'G', '甘' => 'G', '广' => 'G', '贵' => 'G', '桂' => 'G',
        '杭' => 'H', '哈' => 'H', '海' => 'H', '合' => 'H', '河' => 'H', '衡' => 'H', '呼' => 'H', '黄' => 'H', '惠' => 'H', '湖' => 'H', '怀' => 'H', '邯' => 'H', '汉' => 'H', '菏' => 'H', '鹤' => 'H', '黑' => 'H', '红' => 'H', '淮' => 'H', '华' => 'H', '葫' => 'H', '呼' => 'H', '霍' => 'H',
        '济' => 'J', '金' => 'J', '江' => 'J', '嘉' => 'J', '九' => 'J', '吉' => 'J', '锦' => 'J', '焦' => 'J', '荆' => 'J', '景' => 'J', '揭' => 'J', '晋' => 'J', '佳' => 'J', '江' => 'J', '嘉' => 'J', '金' => 'J', '济' => 'J', '吉' => 'J', '鸡' => 'J', '集' => 'J',
        '昆' => 'K', '开' => 'K', '克' => 'K', '喀' => 'K', '凯' => 'K', '库' => 'K',
        '兰' => 'L', '拉' => 'L', '洛' => 'L', '连' => 'L', '柳' => 'L', '六' => 'L', '临' => 'L', '廊' => 'L', '聊' => 'L', '辽' => 'L', '丽' => 'L', '娄' => 'L', '漯' => 'L', '吕' => 'L', '龙' => 'L', '陇' => 'L', '林' => 'L', '丽' => 'L', '来' => 'L', '乐' => 'L', '丽' => 'L', '凉' => 'L', '六' => 'L', '柳' => 'L', '泸' => 'L', '陆' => 'L',
        '马' => 'M', '梅' => 'M', '绵' => 'M', '牡' => 'M', '茂' => 'M', '眉' => 'M', '马' => 'M', '满' => 'M', '绵' => 'M', '牡' => 'M', '茂' => 'M', '梅' => 'M', '眉' => 'M', '密' => 'M', '绵' => 'M',
        '南' => 'N', '宁' => 'N', '内' => 'N', '那' => 'N', '南' => 'N', '宁' => 'N', '怒' => 'N',
        '盘' => 'P', '平' => 'P', '莆' => 'P', '濮' => 'P', '攀' => 'P', '普' => 'P', '萍' => 'P', '平' => 'P', '蓬' => 'P', '普' => 'P',
        '青' => 'Q', '秦' => 'Q', '齐' => 'Q', '泉' => 'Q', '衢' => 'Q', '庆' => 'Q', '钦' => 'Q', '曲' => 'Q', '清' => 'Q', '琼' => 'Q', '齐' => 'Q', '秦' => 'Q', '青' => 'Q', '庆' => 'Q', '曲' => 'Q', '泉' => 'Q',
        '日' => 'R', '瑞' => 'R', '日' => 'R',
        '上' => 'S', '深' => 'S', '苏' => 'S', '沈' => 'S', '石' => 'S', '三' => 'S', '绍' => 'S', '汕' => 'S', '韶' => 'S', '商' => 'S', '十' => 'S', '随' => 'S', '宿' => 'S', '朔' => 'S', '双' => 'S', '松' => 'S', '遂' => 'S', '邵' => 'S', '神' => 'S', '山' => 'S', '汕' => 'S', '韶' => 'S', '绍' => 'S', '深' => 'S', '沈' => 'S', '石' => 'S', '苏' => 'S', '宿' => 'S', '随' => 'S', '三' => 'S', '上' => 'S',
        '天' => 'T', '太' => 'T', '唐' => 'T', '台' => 'T', '泰' => 'T', '通' => 'T', '铜' => 'T', '铁' => 'T', '塔' => 'T', '吐' => 'T', '图' => 'T', '天' => 'T', '太' => 'T', '台' => 'T', '泰' => 'T', '唐' => 'T', '天' => 'T', '铁' => 'T', '通' => 'T', '铜' => 'T', '图' => 'T', '吐' => 'T',
        '乌' => 'W', '无' => 'W', '武' => 'W', '温' => 'W', '威' => 'W', '潍' => 'W', '芜' => 'W', '梧' => 'W', '万' => 'W', '文' => 'W', '渭' => 'W', '乌' => 'W', '吴' => 'W', '五' => 'W', '武' => 'W', '无' => 'W', '芜' => 'W', '温' => 'W', '威' => 'W', '潍' => 'W', '梧' => 'W', '万' => 'W', '文' => 'W', '渭' => 'W', '乌' => 'W', '吴' => 'W', '五' => 'W',
        '西' => 'X', '厦' => 'X', '徐' => 'X', '新' => 'X', '襄' => 'X', '许' => 'X', '宣' => 'X', '咸' => 'X', '湘' => 'X', '孝' => 'X', '信' => 'X', '忻' => 'X', '邢' => 'X', '兴' => 'X', '锡' => 'X', '西' => 'X', '厦' => 'X', '徐' => 'X', '新' => 'X', '襄' => 'X', '许' => 'X', '宣' => 'X', '咸' => 'X', '湘' => 'X', '孝' => 'X', '信' => 'X', '忻' => 'X', '邢' => 'X', '兴' => 'X', '锡' => 'X', '西' => 'X',
        '银' => 'Y', '宜' => 'Y', '扬' => 'Y', '烟' => 'Y', '盐' => 'Y', '营' => 'Y', '岳' => 'Y', '运' => 'Y', '玉' => 'Y', '榆' => 'Y', '永' => 'Y', '益' => 'Y', '阳' => 'Y', '鹰' => 'Y', '伊' => 'Y', '延' => 'Y', '雅' => 'Y', '义' => 'Y', '银' => 'Y', '宜' => 'Y', '扬' => 'Y', '烟' => 'Y', '盐' => 'Y', '营' => 'Y', '岳' => 'Y', '运' => 'Y', '玉' => 'Y', '榆' => 'Y', '永' => 'Y', '益' => 'Y', '阳' => 'Y', '鹰' => 'Y', '伊' => 'Y', '延' => 'Y', '雅' => 'Y', '义' => 'Y',
        '郑' => 'Z', '珠' => 'Z', '中' => 'Z', '株' => 'Z', '淄' => 'Z', '枣' => 'Z', '张' => 'Z', '湛' => 'Z', '肇' => 'Z', '镇' => 'Z', '周' => 'Z', '驻' => 'Z', '舟' => 'Z', '漳' => 'Z', '遵' => 'Z', '资' => 'Z', '自' => 'Z', '昭' => 'Z', '郑' => 'Z', '珠' => 'Z', '中' => 'Z', '株' => 'Z', '淄' => 'Z', '枣' => 'Z', '张' => 'Z', '湛' => 'Z', '肇' => 'Z', '镇' => 'Z', '周' => 'Z', '驻' => 'Z', '舟' => 'Z', '漳' => 'Z', '遵' => 'Z', '资' => 'Z', '自' => 'Z', '昭' => 'Z'
    ];

    return isset($pinyinMap[$firstChar]) ? $pinyinMap[$firstChar] : 'Z';
}

if (!$pdo) {
    echo json_encode([]);
    exit;
}

// 创建城市表（如果不存在）
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS city (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        lng DECIMAL(10,6) DEFAULT NULL,
        lat DECIMAL(10,6) DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_hot TINYINT(1) DEFAULT 0
    ) CHARSET=utf8mb4");
} catch (Exception $e) {
    // 表已存在或创建失败，继续执行
}

// 检查是否有城市数据，如果没有则插入默认数据
$checkStmt = $pdo->query("SELECT COUNT(*) FROM city");
$count = $checkStmt ? $checkStmt->fetchColumn() : 0;

if ($count == 0) {
    // 插入默认城市数据
    $defaultCities = [
        ['三亚市', 109.508268, 18.247872, 1],
        ['海口市', 110.33119, 20.031971, 1],
        ['儋州市', 109.576782, 19.517486, 0],
        ['琼海市', 110.466785, 19.246011, 0],
        ['文昌市', 110.753677, 19.612986, 0],
        ['万宁市', 110.388793, 18.796216, 0],
        ['东方市', 108.653789, 19.10198, 0],
        ['五指山市', 109.516662, 18.776921, 0]
    ];

    $insertStmt = $pdo->prepare("INSERT INTO city (name, lng, lat, is_hot) VALUES (?, ?, ?, ?)");
    foreach ($defaultCities as $city) {
        $insertStmt->execute($city);
    }
}

// 查询城市列表
$sql = "SELECT id, name, lng, lat, created_at, is_hot FROM city";
$stmt = $pdo->query($sql);
$cities = [];
if ($stmt) {
    foreach ($stmt as $row) {
        $cities[] = [
            'id' => $row['id'],
            'name' => $row['name'],
            'lng' => $row['lng'],
            'lat' => $row['lat'],
            'created_at' => $row['created_at'],
            'is_hot' => $row['is_hot'] ? true : false,
            'pinyin_first' => getCityPinyinFirstLetter($row['name'])
        ];
    }
}

// 按拼音首字母排序
usort($cities, function($a, $b) {
    // 首先按热门状态排序（热门在前）
    if ($a['is_hot'] != $b['is_hot']) {
        return $b['is_hot'] - $a['is_hot'];
    }

    // 然后按拼音首字母排序
    if ($a['pinyin_first'] != $b['pinyin_first']) {
        return strcmp($a['pinyin_first'], $b['pinyin_first']);
    }

    // 最后按城市名称排序
    return strcmp($a['name'], $b['name']);
});

// 输出JSON
ob_clean();
echo json_encode($cities);
