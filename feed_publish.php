<?php
// feed_publish.php
header('Content-Type: application/json; charset=utf-8');
require_once 'db.php';

// 登录校验（示例：用手机号识别用户）
$phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
$content = isset($_POST['content']) ? trim($_POST['content']) : '';
if (!$phone || !$content) {
    echo json_encode(['success' => false, 'msg' => '缺少必要参数']);
    exit;
}
// 查找用户ID
$stmt = $pdo->prepare('SELECT id FROM user WHERE phone = ? LIMIT 1');
$stmt->execute([$phone]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);
if (!$user) {
    echo json_encode(['success' => false, 'msg' => '用户不存在']);
    exit;
}
$user_id = $user['id'];

// 处理图片上传
$imgPaths = [];
if (!empty($_FILES['images']) && isset($_FILES['images']['error']) && is_array($_FILES['images']['error'])) {
    $files = $_FILES['images'];
    $count = is_array($files['name']) ? count($files['name']) : 0;
    for ($i = 0; $i < $count; $i++) {
        if (isset($files['error'][$i]) && $files['error'][$i] === UPLOAD_ERR_OK) {
            $ext = pathinfo($files['name'][$i], PATHINFO_EXTENSION);
            $newName = 'feed_' . date('YmdHis') . '_' . rand(1000,9999) . '.' . $ext;
            $target = 'uploads/' . $newName;
            if (move_uploaded_file($files['tmp_name'][$i], $target)) {
                $imgPaths[] = $target;
            }
        }
    }
}
$images = $imgPaths ? json_encode($imgPaths, JSON_UNESCAPED_UNICODE) : '';

// 保存动态
$stmt = $pdo->prepare('INSERT INTO feed (user_id, content, images) VALUES (?, ?, ?)');
$res = $stmt->execute([$user_id, $content, $images]);
if ($res) {
    echo json_encode(['success' => true, 'msg' => '发布成功']);
} else {
    echo json_encode(['success' => false, 'msg' => '发布失败']);
}
