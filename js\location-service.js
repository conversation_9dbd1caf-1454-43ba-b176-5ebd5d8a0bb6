// location-service.js - 位置服务模块
class LocationService {
    constructor() {
        this.userLocation = null;
        this.userCity = null;
        this.isLocationRequested = false;
        this.locationCallbacks = [];
        
        // 从localStorage恢复位置信息
        this.loadStoredLocation();
    }

    // 获取用户位置
    async getUserLocation(options = {}) {
        const defaultOptions = {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000, // 5分钟缓存
            showPrompt: true,
            fallbackToIP: true
        };
        
        const config = { ...defaultOptions, ...options };
        
        // 如果已经有位置信息且在缓存时间内，直接返回
        if (this.userLocation && this.isLocationFresh()) {
            return this.userLocation;
        }

        try {
            // 检查浏览器是否支持地理位置API
            if (!navigator.geolocation) {
                throw new Error('浏览器不支持地理位置功能');
            }

            // 显示获取位置的提示
            if (config.showPrompt) {
                this.showLocationPrompt();
            }

            // 获取地理位置
            const position = await this.getCurrentPosition(config);
            
            this.userLocation = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                accuracy: position.coords.accuracy,
                timestamp: Date.now()
            };

            // 根据坐标获取城市信息
            await this.getCityFromCoordinates(this.userLocation.latitude, this.userLocation.longitude);
            
            // 保存到localStorage
            this.saveLocationToStorage();
            
            // 隐藏提示
            this.hideLocationPrompt();
            
            // 触发回调
            this.triggerLocationCallbacks(this.userLocation, this.userCity);
            
            return this.userLocation;
            
        } catch (error) {
            console.warn('获取精确位置失败:', error.message);
            
            // 如果启用IP定位回退
            if (config.fallbackToIP) {
                try {
                    await this.getLocationByIP();
                    this.hideLocationPrompt();
                    this.triggerLocationCallbacks(this.userLocation, this.userCity);
                    return this.userLocation;
                } catch (ipError) {
                    console.warn('IP定位也失败:', ipError.message);
                }
            }
            
            this.hideLocationPrompt();
            this.handleLocationError(error);
            throw error;
        }
    }

    // 获取当前位置的Promise封装
    getCurrentPosition(options) {
        return new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(
                resolve,
                reject,
                {
                    enableHighAccuracy: options.enableHighAccuracy,
                    timeout: options.timeout,
                    maximumAge: options.maximumAge
                }
            );
        });
    }

    // 通过IP获取大概位置
    async getLocationByIP() {
        try {
            // 使用免费的IP定位服务
            const response = await fetch('https://ipapi.co/json/');
            const data = await response.json();
            
            if (data.latitude && data.longitude) {
                this.userLocation = {
                    latitude: data.latitude,
                    longitude: data.longitude,
                    accuracy: 10000, // IP定位精度较低
                    timestamp: Date.now(),
                    source: 'ip'
                };
                
                this.userCity = {
                    name: data.city + '市',
                    province: data.region,
                    country: data.country_name,
                    source: 'ip'
                };
                
                this.saveLocationToStorage();
                return this.userLocation;
            } else {
                throw new Error('IP定位服务返回无效数据');
            }
        } catch (error) {
            // 如果外部服务失败，使用备用方案
            console.warn('IP定位失败，使用默认城市');
            this.userCity = {
                name: '全部城市',
                source: 'default'
            };
            throw error;
        }
    }

    // 根据坐标获取城市信息（逆地理编码）
    async getCityFromCoordinates(lat, lng) {
        try {
            // 使用高德地图API进行逆地理编码
            const response = await fetch(`https://restapi.amap.com/v3/geocode/regeo?location=${lng},${lat}&key=您的高德地图API密钥&radius=1000&extensions=base`);
            const data = await response.json();
            
            if (data.status === '1' && data.regeocode) {
                const addressComponent = data.regeocode.addressComponent;
                this.userCity = {
                    name: addressComponent.city || addressComponent.district + '市',
                    province: addressComponent.province,
                    district: addressComponent.district,
                    source: 'amap'
                };
            } else {
                throw new Error('逆地理编码失败');
            }
        } catch (error) {
            console.warn('逆地理编码失败，尝试简单映射:', error.message);
            // 使用简单的坐标到城市映射
            this.userCity = this.getCityByCoordinateRange(lat, lng);
        }
    }

    // 简单的坐标范围到城市映射
    getCityByCoordinateRange(lat, lng) {
        // 主要城市的大概坐标范围
        const cityRanges = [
            { name: '北京市', lat: [39.4, 41.0], lng: [115.7, 117.4] },
            { name: '上海市', lat: [30.7, 31.9], lng: [120.9, 122.0] },
            { name: '广州市', lat: [22.8, 23.6], lng: [113.0, 113.8] },
            { name: '深圳市', lat: [22.4, 22.8], lng: [113.8, 114.8] },
            { name: '杭州市', lat: [29.8, 30.6], lng: [119.7, 120.7] },
            { name: '南京市', lat: [31.8, 32.4], lng: [118.4, 119.2] },
            { name: '武汉市', lat: [30.1, 31.0], lng: [113.7, 115.0] },
            { name: '成都市', lat: [30.1, 31.4], lng: [103.5, 104.9] },
            { name: '西安市', lat: [33.8, 34.8], lng: [108.0, 109.8] },
            { name: '重庆市', lat: [28.8, 32.2], lng: [105.3, 110.5] },
            { name: '天津市', lat: [38.5, 40.3], lng: [116.8, 118.0] },
            { name: '青岛市', lat: [35.8, 36.5], lng: [119.8, 121.0] },
            { name: '大连市', lat: [38.8, 39.2], lng: [121.0, 122.2] },
            { name: '厦门市', lat: [24.2, 24.7], lng: [117.8, 118.5] },
            { name: '苏州市', lat: [31.0, 31.6], lng: [120.3, 121.2] },
            { name: '无锡市', lat: [31.2, 31.8], lng: [119.8, 120.8] },
            { name: '宁波市', lat: [29.5, 30.1], lng: [121.0, 122.0] }
        ];

        for (const city of cityRanges) {
            if (lat >= city.lat[0] && lat <= city.lat[1] && 
                lng >= city.lng[0] && lng <= city.lng[1]) {
                return {
                    name: city.name,
                    source: 'coordinate_range'
                };
            }
        }

        // 如果没有匹配到，返回默认
        return {
            name: '全部城市',
            source: 'default'
        };
    }

    // 显示位置获取提示
    showLocationPrompt() {
        // 避免重复显示
        if (document.getElementById('location-prompt')) return;

        const prompt = document.createElement('div');
        prompt.id = 'location-prompt';
        prompt.style.cssText = `
            position: fixed; top: 20px; left: 50%; transform: translateX(-50%);
            background: rgba(0, 198, 162, 0.95); color: white; padding: 12px 20px;
            border-radius: 25px; z-index: 10000; font-size: 14px;
            box-shadow: 0 4px 12px rgba(0, 198, 162, 0.3);
            display: flex; align-items: center; gap: 8px;
            animation: slideDown 0.3s ease;
        `;
        
        prompt.innerHTML = `
            <div style="width: 16px; height: 16px; border: 2px solid white; border-top: 2px solid transparent; border-radius: 50%; animation: spin 1s linear infinite;"></div>
            正在获取您的位置信息...
        `;

        // 添加动画样式
        if (!document.getElementById('location-prompt-styles')) {
            const style = document.createElement('style');
            style.id = 'location-prompt-styles';
            style.textContent = `
                @keyframes slideDown {
                    from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                    to { transform: translateX(-50%) translateY(0); opacity: 1; }
                }
                @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(prompt);
    }

    // 隐藏位置获取提示
    hideLocationPrompt() {
        const prompt = document.getElementById('location-prompt');
        if (prompt) {
            prompt.style.animation = 'slideDown 0.3s ease reverse';
            setTimeout(() => prompt.remove(), 300);
        }
    }

    // 处理位置获取错误
    handleLocationError(error) {
        let message = '无法获取位置信息';
        
        switch (error.code) {
            case error.PERMISSION_DENIED:
                message = '位置访问被拒绝，将使用默认城市';
                break;
            case error.POSITION_UNAVAILABLE:
                message = '位置信息不可用，将使用默认城市';
                break;
            case error.TIMEOUT:
                message = '位置获取超时，将使用默认城市';
                break;
        }

        console.warn(message, error);
        
        // 设置默认城市
        this.userCity = {
            name: '全部城市',
            source: 'error_fallback'
        };
    }

    // 检查位置信息是否新鲜
    isLocationFresh() {
        if (!this.userLocation || !this.userLocation.timestamp) return false;
        const fiveMinutes = 5 * 60 * 1000;
        return (Date.now() - this.userLocation.timestamp) < fiveMinutes;
    }

    // 保存位置信息到localStorage
    saveLocationToStorage() {
        try {
            const locationData = {
                location: this.userLocation,
                city: this.userCity,
                timestamp: Date.now()
            };
            localStorage.setItem('userLocationData', JSON.stringify(locationData));
        } catch (error) {
            console.warn('保存位置信息失败:', error);
        }
    }

    // 从localStorage加载位置信息
    loadStoredLocation() {
        try {
            const stored = localStorage.getItem('userLocationData');
            if (stored) {
                const data = JSON.parse(stored);
                // 检查数据是否过期（24小时）
                const oneDay = 24 * 60 * 60 * 1000;
                if (data.timestamp && (Date.now() - data.timestamp) < oneDay) {
                    this.userLocation = data.location;
                    this.userCity = data.city;
                }
            }
        } catch (error) {
            console.warn('加载存储的位置信息失败:', error);
        }
    }

    // 添加位置变化回调
    onLocationUpdate(callback) {
        this.locationCallbacks.push(callback);
    }

    // 触发位置回调
    triggerLocationCallbacks(location, city) {
        this.locationCallbacks.forEach(callback => {
            try {
                callback(location, city);
            } catch (error) {
                console.error('位置回调执行失败:', error);
            }
        });
    }

    // 获取当前城市
    getCurrentCity() {
        return this.userCity;
    }

    // 获取当前位置
    getCurrentLocation() {
        return this.userLocation;
    }

    // 清除位置信息
    clearLocation() {
        this.userLocation = null;
        this.userCity = null;
        localStorage.removeItem('userLocationData');
    }
}

// 创建全局位置服务实例
window.LocationService = new LocationService();

// 自动初始化位置服务
document.addEventListener('DOMContentLoaded', function() {
    // 延迟1秒后自动获取位置，避免影响页面加载
    setTimeout(() => {
        LocationService.getUserLocation({
            showPrompt: true,
            fallbackToIP: true
        }).catch(error => {
            console.log('自动位置获取失败，用户可手动选择城市');
        });
    }, 1000);
});
