<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技师页面标题居中修改验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }
        .comparison-item h4 {
            margin-top: 0;
            color: #333;
        }
        .comparison-item.before {
            border-left: 4px solid #dc3545;
        }
        .comparison-item.after {
            border-left: 4px solid #28a745;
        }
        .demo-header {
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            margin: 10px 0;
            position: relative;
        }
        .demo-header.before {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .demo-header.after {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .demo-city-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 500;
        }
        .demo-title {
            font-size: 20px;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        .demo-title.center {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }
        .changes-list {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .changes-list h4 {
            margin-top: 0;
            color: #333;
        }
        .changes-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .changes-list li {
            margin: 8px 0;
            line-height: 1.4;
        }
        .highlight {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .highlight strong {
            color: #856404;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .code-block.before {
            border-left: 4px solid #dc3545;
        }
        .code-block.after {
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📍 技师页面标题居中修改完成</h1>
        
        <div class="section">
            <h3>🎯 修改内容</h3>
            <p>已成功将technicians.html页面的标题"技师列表"从左上角移动到页面中间位置。</p>
            
            <div class="highlight">
                <strong>✅ 修改内容：</strong><br>
                • 在顶部导航中添加居中的"技师列表"标题<br>
                • 使用绝对定位将标题居中显示<br>
                • 保持左侧城市选择器功能不变<br>
                • 优化页面视觉平衡
            </div>
        </div>
        
        <div class="section">
            <h3>📊 修改前后对比</h3>
            
            <div class="comparison">
                <div class="comparison-item before">
                    <h4>❌ 修改前</h4>
                    <div class="demo-header before">
                        <div class="demo-city-selector">
                            📍 三亚市 ▼
                        </div>
                        <div style="flex: 1;"></div>
                    </div>
                    <p style="font-size: 14px; color: #666; margin-top: 10px;">
                        只有左侧的城市选择器，没有明显的页面标题
                    </p>
                </div>
                
                <div class="comparison-item after">
                    <h4>✅ 修改后</h4>
                    <div class="demo-header after">
                        <div class="demo-city-selector">
                            📍 三亚市 ▼
                        </div>
                        <div class="demo-title center">技师列表</div>
                        <div style="flex: 1;"></div>
                    </div>
                    <p style="font-size: 14px; color: #666; margin-top: 10px;">
                        左侧城市选择器，中间显示"技师列表"标题
                    </p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>🔧 技术实现详情</h3>
            
            <div class="changes-list">
                <h4>HTML结构修改</h4>
                <div class="code-block after">
// 在top-nav中添加标题元素
&lt;div class="top-nav"&gt;
    &lt;div style="display: flex; align-items: center; position: relative;"&gt;
        &lt;!-- 城市选择器 --&gt;
        &lt;div class="city-selector" onclick="showCitySelector()"&gt;...&lt;/div&gt;
        &lt;!-- 城市提示 --&gt;
        &lt;div id="city-tip"&gt;...&lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div class="page-title"&gt;技师列表&lt;/div&gt;  &lt;!-- 新增标题 --&gt;
&lt;/div&gt;
                </div>
            </div>
            
            <div class="changes-list">
                <h4>CSS样式修改</h4>
                <div class="code-block after">
// 为top-nav添加相对定位
.top-nav {
    position: relative;  /* 新增 */
}

// 新增页面标题样式
.page-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 20px;
    font-weight: bold;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>🎮 查看效果</h3>
            <div style="margin: 15px 0;">
                <button class="btn success" onclick="openTechniciansPage()">👥 查看技师页面</button>
                <button class="btn" onclick="showLayoutDetails()">📐 查看布局详情</button>
            </div>
            
            <div id="result">
                <div class="info">点击"查看技师页面"验证标题居中效果</div>
            </div>
        </div>
        
        <div class="section">
            <h3>📋 修改总结</h3>
            
            <div class="warning">
                <strong>🎯 修改目标达成：</strong><br>
                ✅ 标题从左上角移到中间 - 已完成<br>
                ✅ 保持城市选择器功能 - 已完成<br>
                ✅ 优化页面视觉平衡 - 已完成<br>
                ✅ 保持响应式布局 - 已完成<br><br>
                
                <strong>🔍 布局特点：</strong><br>
                • 左侧：城市选择器和提示<br>
                • 中间：页面标题"技师列表"<br>
                • 右侧：空白区域（保持平衡）<br><br>
                
                <strong>📱 视觉效果：</strong><br>
                • 标题更加突出和醒目<br>
                • 页面布局更加对称<br>
                • 用户能清楚知道当前页面功能<br>
                • 整体设计更加专业
            </div>
        </div>
    </div>

    <script>
        // 打开技师页面
        function openTechniciansPage() {
            window.open('technicians.html', '_blank');
            
            document.getElementById('result').innerHTML = `
                <div class="success">
                    👥 已打开技师页面<br><br>
                    
                    <strong>验证要点：</strong><br>
                    1. 查看页面顶部是否显示居中的"技师列表"标题<br>
                    2. 确认左侧城市选择器功能正常<br>
                    3. 验证标题位置是否在页面中央<br>
                    4. 检查整体布局是否美观平衡<br><br>
                    
                    <strong>预期结果：</strong><br>
                    • 页面顶部中央显示"技师列表"标题<br>
                    • 左侧城市选择器正常工作<br>
                    • 标题字体清晰，有阴影效果<br>
                    • 整体布局对称美观
                </div>
            `;
        }
        
        // 显示布局详情
        function showLayoutDetails() {
            document.getElementById('result').innerHTML = `
                <div class="info">
                    📐 <strong>页面布局详情</strong><br><br>
                    
                    <strong>顶部导航布局：</strong><br>
                    • 容器：flex布局，justify-content: space-between<br>
                    • 左侧：城市选择器 + 提示信息<br>
                    • 中间：绝对定位的页面标题<br>
                    • 右侧：空白区域（自动填充）<br><br>
                    
                    <strong>标题定位方式：</strong><br>
                    • position: absolute - 绝对定位<br>
                    • left: 50% - 左边距离容器50%<br>
                    • transform: translateX(-50%) - 向左偏移自身宽度50%<br>
                    • 结果：标题精确居中<br><br>
                    
                    <strong>样式特点：</strong><br>
                    • 字体大小：20px<br>
                    • 字体粗细：bold<br>
                    • 颜色：白色<br>
                    • 阴影：0 1px 2px rgba(0, 0, 0, 0.1)<br><br>
                    
                    <strong>响应式考虑：</strong><br>
                    • 标题始终居中，不受左侧内容影响<br>
                    • 在不同屏幕尺寸下保持居中效果<br>
                    • 与背景渐变色形成良好对比
                </div>
            `;
        }
        
        // 页面加载时显示完成信息
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                document.getElementById('result').innerHTML = `
                    <div class="success">
                        🎉 技师页面标题居中修改完成！<br><br>
                        
                        <strong>修改内容：</strong><br>
                        ✅ 添加了居中的"技师列表"标题<br>
                        ✅ 使用绝对定位实现精确居中<br>
                        ✅ 保持了左侧城市选择器功能<br>
                        ✅ 优化了页面视觉平衡<br><br>
                        
                        现在可以查看居中显示的页面标题了！
                    </div>
                `;
            }, 1000);
        });
    </script>
</body>
</html>
