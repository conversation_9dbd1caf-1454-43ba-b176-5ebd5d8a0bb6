<?php
// feed_dislike.php
header('Content-Type: application/json; charset=utf-8');
require_once 'db.php';

$user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
$feed_id = isset($_POST['feed_id']) ? intval($_POST['feed_id']) : 0;
$type = isset($_POST['type']) ? trim($_POST['type']) : '';
if (!$user_id || (!$feed_id && $type!=='user')) {
    echo json_encode(['success'=>false, 'msg'=>'缺少参数']);
    exit;
}
// type=user 表示不喜欢此人动态，否则不喜欢此条动态
if ($type==='user') {
    // 可记录到 feed_dislike_user(user_id, dislike_user_id, created_at)
    echo json_encode(['success'=>true, 'msg'=>'已屏蔽该用户动态']);
} else {
    // 可记录到 feed_dislike(feed_id, user_id, created_at)
    echo json_encode(['success'=>true, 'msg'=>'已屏蔽该条动态']);
}
