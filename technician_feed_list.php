<?php
// technician_feed_list.php - 获取技师动态列表
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'db.php';

try {
    // 创建技师动态表（如果不存在）
    $createTableSql = "CREATE TABLE IF NOT EXISTS technician_feeds (
        id INT AUTO_INCREMENT PRIMARY KEY,
        technician_id INT NOT NULL,
        technician_name VARCHAR(100) NOT NULL,
        age INT DEFAULT NULL,
        height INT DEFAULT NULL,
        weight INT DEFAULT NULL,
        service_years INT DEFAULT NULL,
        city VARCHAR(100) NOT NULL,
        previous_city VARCHAR(100) DEFAULT NULL,
        action_type ENUM('join', 'move') NOT NULL DEFAULT 'join',
        content TEXT,
        avatar VARCHAR(255) DEFAULT 'images/tx.jpg',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        is_active TINYINT(1) DEFAULT 1,
        INDEX idx_technician_id (technician_id),
        INDEX idx_city (city),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $pdo->exec($createTableSql);

    // 检查是否有数据，没有则插入示例数据
    $checkStmt = $pdo->query("SELECT COUNT(*) FROM technician_feeds");
    $count = $checkStmt->fetchColumn();

    if ($count == 0) {
        // 插入示例技师动态数据
        $sampleFeeds = [
            [
                'technician_id' => 1,
                'technician_name' => '李大宝',
                'age' => 22,
                'height' => 175,
                'weight' => 60,
                'service_years' => 16,
                'city' => '三亚市',
                'action_type' => 'join',
                'content' => '技师李大宝 22/175/60/16 到三亚了'
            ],
            [
                'technician_id' => 2,
                'technician_name' => '王小明',
                'age' => 25,
                'height' => 180,
                'weight' => 70,
                'service_years' => 20,
                'city' => '海口市',
                'action_type' => 'join',
                'content' => '技师王小明 25/180/70/20 到海口了'
            ],
            [
                'technician_id' => 1,
                'technician_name' => '李大宝',
                'age' => 22,
                'height' => 175,
                'weight' => 60,
                'service_years' => 16,
                'city' => '海口市',
                'previous_city' => '三亚市',
                'action_type' => 'move',
                'content' => '技师李大宝 22/175/60/16 到海口了'
            ],
            [
                'technician_id' => 3,
                'technician_name' => '张伟',
                'age' => 28,
                'height' => 178,
                'weight' => 65,
                'service_years' => 25,
                'city' => '儋州市',
                'action_type' => 'join',
                'content' => '技师张伟 28/178/65/25 到儋州了'
            ],
            [
                'technician_id' => 4,
                'technician_name' => '刘强',
                'age' => 24,
                'height' => 172,
                'weight' => 58,
                'service_years' => 18,
                'city' => '琼海市',
                'action_type' => 'join',
                'content' => '技师刘强 24/172/58/18 到琼海了'
            ],
            [
                'technician_id' => 2,
                'technician_name' => '王小明',
                'age' => 25,
                'height' => 180,
                'weight' => 70,
                'service_years' => 20,
                'city' => '三亚市',
                'previous_city' => '海口市',
                'action_type' => 'move',
                'content' => '技师王小明 25/180/70/20 到三亚了'
            ]
        ];

        $insertStmt = $pdo->prepare("
            INSERT INTO technician_feeds
            (technician_id, technician_name, age, height, weight, service_years, city, previous_city, action_type, content)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($sampleFeeds as $feed) {
            $insertStmt->execute([
                $feed['technician_id'],
                $feed['technician_name'],
                $feed['age'],
                $feed['height'],
                $feed['weight'],
                $feed['service_years'],
                $feed['city'],
                $feed['previous_city'] ?? null,
                $feed['action_type'],
                $feed['content']
            ]);
        }
    }

    // 获取分页参数
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(50, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;

    // 查询技师动态列表（按创建时间倒序，最新的在上面）
    $sql = "SELECT * FROM technician_feeds
            WHERE is_active = 1
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$limit, $offset]);
    $feeds = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 格式化动态数据，符合前端期望的格式
    $formattedFeeds = [];
    foreach ($feeds as $feed) {
        $formattedFeed = [
            'id' => $feed['id'],
            'type' => 'technician_activity',
            'nickname' => $feed['technician_name'],
            'avatar' => $feed['avatar'],
            'content' => $feed['content'],
            'location' => $feed['city'],
            'time' => formatTime($feed['created_at']),
            'created_at' => $feed['created_at'],
            'likes' => 0, // 技师动态暂不支持点赞
            'user_id' => 'technician_' . $feed['technician_id'],
            'images' => [], // 技师动态不包含图片
            'technician_info' => [
                'id' => $feed['technician_id'],
                'name' => $feed['technician_name'],
                'age' => $feed['age'],
                'height' => $feed['height'],
                'weight' => $feed['weight'],
                'service_years' => $feed['service_years'],
                'city' => $feed['city'],
                'previous_city' => $feed['previous_city'],
                'action_type' => $feed['action_type']
            ]
        ];

        $formattedFeeds[] = $formattedFeed;
    }

    // 查询总数
    $countStmt = $pdo->query("SELECT COUNT(*) FROM technician_feeds WHERE is_active = 1");
    $total = $countStmt->fetchColumn();

    // 返回结果
    echo json_encode([
        'success' => true,
        'feeds' => $formattedFeeds,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => intval($total),
            'pages' => ceil($total / $limit),
            'has_more' => ($page * $limit) < $total
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'feeds' => []
    ]);
}

// 格式化时间显示
function formatTime($datetime) {
    $time = strtotime($datetime);
    $now = time();
    $diff = $now - $time;

    if ($diff < 60) {
        return '刚刚';
    } elseif ($diff < 3600) {
        return floor($diff / 60) . '分钟前';
    } elseif ($diff < 86400) {
        return floor($diff / 3600) . '小时前';
    } elseif ($diff < 2592000) {
        return floor($diff / 86400) . '天前';
    } else {
        return date('Y/m/d', $time);
    }
}
?>
