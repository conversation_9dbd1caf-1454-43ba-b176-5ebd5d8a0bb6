<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复动态显示问题</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .feed-preview {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .feed-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .feed-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
        }
        .feed-name {
            font-weight: bold;
            color: #333;
        }
        .feed-time {
            font-size: 12px;
            color: #999;
        }
        .feed-content {
            color: #333;
            margin: 10px 0;
        }
        .feed-details {
            background: #e3f2fd;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 修复动态显示问题</h1>
        
        <div class="section">
            <h3>📋 问题诊断</h3>
            <p>检查技师动态在feed.html页面不显示的原因</p>
            
            <div style="margin: 15px 0;">
                <button class="btn" onclick="checkAPI()">🔍 1. 检查API</button>
                <button class="btn" onclick="checkData()">📊 2. 检查数据</button>
                <button class="btn" onclick="testRender()">🎨 3. 测试渲染</button>
                <button class="btn success" onclick="fixAndTest()">🚀 4. 修复并测试</button>
            </div>
            
            <div id="diagnosis-result"></div>
        </div>
        
        <div class="section">
            <h3>🐛 调试日志</h3>
            <div id="debug-log" class="debug-log">
                等待诊断开始...
            </div>
        </div>
        
        <div class="section">
            <h3>📱 动态预览</h3>
            <div id="feed-preview">
                <div class="info">点击"测试渲染"查看动态预览</div>
            </div>
        </div>
        
        <div class="section">
            <h3>🔧 快速修复</h3>
            <div style="margin: 15px 0;">
                <button class="btn success" onclick="initializeData()">📥 初始化数据</button>
                <button class="btn" onclick="addTestFeed()">➕ 添加测试动态</button>
                <button class="btn danger" onclick="clearAllFeeds()">🗑️ 清空所有动态</button>
            </div>
            <div id="fix-result"></div>
        </div>
    </div>

    <script>
        let debugLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            debugLog.push(logEntry);
            
            const logContainer = document.getElementById('debug-log');
            logContainer.textContent = debugLog.join('\n');
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(logEntry);
        }
        
        // 检查API
        async function checkAPI() {
            log('开始检查API状态...');
            const resultContainer = document.getElementById('diagnosis-result');
            
            try {
                const response = await fetch('technician_feed_list.php');
                log(`API响应状态: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`API数据: ${JSON.stringify(data, null, 2)}`);
                    
                    resultContainer.innerHTML = `
                        <div class="success">
                            ✅ API检查通过<br>
                            • 响应状态: ${response.status}<br>
                            • 数据格式: ${data.success ? '正确' : '错误'}<br>
                            • 动态数量: ${data.feeds ? data.feeds.length : 0}
                        </div>
                    `;
                    return data;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`API检查失败: ${error.message}`, 'error');
                resultContainer.innerHTML = `
                    <div class="error">❌ API检查失败: ${error.message}</div>
                `;
                return null;
            }
        }
        
        // 检查数据
        async function checkData() {
            log('开始检查数据...');
            const data = await checkAPI();
            
            if (data && data.feeds && data.feeds.length > 0) {
                log(`发现 ${data.feeds.length} 条动态数据`);
                data.feeds.forEach((feed, index) => {
                    log(`动态 ${index + 1}: ${feed.content}`);
                });
                return data.feeds;
            } else {
                log('没有找到动态数据', 'warning');
                return [];
            }
        }
        
        // 测试渲染
        async function testRender() {
            log('开始测试渲染...');
            const feeds = await checkData();
            const previewContainer = document.getElementById('feed-preview');
            
            if (feeds.length > 0) {
                log('开始渲染动态预览');
                previewContainer.innerHTML = feeds.map(feed => `
                    <div class="feed-preview">
                        <div class="feed-header">
                            <img src="${feed.avatar || 'images/tx.jpg'}" alt="头像" class="feed-avatar" onerror="this.style.background='#ddd'">
                            <div>
                                <div class="feed-name">${feed.nickname || '技师'}</div>
                                <div class="feed-time">${feed.time || ''} · ${feed.location || ''}</div>
                            </div>
                        </div>
                        <div class="feed-content">${feed.content || ''}</div>
                        ${feed.technician_info ? `
                            <div class="feed-details">
                                <strong>${feed.technician_info.action_type === 'move' ? '更换城市' : '新加入'}</strong> 
                                ${feed.technician_info.city}
                                ${feed.technician_info.previous_city ? ` (从${feed.technician_info.previous_city})` : ''}<br>
                                年龄:${feed.technician_info.age} | 身高:${feed.technician_info.height}cm | 
                                体重:${feed.technician_info.weight}kg | 服务年限:${feed.technician_info.service_years}年
                            </div>
                        ` : ''}
                    </div>
                `).join('');
                log('动态预览渲染完成');
            } else {
                previewContainer.innerHTML = '<div class="info">没有动态数据可预览</div>';
                log('没有动态数据可预览');
            }
        }
        
        // 修复并测试
        async function fixAndTest() {
            log('开始修复并测试...');
            
            // 1. 初始化数据
            await initializeData();
            
            // 2. 检查API
            const data = await checkAPI();
            
            // 3. 测试渲染
            await testRender();
            
            // 4. 打开feed.html页面
            log('打开feed.html页面进行最终测试');
            window.open('feed.html', '_blank');
            
            document.getElementById('diagnosis-result').innerHTML = `
                <div class="success">
                    ✅ 修复完成！<br>
                    • 数据已初始化<br>
                    • API检查通过<br>
                    • 渲染测试成功<br>
                    • 已打开feed.html页面进行验证
                </div>
            `;
        }
        
        // 初始化数据
        async function initializeData() {
            log('开始初始化数据...');
            
            try {
                const response = await fetch('init_technician_feeds.php');
                const result = await response.json();
                log(`初始化结果: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    document.getElementById('fix-result').innerHTML = `
                        <div class="success">
                            ✅ 数据初始化成功<br>
                            • 现有记录: ${result.existing_records}<br>
                            • 活跃记录: ${result.active_records}<br>
                            ${result.sample_data_inserted ? `• 新增示例数据: ${result.sample_data_inserted}条` : ''}
                        </div>
                    `;
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error');
                document.getElementById('fix-result').innerHTML = `
                    <div class="error">❌ 初始化失败: ${error.message}</div>
                `;
            }
        }
        
        // 添加测试动态
        async function addTestFeed() {
            log('添加测试动态...');
            
            try {
                const testData = {
                    technician_id: Math.floor(Math.random() * 1000),
                    technician_name: '测试技师' + Math.floor(Math.random() * 100),
                    age: 25,
                    height: 180,
                    weight: 70,
                    service_years: 20,
                    city: '测试城市',
                    action_type: 'join'
                };
                
                const response = await fetch('add_technician_feed.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                log(`添加测试动态结果: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    document.getElementById('fix-result').innerHTML = `
                        <div class="success">✅ 测试动态添加成功: ${result.data.content}</div>
                    `;
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                log(`添加测试动态失败: ${error.message}`, 'error');
                document.getElementById('fix-result').innerHTML = `
                    <div class="error">❌ 添加失败: ${error.message}</div>
                `;
            }
        }
        
        // 清空所有动态
        function clearAllFeeds() {
            if (confirm('确定要清空所有技师动态吗？此操作不可恢复！')) {
                alert('清空功能需要在后台实现，请手动清理数据库');
            }
        }
        
        // 页面加载时自动开始诊断
        document.addEventListener('DOMContentLoaded', function() {
            log('修复页面加载完成');
            setTimeout(() => {
                log('自动开始诊断...');
                checkAPI();
            }, 1000);
        });
    </script>
</body>
</html>
