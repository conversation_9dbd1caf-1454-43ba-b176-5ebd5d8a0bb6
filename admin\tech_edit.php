<?php
// tech_edit_new.php - 编辑技师资料（重写版）

// 错误处理设置
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// 增加内存和执行时间限制，处理多文件上传
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300);

// 输出缓冲
ob_start();

// 错误处理函数
function sendJsonError($message) {
    ob_clean();
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['success' => false, 'msg' => $message]);
    exit;
}

// 成功响应函数
function sendJsonResponse($success, $message, $data = null) {
    ob_clean();
    header('Content-Type: application/json; charset=utf-8');
    $response = ['success' => $success, 'msg' => $message];
    if ($data !== null) {
        $response['data'] = $data;
    }
    echo json_encode($response);
    exit;
}

function sendJsonSuccess($message) {
    ob_clean();
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['success' => true, 'msg' => $message]);
    exit;
}

// 设置错误处理
set_error_handler(function($severity, $message, $file, $line) {
    error_log("PHP Error: $message in $file on line $line");
    return true;
});

register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        sendJsonError('服务器内部错误');
    }
});

try {
    // 检查登录状态
    session_start();
    if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
        error_log("Admin session check failed: " . print_r($_SESSION, true));
        sendJsonError('未登录或登录已过期，请重新登录');
    }

    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendJsonError('请求方法错误');
    }

    // 数据库连接
    require_once '../db.php';
    if (!$pdo) {
        sendJsonError('数据库连接失败');
    }

    // 获取技师ID
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    if ($id <= 0) {
        sendJsonError('无效的技师ID');
    }

    // 检查是否是删除操作
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    if ($action) {
        error_log("处理删除操作: $action, 技师ID: $id");
        handleDeleteAction($pdo, $id, $action);
        return;
    }

    // 获取表单数据
    $nick = isset($_POST['nick']) ? trim($_POST['nick']) : '';
    $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    $age = isset($_POST['age']) ? trim($_POST['age']) : '';
    $height = isset($_POST['height']) ? trim($_POST['height']) : '';
    $weight = isset($_POST['weight']) ? trim($_POST['weight']) : '';
    $size = isset($_POST['size']) ? trim($_POST['size']) : '';
    $model = isset($_POST['model']) ? trim($_POST['model']) : '';
    $city = isset($_POST['city']) ? trim($_POST['city']) : '';
    $hotel = isset($_POST['hotel']) ? trim($_POST['hotel']) : '';
    $virtual_orders = isset($_POST['virtual_orders']) ? intval($_POST['virtual_orders']) : 0;

    // 基本验证
    if (!$nick || !$phone || !$age || !$height || !$weight || !$size || $model === '' || !$city || !$hotel) {
        sendJsonError('请填写完整信息');
    }

    // 验证虚拟单量
    if ($virtual_orders < 0 || $virtual_orders > 9999) {
        sendJsonError('虚拟单量必须在0-9999之间');
    }

    // 查询现有技师信息
    $stmt = $pdo->prepare("SELECT * FROM technician WHERE id = ?");
    $stmt->execute([$id]);
    $existingTech = $stmt->fetch();
    
    if (!$existingTech) {
        sendJsonError('技师不存在');
    }

    // 保持原有文件路径
    $workimg_path = $existingTech['workimg'];
    $lifeimg_str = $existingTech['lifeimg'];
    $video_path = $existingTech['video'];

    // 处理文件上传
    $uploadDir = __DIR__ . '/../uploads/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // 处理工作照上传
    error_log("文件上传调试 - workimg: " . (isset($_FILES['workimg']) ? $_FILES['workimg']['error'] : 'not set'));
    if (isset($_FILES['workimg']) && $_FILES['workimg']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['workimg'];

        // 验证文件类型
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($mimeType, $allowedTypes)) {
            sendJsonError('工作照只支持 JPG、PNG、GIF 格式');
        }

        // 验证文件大小 (5MB)
        if ($file['size'] > 5 * 1024 * 1024) {
            sendJsonError('工作照大小不能超过5MB');
        }

        // 删除旧文件
        if ($workimg_path && file_exists($uploadDir . basename($workimg_path))) {
            unlink($uploadDir . basename($workimg_path));
        }

        // 生成新文件名
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'work_' . date('Ymd_His') . '_' . rand(1000, 9999) . '.' . $extension;
        $filepath = $uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            $workimg_path = 'uploads/' . $filename;
            error_log("工作照上传成功: $workimg_path");
        } else {
            error_log("工作照上传失败: 无法移动文件到 $filepath");
            sendJsonError('工作照上传失败');
        }
    }

    // 处理生活照上传 - 支持多张追加
    if (isset($_FILES['lifeimg'])) {
        error_log("检测到生活照上传请求");
        error_log("_FILES['lifeimg']: " . print_r($_FILES['lifeimg'], true));

        $lifeimg_paths = [];
        $hasFiles = false;

        // 检查是否有文件上传（支持单个文件和多个文件）
        if (is_array($_FILES['lifeimg']['name'])) {
            // 多文件上传格式
            $hasFiles = !empty($_FILES['lifeimg']['name'][0]);
            $files = $_FILES['lifeimg'];
        } else {
            // 单文件上传格式
            $hasFiles = !empty($_FILES['lifeimg']['name']);
            // 转换为多文件格式以统一处理
            $files = [
                'name' => [$_FILES['lifeimg']['name']],
                'type' => [$_FILES['lifeimg']['type']],
                'tmp_name' => [$_FILES['lifeimg']['tmp_name']],
                'error' => [$_FILES['lifeimg']['error']],
                'size' => [$_FILES['lifeimg']['size']]
            ];
        }

        if ($hasFiles) {
            error_log("开始处理生活照上传，文件数量: " . count($files['name']));

            // 检查是否要替换所有生活照（如果有replace_lifeimg参数）
        $replaceAll = isset($_POST['replace_lifeimg']) && $_POST['replace_lifeimg'] === '1';

        if ($replaceAll) {
            // 删除旧的生活照
            if ($lifeimg_str) {
                $oldLifeimgs = explode(',', $lifeimg_str);
                foreach ($oldLifeimgs as $oldImg) {
                    $oldImg = trim($oldImg);
                    if ($oldImg && file_exists($uploadDir . basename($oldImg))) {
                        unlink($uploadDir . basename($oldImg));
                    }
                }
            }
            error_log("替换模式：删除所有现有生活照");
        } else {
            // 保留现有的生活照（追加模式）
            if ($lifeimg_str) {
                $existingPaths = explode(',', $lifeimg_str);
                foreach ($existingPaths as $path) {
                    $path = trim($path);
                    if (!empty($path)) {
                        $lifeimg_paths[] = $path;
                    }
                }
                error_log("追加模式：保留现有生活照: " . implode(',', $lifeimg_paths));
            }
        }

        // 处理每个文件
        for ($i = 0; $i < count($files['name']); $i++) {
            if (empty($files['name'][$i]) || $files['error'][$i] !== UPLOAD_ERR_OK) {
                continue;
            }

            // 简单的文件类型检查
            $extension = strtolower(pathinfo($files['name'][$i], PATHINFO_EXTENSION));
            $allowedExts = ['jpg', 'jpeg', 'png', 'gif'];

            if (!in_array($extension, $allowedExts)) {
                continue;
            }

            // 文件大小检查
            if ($files['size'][$i] > 5 * 1024 * 1024) {
                continue;
            }

            // 生成文件名
            $timestamp = time() . '_' . rand(1000, 9999) . '_' . $i;
            $filename = 'life_' . $timestamp . '.' . $extension;
            $filepath = $uploadDir . $filename;

            // 上传文件
            if (move_uploaded_file($files['tmp_name'][$i], $filepath)) {
                $lifeimg_paths[] = 'uploads/' . $filename;
                error_log("生活照上传成功: " . $filename);
            }
        }

            // 更新路径
            if (!empty($lifeimg_paths)) {
                $lifeimg_str = implode(',', $lifeimg_paths);
                error_log("生活照最终路径: " . $lifeimg_str);
            } else {
                error_log("没有生活照路径，保持原有值: " . $lifeimg_str);
            }
        }
    }

    // 处理视频上传
    if (isset($_FILES['video']) && $_FILES['video']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['video'];

        // 验证文件类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        $allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'];
        if (!in_array($mimeType, $allowedTypes)) {
            sendJsonError('视频只支持 MP4、AVI、MOV、WMV 格式');
        }

        // 验证文件大小 (50MB)
        if ($file['size'] > 50 * 1024 * 1024) {
            sendJsonError('视频大小不能超过50MB');
        }

        // 删除旧文件
        if ($video_path && file_exists($uploadDir . basename($video_path))) {
            unlink($uploadDir . basename($video_path));
        }

        // 生成新文件名
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'video_' . date('Ymd_His') . '_' . rand(1000, 9999) . '.' . $extension;
        $filepath = $uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            $video_path = 'uploads/' . $filename;
        } else {
            sendJsonError('视频上传失败');
        }
    }

    // 记录即将更新的数据
    error_log("准备更新技师数据 - ID: $id");
    error_log("工作照路径: " . ($workimg_path ?: '无变化'));
    error_log("生活照路径: " . ($lifeimg_str ?: '无变化'));
    error_log("视频路径: " . ($video_path ?: '无变化'));

    // 更新数据库
    $stmt = $pdo->prepare("UPDATE technician SET nick=?, phone=?, age=?, height=?, weight=?, size=?, model=?, city=?, hotel=?, workimg=?, lifeimg=?, video=?, virtual_orders=? WHERE id=?");
    $result = $stmt->execute([
        $nick, $phone, $age, $height, $weight, $size, $model, $city, $hotel,
        $workimg_path, $lifeimg_str, $video_path, $virtual_orders, $id
    ]);

    if ($result) {
        // 记录成功信息到日志
        error_log("技师编辑成功 - ID: $id, 工作照: $workimg_path, 生活照: $lifeimg_str, 视频: $video_path");

        sendJsonSuccess('技师资料修改成功');
    } else {
        error_log("技师编辑失败 - ID: $id, SQL错误: " . print_r($stmt->errorInfo(), true));
        sendJsonError('修改失败');
    }

} catch (Exception $e) {
    error_log("Tech edit error: " . $e->getMessage());
    sendJsonError('修改失败: ' . $e->getMessage());
}

// 处理删除操作
function handleDeleteAction($pdo, $id, $action) {
    try {
        // 查询现有技师信息
        $stmt = $pdo->prepare("SELECT * FROM technician WHERE id = ?");
        $stmt->execute([$id]);
        $existingTech = $stmt->fetch();

        if (!$existingTech) {
            sendJsonError('技师不存在');
        }

        $uploadDir = __DIR__ . '/../uploads/';

        switch ($action) {
            case 'delete_workimg':
                // 删除工作照
                if ($existingTech['workimg']) {
                    $filePath = $uploadDir . basename($existingTech['workimg']);
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }

                    // 更新数据库
                    $stmt = $pdo->prepare("UPDATE technician SET workimg = NULL WHERE id = ?");
                    $result = $stmt->execute([$id]);

                    if ($result) {
                        sendJsonResponse(true, '工作照删除成功');
                    } else {
                        sendJsonError('删除失败');
                    }
                } else {
                    sendJsonError('没有工作照可删除');
                }
                break;

            case 'delete_lifeimg':
                // 删除单张生活照
                $imageIndex = isset($_POST['image_index']) ? intval($_POST['image_index']) : -1;

                if ($existingTech['lifeimg'] && $imageIndex >= 0) {
                    $lifeimgPaths = explode(',', $existingTech['lifeimg']);

                    if (isset($lifeimgPaths[$imageIndex])) {
                        $pathToDelete = trim($lifeimgPaths[$imageIndex]);
                        $filePath = $uploadDir . basename($pathToDelete);

                        // 删除文件
                        if (file_exists($filePath)) {
                            unlink($filePath);
                        }

                        // 从数组中移除该路径
                        unset($lifeimgPaths[$imageIndex]);
                        $newLifeimgStr = implode(',', array_filter($lifeimgPaths, function($path) {
                            return !empty(trim($path));
                        }));

                        // 更新数据库
                        $stmt = $pdo->prepare("UPDATE technician SET lifeimg = ? WHERE id = ?");
                        $result = $stmt->execute([$newLifeimgStr ?: null, $id]);

                        if ($result) {
                            sendJsonResponse(true, '生活照删除成功');
                        } else {
                            sendJsonError('删除失败');
                        }
                    } else {
                        sendJsonError('指定的图片不存在');
                    }
                } else {
                    sendJsonError('没有生活照可删除');
                }
                break;

            case 'delete_video':
                // 删除视频
                if ($existingTech['video']) {
                    $filePath = $uploadDir . basename($existingTech['video']);
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }

                    // 更新数据库
                    $stmt = $pdo->prepare("UPDATE technician SET video = NULL WHERE id = ?");
                    $result = $stmt->execute([$id]);

                    if ($result) {
                        sendJsonResponse(true, '视频删除成功');
                    } else {
                        sendJsonError('删除失败');
                    }
                } else {
                    sendJsonError('没有视频可删除');
                }
                break;

            default:
                sendJsonError('未知的删除操作');
        }

    } catch (Exception $e) {
        error_log("Delete action error: " . $e->getMessage());
        sendJsonError('删除失败: ' . $e->getMessage());
    }
}
?>
