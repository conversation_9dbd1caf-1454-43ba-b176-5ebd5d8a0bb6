<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量导入城市测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .import-btn {
            background: #28a745;
        }
        .import-btn:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>批量导入城市测试页面</h1>
        
        <div class="test-section">
            <h3>当前城市列表</h3>
            <button onclick="loadCurrentCities()">查看当前城市</button>
            <div id="current-cities-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>批量导入测试</h3>
            <p style="color: #666; font-size: 14px;">
                注意：这个功能需要管理员登录才能使用。如果您还没有登录后台，请先登录。<br>
                现在只导入中国主要的热门大城市（约120个），包括直辖市、省会城市、副省级城市和经济发达的地级市。
            </p>
            <button class="import-btn" onclick="testBatchImport()">开始批量导入热门城市</button>
            <div id="batch-import-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>导入后验证</h3>
            <button onclick="verifyImport()">验证导入结果</button>
            <div id="verify-result" class="result"></div>
        </div>
    </div>

    <script>
        function loadCurrentCities() {
            const resultDiv = document.getElementById('current-cities-result');
            resultDiv.textContent = '正在加载当前城市列表...';
            resultDiv.className = 'result';
            
            fetch('admin/city_list.php')
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data)) {
                        resultDiv.textContent = `当前城市数量: ${data.length}\n\n城市列表:\n${data.map(city => `${city.id}. ${city.name} ${city.is_hot ? '(热门)' : ''}`).join('\n')}`;
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.textContent = '获取城市列表失败: ' + JSON.stringify(data, null, 2);
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '错误: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function testBatchImport() {
            const resultDiv = document.getElementById('batch-import-result');
            resultDiv.textContent = '正在批量导入城市数据，请稍候...';
            resultDiv.className = 'result';
            
            fetch('admin/batch_add_cities.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const importData = data.data;
                        resultDiv.textContent = `热门城市导入完成！

导入统计:
- 总计: ${importData.total} 个热门城市
- 新增成功: ${importData.success} 个
- 已存在跳过: ${importData.skipped} 个
- 错误: ${importData.errors} 个

包含：直辖市、省会城市、副省级城市和经济发达的地级市

${importData.errors > 0 ? `错误详情:\n${importData.error_details.join('\n')}` : ''}`;
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.textContent = '批量导入失败: ' + (data.msg || '未知错误');
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '网络错误: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function verifyImport() {
            const resultDiv = document.getElementById('verify-result');
            resultDiv.textContent = '正在验证导入结果...';
            resultDiv.className = 'result';
            
            fetch('admin/city_list.php')
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data)) {
                        // 统计各省份城市数量
                        const provinces = {};
                        const samples = [];
                        
                        data.forEach(city => {
                            const name = city.name;
                            if (name.includes('北京') || name.includes('上海') || name.includes('天津') || name.includes('重庆')) {
                                provinces['直辖市'] = (provinces['直辖市'] || 0) + 1;
                            } else if (name.includes('石家庄') || name.includes('唐山') || name.includes('秦皇岛')) {
                                provinces['河北省'] = (provinces['河北省'] || 0) + 1;
                            } else if (name.includes('太原') || name.includes('大同') || name.includes('阳泉')) {
                                provinces['山西省'] = (provinces['山西省'] || 0) + 1;
                            } else if (name.includes('广州') || name.includes('深圳') || name.includes('珠海')) {
                                provinces['广东省'] = (provinces['广东省'] || 0) + 1;
                            } else if (name.includes('成都') || name.includes('绵阳') || name.includes('德阳')) {
                                provinces['四川省'] = (provinces['四川省'] || 0) + 1;
                            }
                            
                            if (samples.length < 20) {
                                samples.push(name);
                            }
                        });
                        
                        resultDiv.textContent = `验证结果:

总城市数量: ${data.length}

部分省份统计:
${Object.entries(provinces).map(([province, count]) => `- ${province}: ${count} 个城市`).join('\n')}

城市样本 (前20个):
${samples.join(', ')}

${data.length >= 100 ? '✅ 热门城市导入成功！城市数量符合预期' : data.length >= 50 ? '⚠️ 城市数量偏少，可能导入不完整' : '❌ 城市数量过少，导入可能失败'}`;
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.textContent = '验证失败: ' + JSON.stringify(data, null, 2);
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '验证错误: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        // 页面加载时自动加载当前城市列表
        window.onload = function() {
            loadCurrentCities();
        };
    </script>
</body>
</html>
