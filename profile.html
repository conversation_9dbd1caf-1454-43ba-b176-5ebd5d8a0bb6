<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>个人中心</title>
  <link href="https://fonts.googleapis.com/css?family=Roboto:400,500,700&display=swap" rel="stylesheet">
  <style>
    body {
      margin: 0;
      font-family: 'Roboto', 'PingFang SC', 'Microsoft YaHei', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
    }
    /* 以下为底部导航栏样式，完全同步主页 */
    .bottom-nav {
      position: fixed;
      left: 16px;
      right: 16px;
      bottom: 16px;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
      backdrop-filter: blur(20px);
      border-top: none;
      border-radius: 24px;
      box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2), 0 2px 8px rgba(0,0,0,0.08);
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 64px;
      z-index: 120;
      transition: box-shadow 0.3s, bottom 0.3s;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
    .nav-item {
      text-align: center;
      font-size: 12px;
      color: #888;
      flex: 1;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .nav-item.active {
      color: #00c6a2;
    }
    .nav-icon {
      display: block;
      font-size: 24px;
      margin-bottom: 2px;
    }
    .nav-item.center {
      z-index: 101;
      flex: none;
      width: auto;
      margin: 0 32px;
    }
    .nav-plus {
      position: absolute;
      top: -34px;
      left: 50%;
      transform: translateX(-50%);
      width: 48px;
      height: 48px;
      background: #00c6a2;
      border-radius: 50%;
      box-shadow: 0 12px 32px rgba(0,198,162,0.25), 0 2px 8px rgba(0,0,0,0.10);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 28px;
      border: 4px solid #fff;
      z-index: 130;
      transition: box-shadow 0.3s, top 0.3s;
      animation: navPlusFloat 1.2s infinite ease-in-out alternate;
      cursor: pointer;
    }
    @keyframes navPlusFloat {
      0% { box-shadow: 0 8px 24px rgba(0,198,162,0.18), 0 2px 8px rgba(0,0,0,0.10); top: -24px; }
      100% { box-shadow: 0 16px 32px rgba(0,198,162,0.25), 0 4px 16px rgba(0,0,0,0.12); top: -32px; }
    }
    .nav-plus:hover {
      box-shadow: 0 16px 48px 0 rgba(0,198,162,0.35), 0 4px 16px rgba(0,0,0,0.15);
      transform: translateX(-50%) scale(1.08);
    }
    /* 个人中心原有样式保留 */
    .profile-header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 18px 18px 0 18px;
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
      position: relative;
      z-index: 10;
      box-shadow: 0 4px 20px rgba(255, 154, 158, 0.3);
    }
    .profile-title {
      font-size: 22px;
      font-weight: bold;
      color: #fff;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    .profile-setting {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
    .profile-user {
      display: flex;
      align-items: center;
      padding: 18px;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
      backdrop-filter: blur(20px);
      border-radius: 18px;
      box-shadow: 0 8px 30px rgba(168, 237, 234, 0.3);
      margin: 0 18px;
      margin-top: 12px;
      position: relative;
      z-index: 2;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
    .profile-avatar {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      background: #e0fff3;
      overflow: hidden;
      margin-right: 16px;
      box-shadow: 0 2px 8px rgba(0,198,162,0.10);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .profile-avatar img {
      width: 56px;
      height: 56px;
      object-fit: cover;
      border-radius: 50%;
    }
    .profile-info {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .profile-nick {
      font-size: 18px;
      font-weight: bold;
      color: #222;
    }
    .profile-phone {
      font-size: 15px;
      color: #4caf50;
      margin-top: 2px;
    }

    .profile-stats {
      display: block;
      margin: 0 18px;
      margin-top: 8px;
      width: auto;
    }
    .stat-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      gap: 6px;
      margin: 0;
      width: 100%;
    }
    .stat-box {
      background: #fff;
      border-radius: 14px;
      box-shadow: 0 2px 8px rgba(0,198,162,0.06);
      padding: 16px 8px 14px 8px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      min-height: 68px;
    }
    .stat-label {
      font-size: 13px;
      color: #888;
      margin-bottom: 2px;
    }
    .stat-row {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      margin-top: 2px;
      min-height: 40px;
    }
    .stat-num {
      font-size: 17px;
      font-weight: bold;
      color: #222;
      margin-right: 4px;
    }
    .stat-icon {
      margin-left: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .profile-menu {
      margin: 18px;
      margin-top: 16px;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
      backdrop-filter: blur(20px);
      border-radius: 18px;
      box-shadow: 0 8px 30px rgba(210, 153, 194, 0.3);
      padding: 8px 0;
      margin-bottom: 90px;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
    .menu-item {
      display: flex;
      align-items: center;
      padding: 16px 22px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background 0.2s;
    }
    .menu-item:last-child {
      border-bottom: none;
    }
    .menu-icon {
      width: 22px;
      height: 22px;
      margin-right: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .menu-label {
      font-size: 16px;
      color: #222;
      font-weight: 500;
    }
  </style>
</head>
<body>
  <div class="profile-header">
    <div class="profile-title">我的个人中心</div>
  </div>
  <div class="profile-user">
    <div class="profile-avatar">
      <img src="images/tx.jpg" alt="头像" />
    </div>
    <div class="profile-info">
      <div class="profile-nick" id="profile-nick">用户</div>
      <div class="profile-phone" id="profile-phone"></div>
    </div>
  </div>

  <div class="profile-stats">
    <div class="stat-grid">
      <div class="stat-box" onclick="showTechnicianApplication()" style="cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.transform='scale(1.02)'; this.style.boxShadow='0 4px 12px rgba(0, 198, 162, 0.2)'" onmouseout="this.style.transform='scale(1)'; this.style.boxShadow=''">
        <div class="stat-label">申请技师</div>
        <div class="stat-row">
          <div class="stat-num">立即 <span class="stat-unit">申请</span></div>
          <span class="stat-icon">
            <!-- 申请技师：人员+工具图标，主色#00c6a2，内容最大化填满 -->
            <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="7" r="3" stroke="#00c6a2" stroke-width="2" fill="#fff"/>
              <path d="M6 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2" stroke="#00c6a2" stroke-width="2"/>
              <path d="M16 11l2 2 4-4" stroke="#00c6a2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </span>
        </div>
      </div>


      <div class="stat-box" onclick="showProfileEdit()" style="cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.transform='scale(1.02)'; this.style.boxShadow='0 4px 12px rgba(255, 152, 0, 0.2)'" onmouseout="this.style.transform='scale(1)'; this.style.boxShadow=''">
        <div class="stat-label">修改资料</div>
        <div class="stat-row">
          <div class="stat-num">编辑 <span class="stat-unit">信息</span></div>
          <span class="stat-icon">
            <!-- 修改资料：编辑图标，主色#ff9800，内容最大化填满 -->
            <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="#ff9800" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="#ff9800" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="#fff"/>
            </svg>
          </span>
        </div>
      </div>
    </div>
  </div>
  <div class="profile-menu" style="margin-bottom:90px;">
    <div class="menu-item" id="apply-technician-btn">
      <span class="menu-icon">
        <!-- 灯泡图标 -->
        <svg width="22" height="22" viewBox="0 0 22 22" fill="none">
          <ellipse cx="11" cy="9" rx="5" ry="5" stroke="#222" stroke-width="2" fill="none"/>
          <rect x="9" y="14" width="4" height="3" rx="1.2" stroke="#222" stroke-width="1.5" fill="#fff"/>
          <rect x="10" y="17" width="2" height="1.2" rx="0.6" fill="#00c6a2"/>
          <path d="M8 18h6" stroke="#00c6a2" stroke-width="1.5"/>
        </svg>
      </span>
      <span class="menu-label">申请技师</span>
    </div>



    <div class="menu-item">
      <span class="menu-icon">
        <!-- 耳机图标 -->
        <svg width="22" height="22" viewBox="0 0 22 22" fill="none">
          <path d="M4 13a7 7 0 0 1 14 0v3a2 2 0 0 1-2 2h-1a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1h2" stroke="#222" stroke-width="2" fill="none"/>
          <rect x="5" y="16" width="3" height="2" rx="1" fill="#00c6a2"/>
          <rect x="14" y="16" width="3" height="2" rx="1" fill="#00c6a2"/>
        </svg>
      </span>
      <span class="menu-label">联系客服</span>
    </div>
    <div class="menu-item" id="logout-btn">
      <span class="menu-icon">
        <!-- 退出图标：门+箭头 -->
        <svg width="22" height="22" viewBox="0 0 22 22" fill="none">
          <rect x="4" y="4" width="10" height="14" rx="2" stroke="#222" stroke-width="2" fill="#fff"/>
          <path d="M14 11h5m-2-2 2 2-2 2" stroke="#00c6a2" stroke-width="2" fill="none"/>
        </svg>
      </span>
      <span class="menu-label">退出登入</span>
    </div>
  </div>

  <!-- 技师申请弹窗 -->
  <div id="technician-modal" style="display:none;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.32);z-index:999;align-items:center;justify-content:center;">
    <div style="background:#fff;border-radius:18px;box-shadow:0 4px 24px rgba(0,0,0,0.12);padding:32px 24px 24px 24px;max-width:380px;width:90vw;display:flex;flex-direction:column;gap:18px;position:relative;max-height:90vh;overflow-y:auto;">
      <button id="tech-close" style="position:absolute;top:12px;right:12px;background:none;border:none;font-size:22px;color:#888;cursor:pointer;z-index:1001;">×</button>
      <div style="font-size:22px;font-weight:bold;color:#222;text-align:center;margin-bottom:8px;">技师申请表单</div>
      <input id="tech-nick" type="text" placeholder="请输入平台昵称请勿输入微信号" maxlength="32" style="height:40px;border-radius:10px;border:1px solid #eee;padding:0 12px;font-size:15px;outline:none;" required />
      <input id="tech-phone" type="text" placeholder="手机号自动获取" style="height:40px;border-radius:10px;border:1px solid #eee;padding:0 12px;font-size:15px;background:#f7f7f7;" readonly />
      <div style="display:flex;gap:8px;">
        <input id="tech-age" type="number" placeholder="年纪" min="16" max="80" style="flex:1;height:40px;border-radius:10px;border:1px solid #eee;padding:0 12px;font-size:15px;outline:none;" required />
        <input id="tech-height" type="number" placeholder="身高cm" min="140" max="220" style="flex:1;height:40px;border-radius:10px;border:1px solid #eee;padding:0 12px;font-size:15px;outline:none;" required />
      </div>
      <div style="display:flex;gap:8px;">
        <input id="tech-weight" type="number" placeholder="体重kg" min="35" max="150" style="flex:1;height:40px;border-radius:10px;border:1px solid #eee;padding:0 12px;font-size:15px;outline:none;" required />
        <input id="tech-size" type="text" placeholder="尺寸" style="flex:1;height:40px;border-radius:10px;border:1px solid #eee;padding:0 12px;font-size:15px;outline:none;" required />
      </div>
      <select id="tech-model" style="height:40px;border-radius:10px;border:1px solid #eee;padding:0 12px;font-size:15px;outline:none;" required>
        <option value="">你的型号 请选择</option>
        <option value="1">1</option>
        <option value="0">0</option>
        <option value="0.5">0.5</option>
        <option value="不10">不10</option>
      </select>
      <select id="tech-city" style="height:40px;border-radius:10px;border:1px solid #eee;padding:0 12px;font-size:15px;outline:none;" required>
        <option value="">请选择所在城市</option>
      </select>
      <div style="display:flex;gap:8px;align-items:center;">
        <!-- 所在城市地图选址按钮已移除 -->
      </div>
      <div style="display:flex;gap:8px;align-items:center;margin-top:8px;">
        <div style="flex:1;display:flex;flex-direction:column;gap:8px;">
          <label style="font-size:15px;color:#666;margin-bottom:2px;">所在地址</label>
          <input id="tech-hotel" type="text" placeholder="请输入酒店位置" maxlength="128" style="height:36px;border-radius:6px;border:1px solid #eee;padding:0 12px;font-size:15px;outline:none;" required />

        </div>
      </div>
      <div style="font-size:15px;font-weight:bold;color:#222;margin-top:8px;">工作形象照片1张</div>
      <div id="workimg-list" style="display:flex;gap:10px;flex-wrap:wrap;"></div>
      <div style="font-size:15px;font-weight:bold;color:#222;">生活照片（上传艺术照+生活照片 不限制）</div>
      <div id="lifeimg-list" style="display:flex;gap:10px;flex-wrap:wrap;"></div>
      <div style="font-size:15px;font-weight:bold;color:#222;">个人视频 1个</div>
      <div id="video-list" style="display:flex;gap:10px;flex-wrap:wrap;"></div>
      <button id="tech-submit" style="margin-top:8px;background:#00c6a2;color:#fff;font-size:17px;font-weight:bold;border:none;border-radius:20px;height:44px;cursor:pointer;">提交注册</button>

      <div style="font-size:13px;color:#f70505;margin-top:10px;text-align:left;">请确保您的信息真实，使用虚假信息，虚假资料造成退款-退单由本您本人自己承担.<br>请勿私下添加客户，索要联系方式，发现拉黑处理。<br>平台不会泄露您的个人信息，请放心注册</div>
    </div>
  </div>

  <div class="bottom-nav">
    <div class="nav-item" id="nav-home" onclick="window.location.href='index.html'">
        <span class="nav-icon">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><path d="M3 10.5L12 4l9 6.5V20a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V10.5z" stroke="#888" stroke-width="2"/></svg>
        </span>
        首页
    </div>
    <div class="nav-item" id="nav-technicians" onclick="window.location.href='technicians.html'">
        <span class="nav-icon">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><rect x="4" y="4" width="16" height="16" rx="4" stroke="#888" stroke-width="2"/><path d="M8 8h8v8H8V8z" stroke="#888" stroke-width="2"/></svg>
        </span>
        技师
    </div>
    <div class="nav-item center">
        <span class="nav-plus">
            <svg width="38" height="38" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="12" r="10" fill="#00c6a2"/><path d="M12 8v8M8 12h8" stroke="#fff" stroke-width="2"/></svg>
        </span>
    </div>
    <div class="nav-item" id="nav-feed" onclick="window.location.href='feed.html'">
        <span class="nav-icon">
            <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g stroke="#888" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M14 14L22 7.5C20.5 5.5 17.5 5.5 16 7.5C14.5 9.5 14 14 14 14Z"/>
                    <path d="M14 14L6 7.5C7.5 5.5 10.5 5.5 12 7.5C13.5 9.5 14 14 14 14Z"/>
                    <path d="M14 14L6 20.5C7.5 22.5 10.5 22.5 12 20.5C13.5 18.5 14 14 14 14Z"/>
                    <path d="M14 14L22 20.5C20.5 22.5 17.5 22.5 16 20.5C14.5 18.5 14 14 14 14Z"/>
                    <circle cx="14" cy="14" r="2.5" fill="#888" stroke="none"/>
                </g>
            </svg>
        </span>
        动态
    </div>
    <div class="nav-item active" id="nav-mine">
        <span class="nav-icon">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="8" r="4" stroke="#00c6a2" stroke-width="2"/><path d="M4 20c0-2.2 3.6-4 8-4s8 1.8 8 4" stroke="#00c6a2" stroke-width="2"/></svg>
        </span>
        我的
    </div>
</div>
    <script>



      window.addEventListener('DOMContentLoaded', function() {

      });

      // 首页按钮跳转主页
      document.getElementById('nav-home').addEventListener('click', function() {
        window.location.href = 'index.html';
      });

      // 我的按钮判断登录
      document.getElementById('nav-mine').addEventListener('click', function() {
        if (localStorage.getItem('isLogin') === '1') {
          window.location.href = 'profile.html';
        } else {
          window.location.href = 'login.html';
        }
      });
      // 退出登入逻辑
      document.getElementById('logout-btn').addEventListener('click', function() {
        localStorage.removeItem('isLogin');
        alert('已退出登录');
        window.location.href = 'login.html';
      });
      // 显示用户注册手机号和昵称（从 localStorage 获取）
      window.addEventListener('DOMContentLoaded', function() {
        var phone = localStorage.getItem('login_username') || '';
        if (phone) {
          var show = phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');
          document.getElementById('profile-phone').textContent = show;

          // 获取用户信息，检查是否是已通过审核的技师
          loadUserProfile(phone);
        } else {
          document.getElementById('profile-phone').textContent = '未登录';
          document.getElementById('profile-nick').textContent = '未登录用户';
        }
      });

      // 加载用户资料信息
      function loadUserProfile(phone) {
        // 首先检查是否是已通过审核的技师
        fetch('technician_status.php', {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: 'phone=' + encodeURIComponent(phone)
        })
        .then(r => r.json())
        .then(function(res) {
          if (res.success && res.data) {
            var status = res.data.status;
            var nick = res.data.nick || '';

            if (status === 'approved' || status === 1) {
              // 是已通过审核的技师，显示技师昵称
              var displayName = nick ? nick : '技师';
              document.getElementById('profile-nick').textContent = displayName;
            } else if (status === 'pending' || status === 0) {
              // 申请中的技师
              var displayName = nick ? nick + ' (审核中)' : '技师申请审核中';
              document.getElementById('profile-nick').textContent = displayName;
            } else if (status === 'rejected' || status === 2) {
              // 被拒绝的申请
              document.getElementById('profile-nick').textContent = '用户';
            } else {
              // 其他状态
              document.getElementById('profile-nick').textContent = '用户';
            }
          } else {
            // 不是技师或未找到申请记录，显示普通用户
            document.getElementById('profile-nick').textContent = '用户';
          }
        })
        .catch(function(error) {
          console.log('获取用户信息失败:', error);
          document.getElementById('profile-nick').textContent = '用户';
        });
      }
      // 技师申请弹窗逻辑
      document.getElementById('apply-technician-btn').addEventListener('click', function() {
        var phone = localStorage.getItem('login_username') || '';
        if (!phone) {
          alert('请先登录');
          return;
        }
        showLoading('正在查询申请状态...');
        fetch('technician_apply.php', {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: 'action=status&phone=' + encodeURIComponent(phone)
        })
        .then(r => r.json())
        .then(function(res) {
          hideLoading();
          if (res.status === 'pending') {
            alert('您已提交过技师申请，请等待审核。');
            return;
          } else if (res.status === 'approved') {
            alert('您已通过技师审核，无需重复申请。');
            return;
          } else if (res.status === 'rejected') {
            // 被驳回的用户可以重新申请，显示提示但不阻止表单打开
            alert('您的上次技师申请被驳回，可以修改资料重新提交。');
            // 不return，继续打开表单
          }
          document.getElementById('technician-modal').style.display = 'flex';
          document.getElementById('tech-phone').value = phone;
          // 动态获取城市列表填充下拉框
          fetch('admin/user_city_list.php?t=' + Date.now())
            .then(r => r.json())
            .then(function(data) {
              var citySelect = document.getElementById('tech-city');
              citySelect.innerHTML = '<option value="">请选择所在城市</option>';
              if (data.success && Array.isArray(data.cities)) {
                data.cities.forEach(function(city) {
                  if (city && typeof city === 'string') {
                    var opt = document.createElement('option');
                    opt.value = city;
                    opt.textContent = city;
                    citySelect.appendChild(opt);
                  }
                });
              } else {
                var opt = document.createElement('option');
                opt.value = '';
                opt.textContent = '暂无城市数据';
                citySelect.appendChild(opt);
              }
            })
            .catch(function(){
              var citySelect = document.getElementById('tech-city');
              citySelect.innerHTML = '<option value="">城市加载失败</option>';
            });
        })
        .catch(function() {
          hideLoading();
          alert('查询技师状态失败，请稍后再试');
        });
      });
      document.getElementById('tech-close').addEventListener('click', function() {
        document.getElementById('technician-modal').style.display = 'none';
      });
      document.getElementById('tech-submit').addEventListener('click', function() {
        // 简单校验
        var nick = document.getElementById('tech-nick').value.trim();
        var phone = document.getElementById('tech-phone').value.trim();
        var age = document.getElementById('tech-age').value.trim();
        var height = document.getElementById('tech-height').value.trim();
        var weight = document.getElementById('tech-weight').value.trim();
        var size = document.getElementById('tech-size').value.trim();
        var model = document.getElementById('tech-model').value.trim();
        var city = document.getElementById('tech-city').value.trim();
        var hotel = document.getElementById('tech-hotel').value.trim();
        var workimg = workimgBox.files[0];
        var lifeimgs = lifeimgBox.files;
        var video = videoBox.files[0];
        if (!nick) { alert('请输入平台昵称请勿输入微信号'); return; }
        if (!/^1[3-9]\d{9}$/.test(phone)) { alert('手机号格式不正确'); return; }
        if (!age || !height || !weight || !size || model === '' || !city || !hotel) { alert('请填写完整信息'); return; }
        // 可扩展更多校验
        // 构造表单数据
        var formData = new FormData();
        formData.append('nick', nick);
        formData.append('phone', phone);
        formData.append('age', age);
        formData.append('height', height);
        formData.append('weight', weight);
        formData.append('size', size);
        formData.append('model', model);
        formData.append('city', city);
        formData.append('hotel', hotel);
        if (workimg) formData.append('workimg', workimg);
        for (var i = 0; i < lifeimgs.length; i++) {
          formData.append('lifeimg[]', lifeimgs[i]);
        }
        if (video) formData.append('video', video);
        // 显示 loading 遮罩
        showLoading('正在提交...');
        fetch('technician_apply.php', {
          method: 'POST',
          body: formData
        })
        .then(res => res.json())
        .then(data => {
          hideLoading();
          alert(data.msg || '提交成功，等待后台审核');
          if (data.success) {
            document.getElementById('technician-modal').style.display = 'none';
            window.location.href = 'profile.html';
            // 标记已提交，待审核
            localStorage.setItem('technician_status', 'pending');
          }
        })
        .catch(() => {
          hideLoading();
          alert('网络错误，请稍后再试');
        });
      });
      // 图片/视频上传控件生成函数
      function createUploadBox(listId, accept, max, multiple) {
        var list = document.getElementById(listId);
        var files = [];
        function render() {
          list.innerHTML = '';
          files.forEach(function(file, idx) {
            var box = document.createElement('div');
            box.style.cssText = 'width:80px;height:80px;border:1px dashed #ccc;border-radius:10px;display:flex;align-items:center;justify-content:center;position:relative;overflow:hidden;background:#fafbfc;';
            if (file.preview) {
              if (accept.indexOf('image') !== -1) {
                var img = document.createElement('img');
                img.src = file.preview;
                img.style.cssText = 'width:100%;height:100%;object-fit:cover;';
                box.appendChild(img);
              } else if (accept.indexOf('video') !== -1) {
                var video = document.createElement('video');
                video.src = file.preview;
                video.style.cssText = 'width:100%;height:100%;object-fit:cover;';
                video.controls = true;
                box.appendChild(video);
              }
            }
            var del = document.createElement('span');
            del.textContent = '×';
            del.style.cssText = 'position:absolute;top:2px;right:6px;color:#888;font-size:18px;cursor:pointer;z-index:2;';
            del.onclick = function() {
              files.splice(idx, 1);
              render();
            };
            box.appendChild(del);
            list.appendChild(box);
          });
          // 工作照/视频限制数量，生活照始终显示3个加号卡片
          if (listId === 'lifeimg-list') {
            for (var i = 0; i < 3; i++) {
              var add = document.createElement('div');
              add.style.cssText = 'width:80px;height:80px;border:1px dashed #ccc;border-radius:10px;display:flex;align-items:center;justify-content:center;cursor:pointer;background:#fff;';
              add.innerHTML = '<svg width="32" height="32" viewBox="0 0 32 32"><rect x="0" y="0" width="32" height="32" rx="8" fill="#fff"/><line x1="16" y1="8" x2="16" y2="24" stroke="#888" stroke-width="2"/><line x1="8" y1="16" x2="24" y2="16" stroke="#888" stroke-width="2"/></svg>';
              add.onclick = function() {
                var input = document.createElement('input');
                input.type = 'file';
                input.accept = accept;
                input.multiple = multiple;
                input.onchange = function(e) {
                  var selected = Array.from(e.target.files);
                  selected.forEach(function(f) {
                    var reader = new FileReader();
                    reader.onload = function(ev) {
                      f.preview = ev.target.result;
                      files.push(f);
                      render();
                    };
                    reader.readAsDataURL(f);
                  });
                };
                input.click();
              };
              list.appendChild(add);
            }
          } else if (max > 0) {
            var remain = max - files.length;
            for (var i = 0; i < remain; i++) {
              var add = document.createElement('div');
              add.style.cssText = 'width:80px;height:80px;border:1px dashed #ccc;border-radius:10px;display:flex;align-items:center;justify-content:center;cursor:pointer;background:#fff;';
              add.innerHTML = '<svg width="32" height="32" viewBox="0 0 32 32"><rect x="0" y="0" width="32" height="32" rx="8" fill="#fff"/><line x1="16" y1="8" x2="16" y2="24" stroke="#888" stroke-width="2"/><line x1="8" y1="16" x2="24" y2="16" stroke="#888" stroke-width="2"/></svg>';
              add.onclick = function() {
                var input = document.createElement('input');
                input.type = 'file';
                input.accept = accept;
                input.multiple = multiple;
                input.onchange = function(e) {
                  var selected = Array.from(e.target.files);
                  selected.forEach(function(f) {
                    var reader = new FileReader();
                    reader.onload = function(ev) {
                      f.preview = ev.target.result;
                      files.push(f);
                      render();
                    };
                    reader.readAsDataURL(f);
                  });
                };
                input.click();
              };
              list.appendChild(add);
            }
          } else {
            // 不限制数量时始终显示一个加号卡片
            var add = document.createElement('div');
            add.style.cssText = 'width:80px;height:80px;border:1px dashed #ccc;border-radius:10px;display:flex;align-items:center;justify-content:center;cursor:pointer;background:#fff;';
            add.innerHTML = '<svg width="32" height="32" viewBox="0 0 32 32"><rect x="0" y="0" width="32" height="32" rx="8" fill="#fff"/><line x1="16" y1="8" x2="16" y2="24" stroke="#888" stroke-width="2"/><line x1="8" y1="16" x2="24" y2="16" stroke="#888" stroke-width="2"/></svg>';
            add.onclick = function() {
              var input = document.createElement('input');
              input.type = 'file';
              input.accept = accept;
              input.multiple = multiple;
              input.onchange = function(e) {
                var selected = Array.from(e.target.files);
                selected.forEach(function(f) {
                  var reader = new FileReader();
                  reader.onload = function(ev) {
                    f.preview = ev.target.result;
                    files.push(f);
                    render();
                  };
                  reader.readAsDataURL(f);
                });
              };
              input.click();
            };
            list.appendChild(add);
          }
        }
        return {
          getFiles: function() { return files; },
          render: render,
          files: files
        };
      }
      // 初始化上传控件
      var workimgBox, lifeimgBox, videoBox;
      window.addEventListener('DOMContentLoaded', function() {
        workimgBox = createUploadBox('workimg-list', 'image/*', 1, false);
        workimgBox.render();
        lifeimgBox = createUploadBox('lifeimg-list', 'image/*', 0, true); // 0表示不限制数量
        lifeimgBox.render();
        videoBox = createUploadBox('video-list', 'video/*', 1, false);
        videoBox.render();
        // 获取我的动态数量
        var phone = localStorage.getItem('login_username') || '';
        if (phone) {
          fetch('feed_count.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'phone=' + encodeURIComponent(phone)
          })
          .then(r => r.json())
          .then(function(res) {
            if (res.success) {
              var statNum = document.querySelector('.stat-box .stat-label:contains("我的动态")')?.parentNode.querySelector('.stat-num');
              if (statNum) statNum.innerHTML = res.num + ' <span class="stat-unit">个</span>';
              // 更保险：直接查找所有“我的动态”
              var statBoxes = document.querySelectorAll('.stat-box');
              statBoxes.forEach(function(box){
                var label = box.querySelector('.stat-label');
                if(label && label.textContent.indexOf('我的动态')>-1){
                  var num = box.querySelector('.stat-num');
                  if(num) num.innerHTML = res.num + ' <span class="stat-unit">个</span>';
                }
              });
            }
          });
        }
      });
      // loading 遮罩实现
      function showLoading(text) {
        var loading = document.getElementById('loading-mask');
        if (!loading) {
          loading = document.createElement('div');
          loading.id = 'loading-mask';
          loading.style.cssText = 'position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.32);z-index:9999;display:flex;align-items:center;justify-content:center;';
          var box = document.createElement('div');
          box.style.cssText = 'background:#fff;border-radius:16px;padding:32px 38px;font-size:18px;color:#222;box-shadow:0 4px 24px rgba(0,0,0,0.12);display:flex;flex-direction:column;align-items:center;';
          var spinner = document.createElement('div');
          spinner.style.cssText = 'width:38px;height:38px;border-radius:50%;border:4px solid #00c6a2;border-top:4px solid #eee;animation:spin 1s linear infinite;margin-bottom:18px;';
          spinner.innerHTML = '';
          box.appendChild(spinner);
          var txt = document.createElement('div');
          txt.textContent = text || '正在提交...';
          box.appendChild(txt);
          loading.appendChild(box);
          document.body.appendChild(loading);
          var style = document.createElement('style');
          style.innerHTML = '@keyframes spin {0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}';
          document.head.appendChild(style);
        } else {
          loading.style.display = 'flex';
          loading.querySelector('div > div:last-child').textContent = text || '正在提交...';
        }
      }
      function hideLoading() {
        var loading = document.getElementById('loading-mask');
        if (loading) loading.style.display = 'none';
      }

      // ================= 多行文本解析函数集成 =================
      /**
       * 解析多行文本，去除空行、前后空白、前缀符号（如*、-、数字点等），返回纯净内容数组
       * @param {string} text 多行字符串
       * @returns {string[]} 解析后的每行内容数组
       */
      function parseMultilineText(text) {
        if (!text) return [];
        return text.split(/\r?\n/)
          .map(line => line.trim())
          .filter(line => line.length > 0)
          .map(line => line.replace(/^([*\-\d.\s]+)?/, ''));
      }

      // 示例用法：
      // var raw = `* 所在地址\n- 经度\n- 纬度\n  其他说明`;
      // var arr = parseMultilineText(raw);
      // console.log(arr); // ["所在地址", "经度", "纬度", "其他说明"]

      // 你可以在实际业务中调用 parseMultilineText(text) 进行多行文本解析

      // 显示技师申请页面
      function showTechnicianApplication() {
        // 直接触发下面的申请技师按钮点击事件
        const applyBtn = document.getElementById('apply-technician-btn');
        if (applyBtn) {
          applyBtn.click();
        } else {
          alert('技师申请功能暂未开放，请稍后再试！');
        }
      }

      // 显示修改技师申请资料页面
      function showProfileEdit() {
        // 首先检查用户是否已登录
        const phone = localStorage.getItem('login_username') || '';
        if (!phone) {
          alert('请先登录');
          return;
        }

        // 显示加载状态
        showLoading('正在获取申请资料...');

        // 获取用户的技师申请资料
        fetch('technician_status.php', {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: 'phone=' + encodeURIComponent(phone)
        })
        .then(r => r.json())
        .then(function(res) {
          hideLoading();

          if (!res.data) {
            alert('您还没有提交过技师申请，请先申请技师！');
            return;
          }

          // 创建修改技师资料的模态框
          const modal = document.createElement('div');
          modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            padding: 20px;
            box-sizing: border-box;
          `;

          const techData = res.data;
          modal.innerHTML = `
            <div style="
              background: white;
              border-radius: 16px;
              padding: 24px;
              max-width: 500px;
              width: 100%;
              max-height: 90vh;
              overflow-y: auto;
              position: relative;
            ">
              <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;">
                <h3 style="margin: 0; color: #333; font-size: 18px;">🛠️ 修改技师申请资料</h3>
                <button onclick="this.closest('.modal').remove()" style="
                  background: none;
                  border: none;
                  font-size: 24px;
                  cursor: pointer;
                  color: #999;
                  padding: 0;
                  width: 30px;
                  height: 30px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                ">&times;</button>
              </div>

              <form id="tech-edit-form" enctype="multipart/form-data" style="display: flex; flex-direction: column; gap: 16px;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                  <div>
                    <label style="display: block; margin-bottom: 6px; color: #333; font-weight: 500;">昵称</label>
                    <input type="text" name="nick" value="${techData.nick || ''}" placeholder="请输入昵称" style="
                      width: 100%;
                      height: 40px;
                      border: 1px solid #ddd;
                      border-radius: 8px;
                      padding: 0 12px;
                      font-size: 14px;
                      box-sizing: border-box;
                    " required />
                  </div>
                  <div>
                    <label style="display: block; margin-bottom: 6px; color: #333; font-weight: 500;">手机号</label>
                    <input type="tel" name="phone" value="${techData.phone || ''}" placeholder="请输入手机号" style="
                      width: 100%;
                      height: 40px;
                      border: 1px solid #ddd;
                      border-radius: 8px;
                      padding: 0 12px;
                      font-size: 14px;
                      box-sizing: border-box;
                    " required pattern="^1[3-9]\\d{9}$" title="请输入正确的手机号格式" />
                    <div style="font-size: 11px; color: #ff6b6b; margin-top: 4px;">
                      ⚠️ 修改手机号将更改您的登录账号
                    </div>
                  </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 12px;">
                  <div>
                    <label style="display: block; margin-bottom: 6px; color: #333; font-weight: 500;">年龄</label>
                    <input type="number" name="age" value="${techData.age || ''}" min="16" max="80" style="
                      width: 100%;
                      height: 40px;
                      border: 1px solid #ddd;
                      border-radius: 8px;
                      padding: 0 12px;
                      font-size: 14px;
                      box-sizing: border-box;
                    " required />
                  </div>
                  <div>
                    <label style="display: block; margin-bottom: 6px; color: #333; font-weight: 500;">身高(cm)</label>
                    <input type="number" name="height" value="${techData.height || ''}" min="140" max="220" style="
                      width: 100%;
                      height: 40px;
                      border: 1px solid #ddd;
                      border-radius: 8px;
                      padding: 0 12px;
                      font-size: 14px;
                      box-sizing: border-box;
                    " required />
                  </div>
                  <div>
                    <label style="display: block; margin-bottom: 6px; color: #333; font-weight: 500;">体重(kg)</label>
                    <input type="number" name="weight" value="${techData.weight || ''}" min="30" max="200" style="
                      width: 100%;
                      height: 40px;
                      border: 1px solid #ddd;
                      border-radius: 8px;
                      padding: 0 12px;
                      font-size: 14px;
                      box-sizing: border-box;
                    " required />
                  </div>
                  <div>
                    <label style="display: block; margin-bottom: 6px; color: #333; font-weight: 500;">尺寸</label>
                    <input type="text" name="size" value="${techData.size || ''}" placeholder="如: C" style="
                      width: 100%;
                      height: 40px;
                      border: 1px solid #ddd;
                      border-radius: 8px;
                      padding: 0 12px;
                      font-size: 14px;
                      box-sizing: border-box;
                    " required />
                  </div>
                </div>

                <div style="margin-bottom: 15px;">
                  <label style="display: block; margin-bottom: 6px; color: #333; font-weight: 500;">你的型号</label>
                  <select name="model" style="
                    width: 100%;
                    height: 40px;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 0 12px;
                    font-size: 14px;
                    box-sizing: border-box;
                  " required>
                    <option value="">请选择</option>
                    <option value="1" ${techData.model === '1' ? 'selected' : ''}>1</option>
                    <option value="0" ${techData.model === '0' ? 'selected' : ''}>0</option>
                    <option value="0.5" ${techData.model === '0.5' ? 'selected' : ''}>0.5</option>
                    <option value="不10" ${techData.model === '不10' ? 'selected' : ''}>不10</option>
                  </select>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                  <div>
                    <label style="display: block; margin-bottom: 6px; color: #333; font-weight: 500;">城市</label>
                    <select name="city" id="tech-edit-city-select" style="
                      width: 100%;
                      height: 40px;
                      border: 1px solid #ddd;
                      border-radius: 8px;
                      padding: 0 12px;
                      font-size: 14px;
                      box-sizing: border-box;
                    " required>
                      <option value="">正在加载城市...</option>
                    </select>
                  </div>
                  <div>
                    <label style="display: block; margin-bottom: 6px; color: #333; font-weight: 500;">详细地址</label>
                    <input type="text" name="hotel" value="${techData.hotel || ''}" placeholder="详细地址" style="
                      width: 100%;
                      height: 40px;
                      border: 1px solid #ddd;
                      border-radius: 8px;
                      padding: 0 12px;
                      font-size: 14px;
                      box-sizing: border-box;
                    " required />
                  </div>
                </div>

                <div>
                  <label style="display: block; margin-bottom: 6px; color: #333; font-weight: 500;">工作照</label>
                  ${techData.workimg ? `
                    <div style="margin-bottom: 8px;">
                      <img src="${techData.workimg}" style="width: 80px; height: 80px; object-fit: cover; border-radius: 8px; border: 1px solid #ddd;">
                      <span style="font-size: 12px; color: #666; margin-left: 8px;">当前工作照</span>
                    </div>
                  ` : ''}
                  <div id="edit-workimg-list" style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 8px;"></div>
                  <div style="font-size: 12px; color: #888;">点击+号更换工作照，不更换则保持原图片</div>
                </div>

                <div>
                  <label style="display: block; margin-bottom: 6px; color: #333; font-weight: 500;">生活照</label>
                  ${techData.lifeimg ? `
                    <div style="margin-bottom: 8px; display: flex; gap: 8px; flex-wrap: wrap;" id="existing-lifeimg-list">
                      ${techData.lifeimg.split(',').filter(img => img.trim()).map((img, index) => `
                        <div style="position: relative; width: 60px; height: 60px;">
                          <img src="${img.trim()}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 6px; border: 1px solid #ddd;">
                          <span onclick="removeExistingLifeimg(${index})" style="position: absolute; top: -5px; right: -5px; background: #ff4444; color: white; border-radius: 50%; width: 18px; height: 18px; display: flex; align-items: center; justify-content: center; font-size: 12px; cursor: pointer; line-height: 1;">×</span>
                        </div>
                      `).join('')}
                      <span style="font-size: 12px; color: #666; align-self: center; margin-left: 8px;">当前生活照</span>
                    </div>
                  ` : ''}
                  <div id="edit-lifeimg-list" style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 8px;"></div>
                  <div style="font-size: 12px; color: #888;">点击+号添加新的生活照，点击×号删除不需要的照片</div>
                </div>

                <div>
                  <label style="display: block; margin-bottom: 6px; color: #333; font-weight: 500;">个人视频</label>
                  ${techData.video ? `
                    <div style="margin-bottom: 8px;">
                      <video controls style="width: 120px; height: 80px; object-fit: cover; border-radius: 8px; border: 1px solid #ddd;">
                        <source src="${techData.video}" type="video/mp4">
                      </video>
                      <span style="font-size: 12px; color: #666; margin-left: 8px;">当前视频</span>
                    </div>
                  ` : ''}
                  <div id="edit-video-list" style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 8px;"></div>
                  <div style="font-size: 12px; color: #888;">点击+号更换视频，不更换则保持原视频</div>
                </div>

                <div style="display: flex; gap: 12px; margin-top: 8px;">
                  <button type="submit" style="
                    flex: 1;
                    height: 44px;
                    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;
                  ">💾 保存修改</button>
                  <button type="button" onclick="this.closest('.modal').remove()" style="
                    flex: 1;
                    height: 44px;
                    background: #f5f5f5;
                    color: #666;
                    border: none;
                    border-radius: 8px;
                    font-size: 16px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                  ">❌ 取消</button>
                </div>
              </form>

              <div id="tech-edit-msg" style="
                margin-top: 16px;
                padding: 12px;
                border-radius: 8px;
                text-align: center;
                font-size: 14px;
                display: none;
              "></div>
            </div>
          `;

          modal.className = 'modal';
          document.body.appendChild(modal);

          // 加载城市列表
          loadCitiesForEdit(techData.city);

          // 初始化编辑页面的上传控件
          window.editWorkimgBox = createUploadBox('edit-workimg-list', 'image/*', 1, false);
          window.editWorkimgBox.render();

          window.editLifeimgBox = createUploadBox('edit-lifeimg-list', 'image/*', 0, true);
          window.editLifeimgBox.render();

          window.editVideoBox = createUploadBox('edit-video-list', 'video/*', 1, false);
          window.editVideoBox.render();

          // 存储要删除的现有生活照索引
          window.removedLifeimgIndexes = [];

          // 删除现有生活照的函数
          window.removeExistingLifeimg = function(index) {
            if (confirm('确定要删除这张生活照吗？')) {
              window.removedLifeimgIndexes.push(index);
              // 隐藏被删除的图片
              var existingList = document.getElementById('existing-lifeimg-list');
              if (existingList) {
                var imgDivs = existingList.querySelectorAll('div[style*="position: relative"]');
                if (imgDivs[index]) {
                  imgDivs[index].style.display = 'none';
                }
              }
            }
          };

          // 绑定表单提交事件
          document.getElementById('tech-edit-form').addEventListener('submit', function(e) {
            e.preventDefault();
            submitTechEdit(e.target);
          });

          // 点击背景关闭模态框
          modal.addEventListener('click', function(e) {
            if (e.target === modal) {
              modal.remove();
            }
          });
        })
        .catch(function(error) {
          hideLoading();
          alert('获取申请资料失败，请稍后再试');
        });
      }

      // 加载城市列表用于编辑
      function loadCitiesForEdit(selectedCity) {
        console.log('加载城市列表，选中城市:', selectedCity);

        fetch('city_list.php')
        .then(r => r.json())
        .then(function(res) {
          console.log('城市列表API响应:', res);

          if (res.success && res.data) {
            const citySelect = document.getElementById('tech-edit-city-select');
            if (citySelect) {
              // 清空现有选项
              citySelect.innerHTML = '<option value="">请选择城市</option>';

              // 添加城市选项
              res.data.forEach(function(city) {
                const option = document.createElement('option');
                option.value = city.name;
                option.textContent = city.name;

                // 检查是否是选中的城市
                if (city.name === selectedCity) {
                  option.selected = true;
                  console.log('设置选中城市:', city.name);
                }

                citySelect.appendChild(option);
              });

              // 如果没有找到匹配的城市，手动设置值
              if (selectedCity && citySelect.value !== selectedCity) {
                console.log('未找到匹配城市，手动添加:', selectedCity);
                const customOption = document.createElement('option');
                customOption.value = selectedCity;
                customOption.textContent = selectedCity;
                customOption.selected = true;
                citySelect.appendChild(customOption);
              }

              console.log('城市选择框当前值:', citySelect.value);
            } else {
              console.error('未找到城市选择框元素');
            }
          } else {
            console.error('城市列表API返回错误:', res);
            // 如果API失败，至少设置当前城市
            const citySelect = document.getElementById('tech-edit-city-select');
            if (citySelect && selectedCity) {
              citySelect.innerHTML = `
                <option value="">请选择城市</option>
                <option value="${selectedCity}" selected>${selectedCity}</option>
              `;
            }
          }
        })
        .catch(function(error) {
          console.error('加载城市列表失败:', error);
          // 网络错误时，至少设置当前城市
          const citySelect = document.getElementById('tech-edit-city-select');
          if (citySelect && selectedCity) {
            citySelect.innerHTML = `
              <option value="">请选择城市</option>
              <option value="${selectedCity}" selected>${selectedCity}</option>
            `;
          }
        });
      }

      // 提交技师资料修改
      function submitTechEdit(form) {
        const msgDiv = document.getElementById('tech-edit-msg');
        const submitBtn = form.querySelector('button[type="submit"]');

        // 获取当前登录的手机号和表单中的新手机号
        const currentPhone = localStorage.getItem('login_username') || '';
        const newPhone = form.querySelector('input[name="phone"]').value.trim();

        // 验证新手机号格式
        if (!/^1[3-9]\d{9}$/.test(newPhone)) {
          msgDiv.style.display = 'block';
          msgDiv.textContent = '请输入正确的手机号格式';
          msgDiv.style.background = '#f8d7da';
          msgDiv.style.color = '#721c24';
          msgDiv.style.border = '1px solid #f5c6cb';
          return;
        }

        // 如果手机号发生变更，给出确认提示
        if (currentPhone !== newPhone) {
          const confirmed = confirm(`确定要将手机号从 ${currentPhone} 修改为 ${newPhone} 吗？\n\n修改后您需要使用新手机号登录，密码保持不变。`);
          if (!confirmed) {
            return;
          }
        }

        // 显示提交状态
        submitBtn.disabled = true;
        submitBtn.textContent = '⏳ 保存中...';
        msgDiv.style.display = 'none';

        const formData = new FormData(form);
        // 添加原手机号信息，用于后端验证
        formData.append('original_phone', currentPhone);

        // 添加要删除的现有生活照索引
        if (window.removedLifeimgIndexes && window.removedLifeimgIndexes.length > 0) {
          formData.append('removed_lifeimg_indexes', JSON.stringify(window.removedLifeimgIndexes));
        }

        // 添加上传控件中的文件
        if (window.editWorkimgBox && window.editWorkimgBox.getFiles().length > 0) {
          formData.append('workimg', window.editWorkimgBox.getFiles()[0]);
        }

        if (window.editLifeimgBox && window.editLifeimgBox.getFiles().length > 0) {
          window.editLifeimgBox.getFiles().forEach(function(file, index) {
            formData.append('lifeimg[]', file);
          });
        }

        if (window.editVideoBox && window.editVideoBox.getFiles().length > 0) {
          formData.append('video', window.editVideoBox.getFiles()[0]);
        }

        fetch('technician_update.php', {
          method: 'POST',
          body: formData
        })
        .then(r => r.json())
        .then(function(res) {
          submitBtn.disabled = false;
          submitBtn.innerHTML = '💾 保存修改';

          msgDiv.style.display = 'block';
          msgDiv.textContent = res.msg || (res.success ? '修改成功' : '修改失败');

          if (res.success) {
            msgDiv.style.background = '#d4edda';
            msgDiv.style.color = '#155724';
            msgDiv.style.border = '1px solid #c3e6cb';

            // 如果手机号发生了变更，更新本地存储的登录信息
            if (currentPhone !== newPhone) {
              localStorage.setItem('login_username', newPhone);
              msgDiv.textContent = res.msg + ' 登录账号已更新为新手机号';

              // 提示用户账号已变更
              setTimeout(() => {
                alert('手机号修改成功！\n\n您的登录账号已更新为：' + newPhone + '\n密码保持不变，请使用新手机号登录。');
                // 刷新页面以更新显示的手机号
                window.location.reload();
              }, 1000);
            } else {
              // 2秒后关闭模态框
              setTimeout(() => {
                document.querySelector('.modal').remove();
              }, 2000);
            }
          } else {
            msgDiv.style.background = '#f8d7da';
            msgDiv.style.color = '#721c24';
            msgDiv.style.border = '1px solid #f5c6cb';
          }
        })
        .catch(function(error) {
          submitBtn.disabled = false;
          submitBtn.innerHTML = '💾 保存修改';

          msgDiv.style.display = 'block';
          msgDiv.textContent = '网络错误，修改失败';
          msgDiv.style.background = '#f8d7da';
          msgDiv.style.color = '#721c24';
          msgDiv.style.border = '1px solid #f5c6cb';
        });
      }
    </script>
</body>
</html>
