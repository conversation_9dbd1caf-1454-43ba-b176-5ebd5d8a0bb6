<?php
// 测试生活照上传功能
session_start();
$_SESSION['admin_login'] = true; // 模拟登录状态

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo json_encode([
        'success' => true,
        'msg' => '生活照上传测试',
        'data' => [
            'POST' => $_POST,
            'FILES' => $_FILES,
            'lifeimg_info' => isset($_FILES['lifeimg']) ? [
                'count' => count($_FILES['lifeimg']['name']),
                'names' => $_FILES['lifeimg']['name'],
                'errors' => $_FILES['lifeimg']['error'],
                'sizes' => $_FILES['lifeimg']['size'],
                'types' => $_FILES['lifeimg']['type']
            ] : 'not set'
        ]
    ]);
} else {
    // 显示测试表单
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>生活照上传测试</title>
        <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            .form-group { margin: 15px 0; }
            label { display: block; margin-bottom: 5px; }
            input, button { padding: 8px; }
            button { background: #007bff; color: white; border: none; cursor: pointer; }
            #result { margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 4px; }
        </style>
    </head>
    <body>
        <h2>生活照上传测试</h2>
        <form id="testForm" enctype="multipart/form-data">
            <div class="form-group">
                <label>技师ID:</label>
                <input type="number" name="id" value="1" required>
            </div>
            <div class="form-group">
                <label>昵称:</label>
                <input type="text" name="nick" value="测试技师" required>
            </div>
            <div class="form-group">
                <label>生活照 (可选择多张):</label>
                <input type="file" name="lifeimg" accept="image/*" multiple>
            </div>
            <button type="submit">测试上传</button>
        </form>
        
        <div id="result"></div>
        
        <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '上传中...';
            
            fetch('test_lifeimg_upload.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                resultDiv.innerHTML = '错误: ' + error.message;
            });
        });
        </script>
    </body>
    </html>
    <?php
}
?>
