<?php
// tech_delete.php - 删除技师申请
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

try {
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    
    if ($id <= 0) {
        echo json_encode(['success' => false, 'msg' => '无效的技师ID']);
        exit;
    }

    // 查询技师信息，获取文件路径用于删除
    $stmt = $pdo->prepare("SELECT id, nick, phone, status, workimg, lifeimg, video FROM technician WHERE id = ?");
    $stmt->execute([$id]);
    $tech = $stmt->fetch();
    
    if (!$tech) {
        echo json_encode(['success' => false, 'msg' => '技师不存在']);
        exit;
    }

    // 允许删除所有状态的技师，但给出不同的提示
    $statusText = '';
    if ($tech['status'] === 'pending' || $tech['status'] == 0) {
        $statusText = '申请中';
    } elseif ($tech['status'] === 'approved' || $tech['status'] == 1) {
        $statusText = '已通过';
    } elseif ($tech['status'] === 'rejected' || $tech['status'] == 2) {
        $statusText = '已驳回';
    }

    // 删除相关文件
    $filesToDelete = [];
    
    // 工作照片
    if (!empty($tech['workimg'])) {
        $workimgPath = __DIR__ . '/../' . ltrim($tech['workimg'], '/');
        if (file_exists($workimgPath)) {
            $filesToDelete[] = $workimgPath;
        }
    }
    
    // 生活照片
    if (!empty($tech['lifeimg'])) {
        $lifeimgs = explode(',', $tech['lifeimg']);
        foreach ($lifeimgs as $lifeimg) {
            $lifeimg = trim($lifeimg);
            if (!empty($lifeimg)) {
                $lifeimgPath = __DIR__ . '/../' . ltrim($lifeimg, '/');
                if (file_exists($lifeimgPath)) {
                    $filesToDelete[] = $lifeimgPath;
                }
            }
        }
    }
    
    // 视频文件
    if (!empty($tech['video'])) {
        $videoPath = __DIR__ . '/../' . ltrim($tech['video'], '/');
        if (file_exists($videoPath)) {
            $filesToDelete[] = $videoPath;
        }
    }

    // 删除数据库记录
    $stmt = $pdo->prepare("DELETE FROM technician WHERE id = ?");
    $result = $stmt->execute([$id]);
    
    if ($result) {
        // 删除文件
        foreach ($filesToDelete as $filePath) {
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
        
        echo json_encode([
            'success' => true,
            'msg' => "技师已删除（{$tech['nick']} - {$tech['phone']} - {$statusText}）"
        ]);
    } else {
        echo json_encode(['success' => false, 'msg' => '删除失败']);
    }

} catch (Exception $e) {
    error_log("Tech delete error: " . $e->getMessage());
    echo json_encode(['success' => false, 'msg' => '删除失败: ' . $e->getMessage()]);
}
?>
