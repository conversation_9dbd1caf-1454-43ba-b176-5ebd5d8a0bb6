
<?php
// 禁用错误输出到浏览器，防止HTML错误信息污染JSON响应
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// 开启输出缓冲，确保只输出JSON
ob_start();

header('Content-Type: application/json; charset=utf-8');

// 统一的JSON响应函数
function sendJsonResponse($success, $message, $data = null) {
    ob_clean();
    $response = ['success' => $success, 'msg' => $message];
    if ($data !== null) {
        $response = array_merge($response, $data);
    }
    echo json_encode($response);
    exit;
}

// 注册致命错误处理器
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '服务器内部错误', 'cities' => []]);
        exit;
    }
});

try {
    require_once '../db.php';

    if (!$pdo) {
        sendJsonResponse(false, '数据库连接失败', ['cities' => []]);
    }
} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    sendJsonResponse(false, '数据库连接失败', ['cities' => []]);
}

// 查询后台城市表（city）
$sql = "SELECT name FROM city ORDER BY id DESC";
$stmt = $pdo->query($sql);
$cities = [];
if ($stmt) {
    foreach ($stmt as $row) {
        $cities[] = $row['name'];
    }
}
sendJsonResponse(true, '获取成功', ['cities' => $cities]);
?>
