

<?php
header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

if (!$pdo) {
    echo json_encode(['success' => false, 'msg' => '数据库连接失败']);
    exit;
}

// 查询后台城市表（city）
$sql = "SELECT name FROM city ORDER BY id DESC";
$stmt = $pdo->query($sql);
$cities = [];
if ($stmt) {
    foreach ($stmt as $row) {
        $cities[] = $row['name'];
    }
}
echo json_encode([
    'success' => true,
    'cities' => $cities
], JSON_UNESCAPED_UNICODE);
?>
