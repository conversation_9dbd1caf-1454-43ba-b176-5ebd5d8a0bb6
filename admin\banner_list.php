<?php
// banner_list.php - 获取轮播图列表
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

$bannerFile = __DIR__ . '/banner.json';

try {
    if (file_exists($bannerFile)) {
        $banners = json_decode(file_get_contents($bannerFile), true);
        if ($banners === null) {
            echo json_encode(['success' => false, 'msg' => '轮播图数据格式错误']);
            exit;
        }
        echo json_encode(['success' => true, 'data' => $banners]);
    } else {
        echo json_encode(['success' => true, 'data' => []]);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'msg' => '获取轮播图列表失败: ' . $e->getMessage()]);
}
?>