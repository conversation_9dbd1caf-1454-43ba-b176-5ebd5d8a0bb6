<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>城市布局预览</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            margin-bottom: 20px;
            display: flex;
            gap: 12px;
            align-items: center;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
        }
        button:hover {
            background: #0056b3;
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #1e7e34 0%, #17a2b8 100%);
        }
        .city-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 8px;
            padding: 20px 0;
        }
        .city-card {
            background: #fff;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 12px 8px;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
            cursor: pointer;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .city-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        .city-icon {
            font-size: 12px;
            color: #667eea;
            margin-bottom: 4px;
            font-weight: 500;
        }
        .city-name {
            font-size: 13px;
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
            line-height: 1.2;
            word-break: break-all;
        }
        .city-id {
            font-size: 10px;
            color: #999;
        }
        .delete-btn {
            width: 100%;
            height: 24px;
            font-size: 10px;
            border-radius: 4px;
            background: #dc3545;
            border: none;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            padding: 0;
            margin-top: 8px;
        }
        .delete-btn:hover {
            background: #c82333;
        }
        .stats {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>城市管理布局预览 - 一行10列</h1>
        
        <div class="header">
            <button onclick="loadCities()">➕ 新增城市</button>
            <button class="btn-success" onclick="alert('批量导入功能')">📥 批量导入全国城市</button>
            <div class="stats" id="city-count">
                共 0 个城市
            </div>
        </div>
        
        <div id="city-container">
            <div style="text-align: center; padding: 40px; color: #999;">
                点击下方按钮加载城市数据
            </div>
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
            <button onclick="loadCities()">加载城市数据</button>
            <button onclick="generateSampleCities()">生成示例数据</button>
        </div>
    </div>

    <script>
        function loadCities() {
            const container = document.getElementById('city-container');
            const countDiv = document.getElementById('city-count');
            
            container.innerHTML = '<div style="text-align: center; padding: 40px; color: #999;">正在加载城市数据...</div>';
            
            fetch('admin/city_list.php')
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data)) {
                        renderCities(data);
                        countDiv.textContent = `共 ${data.length} 个城市`;
                    } else {
                        container.innerHTML = '<div style="text-align: center; padding: 40px; color: #f56c6c;">加载失败</div>';
                    }
                })
                .catch(error => {
                    container.innerHTML = '<div style="text-align: center; padding: 40px; color: #f56c6c;">网络错误</div>';
                });
        }
        
        function generateSampleCities() {
            const sampleCities = [
                {id: 1, name: '北京市'},
                {id: 2, name: '上海市'},
                {id: 3, name: '天津市'},
                {id: 4, name: '重庆市'},
                {id: 5, name: '石家庄市'},
                {id: 6, name: '唐山市'},
                {id: 7, name: '秦皇岛市'},
                {id: 8, name: '邯郸市'},
                {id: 9, name: '邢台市'},
                {id: 10, name: '保定市'},
                {id: 11, name: '张家口市'},
                {id: 12, name: '承德市'},
                {id: 13, name: '沧州市'},
                {id: 14, name: '廊坊市'},
                {id: 15, name: '衡水市'},
                {id: 16, name: '太原市'},
                {id: 17, name: '大同市'},
                {id: 18, name: '阳泉市'},
                {id: 19, name: '长治市'},
                {id: 20, name: '晋城市'},
                {id: 21, name: '朔州市'},
                {id: 22, name: '晋中市'},
                {id: 23, name: '运城市'},
                {id: 24, name: '忻州市'},
                {id: 25, name: '临汾市'},
                {id: 26, name: '吕梁市'},
                {id: 27, name: '呼和浩特市'},
                {id: 28, name: '包头市'},
                {id: 29, name: '乌海市'},
                {id: 30, name: '赤峰市'},
                {id: 31, name: '通辽市'},
                {id: 32, name: '鄂尔多斯市'},
                {id: 33, name: '呼伦贝尔市'},
                {id: 34, name: '巴彦淖尔市'},
                {id: 35, name: '乌兰察布市'},
                {id: 36, name: '兴安盟'},
                {id: 37, name: '锡林郭勒盟'},
                {id: 38, name: '阿拉善盟'},
                {id: 39, name: '沈阳市'},
                {id: 40, name: '大连市'},
                {id: 41, name: '鞍山市'},
                {id: 42, name: '抚顺市'},
                {id: 43, name: '本溪市'},
                {id: 44, name: '丹东市'},
                {id: 45, name: '锦州市'},
                {id: 46, name: '营口市'},
                {id: 47, name: '阜新市'},
                {id: 48, name: '辽阳市'},
                {id: 49, name: '盘锦市'},
                {id: 50, name: '铁岭市'}
            ];
            
            renderCities(sampleCities);
            document.getElementById('city-count').textContent = `共 ${sampleCities.length} 个城市`;
        }
        
        function renderCities(cities) {
            const container = document.getElementById('city-container');

            if (cities.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 60px 20px; color: #999;">
                        <div style="font-size: 48px; margin-bottom: 16px;">🏙️</div>
                        <div style="font-size: 16px; margin-bottom: 8px;">暂无城市数据</div>
                        <div style="font-size: 14px;">点击"批量导入热门城市"添加数据</div>
                    </div>
                `;
                return;
            }

            container.innerHTML = `
                <div class="city-grid">
                    ${cities.map(city => `
                        <div class="city-card" style="${city.is_hot == 1 ? 'border-color: #ffc107; background: linear-gradient(135deg, #fff9c4 0%, #fff 100%);' : ''}">
                            <div style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
                                <div class="city-icon">🏙️</div>
                                <div class="city-name">
                                    ${city.name}
                                    ${city.is_hot == 1 ? '<span style="color: #ffc107; font-size: 10px;">⭐</span>' : ''}
                                </div>
                                <div class="city-id">#${city.id}</div>
                                <div style="font-size: 9px; color: ${city.is_hot == 1 ? '#ffc107' : '#999'}; margin-top: 2px;">
                                    ${city.is_hot == 1 ? '热门' : '普通'}
                                </div>
                            </div>
                            <div style="display: flex; gap: 2px; margin-top: 8px;">
                                <button
                                    style="flex: 1; height: 20px; font-size: 8px; border-radius: 3px; background: ${city.is_hot == 1 ? '#ffc107' : '#28a745'}; border: none; color: ${city.is_hot == 1 ? '#212529' : 'white'}; cursor: pointer; padding: 0;"
                                    onclick="toggleHot(${city.id}, ${city.is_hot == 1 ? 0 : 1})"
                                    title="${city.is_hot == 1 ? '取消热门' : '设为热门'}">
                                    ${city.is_hot == 1 ? '⭐' : '☆'}
                                </button>
                                <button
                                    class="delete-btn"
                                    style="flex: 1; height: 20px; font-size: 8px;"
                                    onclick="deleteCity(${city.id})"
                                    title="删除城市: ${city.name}">
                                    🗑️
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }
        
        function deleteCity(id) {
            if (confirm('确定要删除这个城市吗？')) {
                alert('删除功能演示 - 城市ID: ' + id);
            }
        }

        function toggleHot(id, isHot) {
            const actionText = isHot ? '设为热门城市' : '取消热门城市';
            if (confirm(`确定要${actionText}吗？`)) {
                alert(`热门功能演示 - 城市ID: ${id}, 操作: ${actionText}`);
                // 这里可以调用实际的API
                // 演示：重新加载数据
                setTimeout(() => {
                    loadCities();
                }, 500);
            }
        }
        
        // 页面加载时生成示例数据
        window.onload = function() {
            generateSampleCities();
        };
    </script>
</body>
</html>
