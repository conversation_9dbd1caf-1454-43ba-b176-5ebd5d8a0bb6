<?php
// tech_update.php - 更新技师状态（推荐、休息等）
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

try {
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    
    if ($id <= 0) {
        echo json_encode(['success' => false, 'msg' => '无效的技师ID']);
        exit;
    }

    // 检查技师是否存在
    $checkStmt = $pdo->prepare("SELECT id, nick FROM technician WHERE id = ?");
    $checkStmt->execute([$id]);
    $tech = $checkStmt->fetch();
    
    if (!$tech) {
        echo json_encode(['success' => false, 'msg' => '技师不存在']);
        exit;
    }

    // 构建更新字段
    $updateFields = [];
    $updateValues = [];
    
    // 推荐状态
    if (isset($_POST['is_recommended'])) {
        $is_recommended = intval($_POST['is_recommended']);
        $updateFields[] = 'is_recommended = ?';
        $updateValues[] = $is_recommended;
    }
    
    // 休息状态
    if (isset($_POST['is_resting'])) {
        $is_resting = intval($_POST['is_resting']);
        $updateFields[] = 'is_resting = ?';
        $updateValues[] = $is_resting;
    }
    
    // 拉黑状态
    if (isset($_POST['is_black'])) {
        $is_black = intval($_POST['is_black']);
        $updateFields[] = 'is_black = ?';
        $updateValues[] = $is_black;
    }
    
    // 虚拟单量
    if (isset($_POST['virtual_orders'])) {
        $virtual_orders = intval($_POST['virtual_orders']);
        $updateFields[] = 'virtual_orders = ?';
        $updateValues[] = $virtual_orders;
    }
    
    if (empty($updateFields)) {
        echo json_encode(['success' => false, 'msg' => '没有要更新的字段']);
        exit;
    }
    
    // 执行更新
    $updateValues[] = $id; // 添加WHERE条件的ID
    $sql = "UPDATE technician SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute($updateValues);
    
    if ($result) {
        // 构建成功消息
        $actions = [];
        if (isset($_POST['is_recommended'])) {
            $actions[] = $_POST['is_recommended'] == 1 ? '设为推荐' : '取消推荐';
        }
        if (isset($_POST['is_resting'])) {
            $actions[] = $_POST['is_resting'] == 1 ? '设为休息' : '设为工作';
        }
        if (isset($_POST['is_black'])) {
            $actions[] = $_POST['is_black'] == 1 ? '拉黑' : '解除拉黑';
        }
        if (isset($_POST['virtual_orders'])) {
            $actions[] = '更新虚拟单量';
        }
        
        $actionText = implode('、', $actions);
        $msg = "技师 \"{$tech['nick']}\" {$actionText}成功";
        
        echo json_encode([
            'success' => true, 
            'msg' => $msg,
            'data' => [
                'id' => $id,
                'nick' => $tech['nick'],
                'updated_fields' => array_keys($_POST)
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'msg' => '更新失败']);
    }

} catch (Exception $e) {
    error_log("技师状态更新错误: " . $e->getMessage());
    echo json_encode(['success' => false, 'msg' => '系统错误：' . $e->getMessage()]);
}
?>
