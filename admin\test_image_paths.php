<?php
// 测试图片路径和文件存在性
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

try {
    require_once '../db.php';
    
    if (!$pdo) {
        echo json_encode(['success' => false, 'msg' => '数据库连接失败']);
        exit;
    }

    // 查询有生活照的技师
    $stmt = $pdo->query("SELECT id, nick, lifeimg FROM technician WHERE lifeimg IS NOT NULL AND lifeimg != '' ORDER BY id DESC LIMIT 5");
    $techs = $stmt->fetchAll();
    
    $results = [];
    
    foreach ($techs as $tech) {
        $lifeimg_paths = explode(',', $tech['lifeimg']);
        $images = [];
        
        foreach ($lifeimg_paths as $path) {
            $trimmed_path = trim($path);
            if (empty($trimmed_path)) continue;
            
            $full_path = __DIR__ . '/../' . $trimmed_path;
            $web_path = '../' . $trimmed_path;
            
            $images[] = [
                'original_path' => $path,
                'trimmed_path' => $trimmed_path,
                'full_path' => $full_path,
                'web_path' => $web_path,
                'file_exists' => file_exists($full_path),
                'file_size' => file_exists($full_path) ? filesize($full_path) : 0,
                'is_readable' => file_exists($full_path) ? is_readable($full_path) : false
            ];
        }
        
        $results[] = [
            'id' => $tech['id'],
            'nick' => $tech['nick'],
            'lifeimg_raw' => $tech['lifeimg'],
            'images' => $images,
            'image_count' => count($images)
        ];
    }
    
    // 检查 uploads 目录
    $uploads_dir = __DIR__ . '/../uploads/';
    $uploads_info = [
        'path' => $uploads_dir,
        'exists' => is_dir($uploads_dir),
        'readable' => is_readable($uploads_dir),
        'writable' => is_writable($uploads_dir)
    ];
    
    // 列出 uploads 目录中的生活照文件
    $life_files = [];
    if (is_dir($uploads_dir)) {
        $files = scandir($uploads_dir);
        foreach ($files as $file) {
            if (strpos($file, 'life_') === 0) {
                $life_files[] = [
                    'filename' => $file,
                    'size' => filesize($uploads_dir . $file),
                    'modified' => date('Y-m-d H:i:s', filemtime($uploads_dir . $file))
                ];
            }
        }
    }
    
    echo json_encode([
        'success' => true,
        'techs' => $results,
        'uploads_info' => $uploads_info,
        'life_files' => $life_files,
        'life_files_count' => count($life_files)
    ], JSON_PRETTY_PRINT);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'msg' => '查询失败: ' . $e->getMessage()
    ]);
}
?>
