<?php
// banner_detail.php - 获取轮播图详情
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

$bannerFile = __DIR__ . '/banner.json';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

try {
    $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    if ($id <= 0) {
        echo json_encode(['success' => false, 'msg' => '无效的轮播图ID']);
        exit;
    }

    // 读取轮播图数据
    if (!file_exists($bannerFile)) {
        echo json_encode(['success' => false, 'msg' => '轮播图数据文件不存在']);
        exit;
    }

    $banners = json_decode(file_get_contents($bannerFile), true);
    if ($banners === null) {
        echo json_encode(['success' => false, 'msg' => '轮播图数据格式错误']);
        exit;
    }

    // 查找指定ID的轮播图
    foreach ($banners as $banner) {
        if ($banner['id'] == $id) {
            echo json_encode([
                'success' => true,
                'data' => $banner
            ]);
            exit;
        }
    }

    echo json_encode(['success' => false, 'msg' => '轮播图不存在']);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'msg' => '获取失败: ' . $e->getMessage()]);
}
?>
