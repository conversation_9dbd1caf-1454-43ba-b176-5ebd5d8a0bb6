<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>后台登录</title>
  <style>
    body { background:#f7f8fa; font-family:'<PERSON><PERSON>','PingFang SC','Microsoft YaHei',sans-serif; }
    .login-box { max-width:340px; margin:80px auto; background:#fff; border-radius:16px; box-shadow:0 4px 24px rgba(0,0,0,0.10); padding:38px 28px; }
    h2 { text-align:center; color:#222; margin-bottom:28px; }
    .form-group { margin-bottom:22px; }
    label { display:block; color:#888; font-size:15px; margin-bottom:6px; }
    input[type=text], input[type=password] { width:100%; height:40px; border-radius:8px; border:1px solid #eee; padding:0 12px; font-size:16px; }
    button { width:100%; height:44px; background:#2196f3; color:#fff; font-size:17px; font-weight:bold; border:none; border-radius:10px; cursor:pointer; margin-top:12px; }
    .error { color:#ff6a6a; text-align:center; margin-bottom:12px; }
  </style>
</head>
<body>
  <div class="login-box">
    <h2>后台登录</h2>
    <form method="post" action="admin_login.php">
      <div class="form-group">
        <label for="username">账号</label>
        <input type="text" id="username" name="username" required autocomplete="username">
      </div>
      <div class="form-group">
        <label for="password">密码</label>
        <input type="password" id="password" name="password" required autocomplete="current-password">
      </div>
      <button type="submit">登录</button>
      <div class="error" id="login-error" style="display:none;">账号或密码错误</div>
    </form>
  </div>
</body>
<script>
  // 前端判断 URL 参数显示错误提示
  window.addEventListener('DOMContentLoaded', function() {
    var params = new URLSearchParams(window.location.search);
    if(params.get('error')){
      document.getElementById('login-error').style.display = 'block';
    }
  });
</script>
</html>
