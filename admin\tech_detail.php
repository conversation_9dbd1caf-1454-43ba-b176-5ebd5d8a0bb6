<?php
// tech_detail.php - 获取单个技师详细信息

// 禁用错误输出到浏览器，防止HTML错误信息污染JSON响应
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// 开启输出缓冲，确保只输出JSON
ob_start();

session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    ob_clean();
    http_response_code(401);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

// 统一的JSON响应函数
function sendJsonResponse($success, $message, $data = null) {
    ob_clean();
    $response = ['success' => $success, 'msg' => $message];
    if ($data !== null) {
        $response['data'] = $data;
    }
    echo json_encode($response);
    exit;
}

try {
    require_once '../db.php';
    
    if (!$pdo) {
        sendJsonResponse(false, '数据库连接失败');
    }

    $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
    if ($id <= 0) {
        sendJsonResponse(false, '无效的技师ID');
    }

    // 查询技师详细信息
    $stmt = $pdo->prepare("SELECT * FROM technician WHERE id = ?");
    $stmt->execute([$id]);
    $tech = $stmt->fetch();
    
    if (!$tech) {
        sendJsonResponse(false, '技师不存在');
    }

    // 处理图片路径，确保前端能正确显示
    if ($tech['workimg']) {
        $workimg = trim($tech['workimg']);
        // 如果路径不是以 uploads/ 开头，添加前缀
        if (strpos($workimg, 'uploads/') !== 0) {
            // 检查是否只是文件名
            if (strpos($workimg, '/') === false) {
                $tech['workimg'] = 'uploads/' . $workimg;
            } else {
                $tech['workimg'] = 'uploads/' . basename($workimg);
            }
        }
        error_log("工作照路径处理: {$tech['workimg']}");
    }

    if ($tech['lifeimg']) {
        $lifeimgPaths = explode(',', $tech['lifeimg']);
        $processedPaths = [];
        foreach ($lifeimgPaths as $path) {
            $path = trim($path);
            if (!empty($path)) {
                // 如果路径不是以 uploads/ 开头，添加前缀
                if (strpos($path, 'uploads/') !== 0) {
                    // 检查是否只是文件名
                    if (strpos($path, '/') === false) {
                        $path = 'uploads/' . $path;
                    } else {
                        $path = 'uploads/' . basename($path);
                    }
                }
                $processedPaths[] = $path;
            }
        }
        $tech['lifeimg'] = implode(',', $processedPaths);
        error_log("生活照路径处理: {$tech['lifeimg']}");
    }

    if ($tech['video']) {
        $video = trim($tech['video']);
        // 如果路径不是以 uploads/ 开头，添加前缀
        if (strpos($video, 'uploads/') !== 0) {
            // 检查是否只是文件名
            if (strpos($video, '/') === false) {
                $tech['video'] = 'uploads/' . $video;
            } else {
                $tech['video'] = 'uploads/' . basename($video);
            }
        }
        error_log("视频路径处理: {$tech['video']}");
    }

    sendJsonResponse(true, '获取成功', $tech);

} catch (Exception $e) {
    error_log("Tech detail error: " . $e->getMessage());
    sendJsonResponse(false, '获取失败: ' . $e->getMessage());
}
?>
