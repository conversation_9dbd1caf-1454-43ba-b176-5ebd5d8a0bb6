<?php
// 调试技师数据 - 检查生活照字段
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

try {
    require_once '../db.php';
    
    if (!$pdo) {
        echo json_encode(['success' => false, 'msg' => '数据库连接失败']);
        exit;
    }

    $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    if ($id > 0) {
        // 查询特定技师
        $stmt = $pdo->prepare("SELECT id, nick, phone, workimg, lifeimg, video FROM technician WHERE id = ?");
        $stmt->execute([$id]);
        $tech = $stmt->fetch();
        
        if ($tech) {
            // 分析生活照数据
            $lifeimg_analysis = [];
            if ($tech['lifeimg']) {
                $lifeimg_paths = explode(',', $tech['lifeimg']);
                foreach ($lifeimg_paths as $index => $path) {
                    $trimmed_path = trim($path);
                    $lifeimg_analysis[] = [
                        'index' => $index,
                        'original' => $path,
                        'trimmed' => $trimmed_path,
                        'file_exists' => file_exists(__DIR__ . '/../' . $trimmed_path),
                        'full_path' => __DIR__ . '/../' . $trimmed_path
                    ];
                }
            }
            
            echo json_encode([
                'success' => true,
                'tech' => $tech,
                'lifeimg_analysis' => $lifeimg_analysis,
                'uploads_dir_exists' => is_dir(__DIR__ . '/../uploads/'),
                'uploads_dir_readable' => is_readable(__DIR__ . '/../uploads/')
            ], JSON_PRETTY_PRINT);
        } else {
            echo json_encode(['success' => false, 'msg' => '技师不存在']);
        }
    } else {
        // 查询所有有生活照的技师
        $stmt = $pdo->query("SELECT id, nick, phone, workimg, lifeimg, video FROM technician WHERE lifeimg IS NOT NULL AND lifeimg != '' ORDER BY id DESC LIMIT 10");
        $techs = $stmt->fetchAll();
        
        $analysis = [];
        foreach ($techs as $tech) {
            $lifeimg_count = 0;
            $lifeimg_paths = [];
            if ($tech['lifeimg']) {
                $paths = explode(',', $tech['lifeimg']);
                $lifeimg_count = count($paths);
                foreach ($paths as $path) {
                    $trimmed = trim($path);
                    $lifeimg_paths[] = [
                        'path' => $trimmed,
                        'exists' => file_exists(__DIR__ . '/../' . $trimmed)
                    ];
                }
            }
            
            $analysis[] = [
                'id' => $tech['id'],
                'nick' => $tech['nick'],
                'lifeimg_raw' => $tech['lifeimg'],
                'lifeimg_count' => $lifeimg_count,
                'lifeimg_paths' => $lifeimg_paths
            ];
        }
        
        echo json_encode([
            'success' => true,
            'message' => '查询所有有生活照的技师',
            'count' => count($techs),
            'analysis' => $analysis
        ], JSON_PRETTY_PRINT);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'msg' => '查询失败: ' . $e->getMessage()
    ]);
}
?>
