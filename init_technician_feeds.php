<?php
// init_technician_feeds.php - 初始化技师动态表和数据
header('Content-Type: application/json; charset=utf-8');

require_once 'db.php';

try {
    // 创建技师动态表
    $createTableSql = "CREATE TABLE IF NOT EXISTS technician_feeds (
        id INT AUTO_INCREMENT PRIMARY KEY,
        technician_id INT NOT NULL,
        technician_name VARCHAR(100) NOT NULL,
        age INT DEFAULT NULL,
        height INT DEFAULT NULL,
        weight INT DEFAULT NULL,
        service_years INT DEFAULT NULL,
        city VARCHAR(100) NOT NULL,
        previous_city VARCHAR(100) DEFAULT NULL,
        action_type ENUM('join', 'move') NOT NULL DEFAULT 'join',
        content TEXT,
        avatar VARCHAR(255) DEFAULT 'images/tx.jpg',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        is_active TINYINT(1) DEFAULT 1,
        INDEX idx_technician_id (technician_id),
        INDEX idx_city (city),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createTableSql);
    
    // 检查是否有数据
    $checkStmt = $pdo->query("SELECT COUNT(*) FROM technician_feeds");
    $count = $checkStmt->fetchColumn();
    
    $result = [
        'success' => true,
        'message' => '技师动态表初始化成功',
        'table_created' => true,
        'existing_records' => intval($count)
    ];
    
    // 如果没有数据，插入一些示例数据
    if ($count == 0) {
        $sampleFeeds = [
            [
                'technician_id' => 1,
                'technician_name' => '李大宝',
                'age' => 22,
                'height' => 175,
                'weight' => 60,
                'service_years' => 16,
                'city' => '三亚市',
                'action_type' => 'join',
                'content' => '技师李大宝 22/175/60/16 到三亚了'
            ],
            [
                'technician_id' => 2,
                'technician_name' => '王小明',
                'age' => 25,
                'height' => 180,
                'weight' => 70,
                'service_years' => 20,
                'city' => '海口市',
                'action_type' => 'join',
                'content' => '技师王小明 25/180/70/20 到海口了'
            ],
            [
                'technician_id' => 1,
                'technician_name' => '李大宝',
                'age' => 22,
                'height' => 175,
                'weight' => 60,
                'service_years' => 16,
                'city' => '海口市',
                'previous_city' => '三亚市',
                'action_type' => 'move',
                'content' => '技师李大宝 22/175/60/16 到海口了'
            ],
            [
                'technician_id' => 3,
                'technician_name' => '张伟',
                'age' => 28,
                'height' => 178,
                'weight' => 65,
                'service_years' => 25,
                'city' => '儋州市',
                'action_type' => 'join',
                'content' => '技师张伟 28/178/65/25 到儋州了'
            ],
            [
                'technician_id' => 4,
                'technician_name' => '刘强',
                'age' => 24,
                'height' => 172,
                'weight' => 58,
                'service_years' => 18,
                'city' => '琼海市',
                'action_type' => 'join',
                'content' => '技师刘强 24/172/58/18 到琼海了'
            ]
        ];
        
        $insertStmt = $pdo->prepare("
            INSERT INTO technician_feeds 
            (technician_id, technician_name, age, height, weight, service_years, city, previous_city, action_type, content) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $insertedCount = 0;
        foreach ($sampleFeeds as $feed) {
            $success = $insertStmt->execute([
                $feed['technician_id'],
                $feed['technician_name'],
                $feed['age'],
                $feed['height'],
                $feed['weight'],
                $feed['service_years'],
                $feed['city'],
                $feed['previous_city'] ?? null,
                $feed['action_type'],
                $feed['content']
            ]);
            
            if ($success) {
                $insertedCount++;
            }
        }
        
        $result['sample_data_inserted'] = $insertedCount;
        $result['message'] .= "，已插入 {$insertedCount} 条示例数据";
    }
    
    // 验证数据
    $verifyStmt = $pdo->query("SELECT COUNT(*) FROM technician_feeds WHERE is_active = 1");
    $activeCount = $verifyStmt->fetchColumn();
    
    $result['active_records'] = intval($activeCount);
    
    echo json_encode($result);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'message' => '初始化失败'
    ]);
}
?>
