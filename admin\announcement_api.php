<?php
// announcement_api.php - 公告管理API
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

// 创建公告表（如果不存在）
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS announcements (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type VARCHAR(20) NOT NULL COMMENT '公告类型：home首页，tech技师页',
        enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
        content TEXT NOT NULL COMMENT '公告内容',
        scroll_enabled TINYINT(1) DEFAULT 0 COMMENT '是否滚动',
        scroll_speed INT DEFAULT 15 COMMENT '滚动速度（秒）',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_type (type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告管理表'");
} catch (Exception $e) {
    error_log("创建公告表失败: " . $e->getMessage());
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'get':
        // 获取公告
        $type = $_GET['type'] ?? '';
        if (!in_array($type, ['home', 'tech'])) {
            echo json_encode(['success' => false, 'msg' => '无效的公告类型']);
            exit;
        }
        
        try {
            $stmt = $pdo->prepare("SELECT * FROM announcements WHERE type = ?");
            $stmt->execute([$type]);
            $announcement = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($announcement) {
                echo json_encode(['success' => true, 'data' => $announcement]);
            } else {
                // 返回默认值
                $defaultData = [
                    'type' => $type,
                    'enabled' => 1,
                    'content' => $type === 'home' ? '欢迎来到7SPA平台！专业技师为您提供优质服务。' : '选择优质技师，享受专业服务！',
                    'scroll_enabled' => 0,
                    'scroll_speed' => 15
                ];
                echo json_encode(['success' => true, 'data' => $defaultData]);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'msg' => '获取公告失败: ' . $e->getMessage()]);
        }
        break;
        
    case 'save':
        // 保存公告
        $type = $_POST['type'] ?? '';
        $enabled = isset($_POST['enabled']) ? (int)$_POST['enabled'] : 1;
        $content = trim($_POST['content'] ?? '');
        $scroll_enabled = isset($_POST['scroll_enabled']) ? (int)$_POST['scroll_enabled'] : 0;
        $scroll_speed = (int)($_POST['scroll_speed'] ?? 15);
        
        if (!in_array($type, ['home', 'tech'])) {
            echo json_encode(['success' => false, 'msg' => '无效的公告类型']);
            exit;
        }
        
        if (empty($content)) {
            echo json_encode(['success' => false, 'msg' => '公告内容不能为空']);
            exit;
        }
        
        try {
            // 使用 INSERT ... ON DUPLICATE KEY UPDATE
            $stmt = $pdo->prepare("
                INSERT INTO announcements (type, enabled, content, scroll_enabled, scroll_speed) 
                VALUES (?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                enabled = VALUES(enabled),
                content = VALUES(content),
                scroll_enabled = VALUES(scroll_enabled),
                scroll_speed = VALUES(scroll_speed),
                updated_at = CURRENT_TIMESTAMP
            ");
            
            $stmt->execute([$type, $enabled, $content, $scroll_enabled, $scroll_speed]);
            
            echo json_encode(['success' => true, 'msg' => '保存成功']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'msg' => '保存失败: ' . $e->getMessage()]);
        }
        break;
        
    case 'toggle':
        // 切换公告启用状态
        $type = $_POST['type'] ?? '';
        $enabled = isset($_POST['enabled']) ? (int)$_POST['enabled'] : 1;
        
        if (!in_array($type, ['home', 'tech'])) {
            echo json_encode(['success' => false, 'msg' => '无效的公告类型']);
            exit;
        }
        
        try {
            $stmt = $pdo->prepare("
                INSERT INTO announcements (type, enabled, content) 
                VALUES (?, ?, '')
                ON DUPLICATE KEY UPDATE 
                enabled = VALUES(enabled),
                updated_at = CURRENT_TIMESTAMP
            ");
            
            $stmt->execute([$type, $enabled]);
            
            $status = $enabled ? '启用' : '禁用';
            echo json_encode(['success' => true, 'msg' => "公告已{$status}"]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'msg' => '操作失败: ' . $e->getMessage()]);
        }
        break;
        
    default:
        echo json_encode(['success' => false, 'msg' => '无效的操作']);
        break;
}
?>
