<?php
// announcement.php - 前端获取公告API
header('Content-Type: application/json; charset=utf-8');
require_once 'db.php';

$type = $_GET['type'] ?? '';

if (!in_array($type, ['home', 'tech'])) {
    echo json_encode(['success' => false, 'msg' => '无效的公告类型']);
    exit;
}

try {
    $stmt = $pdo->prepare("SELECT * FROM announcements WHERE type = ? AND enabled = 1");
    $stmt->execute([$type]);
    $announcement = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($announcement) {
        echo json_encode([
            'success' => true,
            'data' => [
                'enabled' => (bool)$announcement['enabled'],
                'content' => $announcement['content'],
                'scroll_enabled' => (bool)$announcement['scroll_enabled'],
                'scroll_speed' => (int)$announcement['scroll_speed']
            ]
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'data' => [
                'enabled' => false,
                'content' => '',
                'scroll_enabled' => false,
                'scroll_speed' => 15
            ]
        ]);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'msg' => '获取公告失败: ' . $e->getMessage()]);
}
?>
