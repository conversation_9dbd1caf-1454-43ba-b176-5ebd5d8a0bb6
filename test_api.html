<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API测试页面</h1>
        
        <div class="test-section">
            <h3>服务项目API测试</h3>
            <button onclick="testServiceAPI()">测试 service_list.php</button>
            <div id="service-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>技师列表API测试</h3>
            <button onclick="testTechAPI()">测试 tech_list.php</button>
            <div id="tech-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>数据库连接测试</h3>
            <button onclick="testDBConnection()">测试数据库连接</button>
            <div id="db-result" class="result"></div>
        </div>
    </div>

    <script>
        function testServiceAPI() {
            const resultDiv = document.getElementById('service-result');
            resultDiv.textContent = '正在测试...';
            resultDiv.className = 'result';
            
            fetch('service_list.php')
                .then(response => {
                    console.log('Service API Response Status:', response.status);
                    return response.json();
                })
                .then(data => {
                    resultDiv.textContent = JSON.stringify(data, null, 2);
                    resultDiv.className = 'result success';
                })
                .catch(error => {
                    resultDiv.textContent = '错误: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function testTechAPI() {
            const resultDiv = document.getElementById('tech-result');
            resultDiv.textContent = '正在测试...';
            resultDiv.className = 'result';
            
            fetch('tech_list.php')
                .then(response => {
                    console.log('Tech API Response Status:', response.status);
                    return response.json();
                })
                .then(data => {
                    resultDiv.textContent = JSON.stringify(data, null, 2);
                    resultDiv.className = 'result success';
                })
                .catch(error => {
                    resultDiv.textContent = '错误: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function testDBConnection() {
            const resultDiv = document.getElementById('db-result');
            resultDiv.textContent = '正在测试数据库连接...';
            resultDiv.className = 'result';
            
            // 通过tech_list.php来间接测试数据库连接
            fetch('tech_list.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success !== undefined) {
                        resultDiv.textContent = '数据库连接成功！\n返回数据: ' + JSON.stringify(data, null, 2);
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.textContent = '数据库连接可能有问题\n返回数据: ' + JSON.stringify(data, null, 2);
                        resultDiv.className = 'result error';
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '数据库连接失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
    </script>
</body>
</html>
