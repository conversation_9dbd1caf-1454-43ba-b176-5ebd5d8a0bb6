<?php
require_once 'db.php';

try {
    echo "=== 检查技师拉黑状态 ===\n";
    
    // 查看所有已通过的技师及其拉黑状态
    $stmt = $pdo->query("SELECT id, nick, phone, status, is_black, last_visit FROM technician WHERE status = 'approved' OR status = 1 ORDER BY id");
    $techs = $stmt->fetchAll();
    
    if (empty($techs)) {
        echo "没有已通过的技师\n";
    } else {
        echo "已通过的技师列表:\n";
        foreach ($techs as $tech) {
            $blackStatus = $tech['is_black'] == 1 ? '已拉黑' : '正常';
            echo "ID: {$tech['id']}, 昵称: {$tech['nick']}, 手机: {$tech['phone']}, 拉黑状态: {$blackStatus}, 最后访问: {$tech['last_visit']}\n";
        }
    }
    
    // 统计拉黑状态
    $stmt = $pdo->query("SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN is_black = 1 THEN 1 ELSE 0 END) as blacklisted,
        SUM(CASE WHEN is_black = 0 THEN 1 ELSE 0 END) as normal
        FROM technician WHERE status = 'approved' OR status = 1");
    $stats = $stmt->fetch();
    
    echo "\n统计信息:\n";
    echo "总计: {$stats['total']}个已通过技师\n";
    echo "正常: {$stats['normal']}个\n";
    echo "已拉黑: {$stats['blacklisted']}个\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
