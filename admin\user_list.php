<?php
ob_clean(); // 清理输出缓冲，防止BOM或多余内容
error_reporting(0); // 关闭错误输出
ini_set('display_errors', 0);
require_once '../db.php';
header('Content-Type: application/json; charset=utf-8');
// 自动建表（用PDO）
$pdo->exec("CREATE TABLE IF NOT EXISTS user (
  id INT AUTO_INCREMENT PRIMARY KEY,
  nickname VARCHAR(64),
  avatar VARCHAR(255),
  mobile VARCHAR(32),
  level VARCHAR(32),
  total_amount DECIMAL(10,2),
  balance DECIMAL(10,2),
  source VARCHAR(64),
  region VARCHAR(128),
  last_visit DATETIME,
  created_at DATETIME,
  is_black TINYINT(1) DEFAULT 0
) CHARSET=utf8mb4;");


if (!$pdo) {
  echo json_encode([]); // 数据库连接失败时返回空数组
  exit;
}

try {
  $stmt = $pdo->query("SELECT
    id,
    nickname,
    avatar,
    phone AS mobile,
    password,
    password_plain,
    '' AS level,
    0.00 AS total_amount,
    0.00 AS balance,
    '' AS source,
    '' AS region,
    last_visit,
    created_at,
    is_black
  FROM user ORDER BY id DESC");

  if ($stmt) {
    $data = $stmt->fetchAll();

    // 处理数据，确保所有字段都有值
    $data = array_map(function($row) {
      // 处理昵称
      if (empty($row['nickname'])) {
        $row['nickname'] = '未设置昵称';
      }

      // 处理头像路径
      if (empty($row['avatar'])) {
        $row['avatar'] = 'images/tx.jpg'; // 默认头像
      } else {
        // 确保头像路径正确（兼容PHP 7.x）
        if (strpos($row['avatar'], 'images/') !== 0 && strpos($row['avatar'], '/') !== 0) {
          $row['avatar'] = 'images/' . $row['avatar'];
        }
      }

      // 处理时间格式
      if (!empty($row['last_visit'])) {
        $row['last_visit'] = date('Y-m-d H:i:s', strtotime($row['last_visit']));
      } else {
        $row['last_visit'] = '从未访问';
      }

      if (!empty($row['created_at'])) {
        $row['created_at'] = date('Y-m-d H:i:s', strtotime($row['created_at']));
      }

      // 确保所有字段都存在
      $fields = ['id','nickname','avatar','mobile','password','password_plain','level','total_amount','balance','source','region','last_visit','created_at','is_black'];
      foreach($fields as $f){
        if(!isset($row[$f])) $row[$f] = '';
      }

      return $row;
    }, $data);

    echo json_encode($data, JSON_UNESCAPED_UNICODE);
  } else {
    echo json_encode([]);
  }
} catch (Exception $e) {
  // 输出错误信息用于调试
  error_log("User list query error: " . $e->getMessage());
  echo json_encode([]); // 查询异常时返回空数组
}
exit;

