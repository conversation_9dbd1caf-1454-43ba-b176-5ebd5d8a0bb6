<?php
// 项目介绍文章接口
header('Content-Type: application/json; charset=utf-8');

require_once '../db.php';
// 兼容 mysqli 连接
$host = 'localhost';
$user = 'root';
$pass = '';
$dbname = '7spa';
$conn = new mysqli($host, $user, $pass, $dbname);
if ($conn->connect_error) {
    echo json_encode(['success'=>false, 'msg'=>'数据库连接失败']);
    exit;
}

define('TABLE', 'project_article');
$create_sql = "CREATE TABLE IF NOT EXISTS `".TABLE."` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `title` VARCHAR(50) NOT NULL,
  `content` TEXT NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
$conn->query($create_sql);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $content = trim($_POST['content'] ?? '');
    if ($title === '' || $content === '') {
        echo json_encode(['success'=>false, 'msg'=>'标题和内容不能为空']);
        exit;
    }
    $check = $conn->query("SELECT id FROM `".TABLE."` LIMIT 1");
    if ($row = $check->fetch_assoc()) {
        $stmt = $conn->prepare("UPDATE `".TABLE."` SET title=?, content=?, updated_at=NOW() WHERE id=?");
        $stmt->bind_param('ssi', $title, $content, $row['id']);
        $stmt->execute();
    } else {
        $stmt = $conn->prepare("INSERT INTO `".TABLE."` (title, content) VALUES (?, ?)");
        $stmt->bind_param('ss', $title, $content);
        $stmt->execute();
    }
    echo json_encode(['success'=>true, 'msg'=>'保存成功']);
    exit;
}
$res = $conn->query("SELECT title, content FROM `".TABLE."` ORDER BY updated_at DESC LIMIT 1");
if ($row = $res->fetch_assoc()) {
    echo json_encode(['success'=>true, 'data'=>['title'=>$row['title'], 'content'=>$row['content']]]);
} else {
    echo json_encode(['success'=>true, 'data'=>['title'=>'', 'content'=>'']]);
}
