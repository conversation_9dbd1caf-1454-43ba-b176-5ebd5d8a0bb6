<?php
// city_set_hot.php - 设置城市热门状态
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

// 获取原始输入数据
$rawInput = file_get_contents('php://input');
$input = json_decode($rawInput, true);

// 如果JSON解析失败，尝试解析表单数据
if ($input === null) {
    $input = $_POST;
}

$cityId = $input['id'] ?? null;
$isHot = $input['is_hot'] ?? 0;

if (!$cityId) {
    echo json_encode([
        'success' => false,
        'msg' => '城市ID不能为空'
    ]);
    exit;
}

try {
    // 检查城市是否存在
    $checkStmt = $pdo->prepare("SELECT id, name, is_hot FROM city WHERE id = ?");
    $checkStmt->execute([$cityId]);
    $city = $checkStmt->fetch();
    
    if (!$city) {
        echo json_encode(['success' => false, 'msg' => '城市不存在']);
        exit;
    }
    
    // 检查表中是否有updated_at字段
    $columnsStmt = $pdo->query("SHOW COLUMNS FROM city LIKE 'updated_at'");
    $hasUpdatedAt = $columnsStmt->fetch() !== false;

    // 根据字段存在情况构建更新SQL
    if ($hasUpdatedAt) {
        $updateStmt = $pdo->prepare("UPDATE city SET is_hot = ?, updated_at = NOW() WHERE id = ?");
    } else {
        $updateStmt = $pdo->prepare("UPDATE city SET is_hot = ? WHERE id = ?");
    }

    $result = $updateStmt->execute([$isHot, $cityId]);
    
    if ($result) {
        // 获取更新后的城市信息
        $checkStmt->execute([$cityId]);
        $updatedCity = $checkStmt->fetch();
        
        echo json_encode([
            'success' => true,
            'msg' => $isHot ? '已设为热门城市' : '已取消热门城市',
            'data' => [
                'id' => $updatedCity['id'],
                'name' => $updatedCity['name'],
                'is_hot' => $updatedCity['is_hot'],
                'status_text' => $updatedCity['is_hot'] ? '热门城市' : '普通城市'
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'msg' => '更新失败']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'msg' => '操作失败: ' . $e->getMessage()]);
}
?>
