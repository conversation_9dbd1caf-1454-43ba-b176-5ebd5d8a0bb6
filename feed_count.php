<?php
// feed_count.php
header('Content-Type: application/json; charset=utf-8');
require_once 'db.php';
$phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
if (!$phone) {
    echo json_encode(['success'=>false, 'msg'=>'缺少参数']);
    exit;
}
// 查询 feed 表，统计该手机号发布的动态数量
$sql = "SELECT COUNT(*) as num FROM feed WHERE phone = ?";
$stmt = $pdo->prepare($sql);
$stmt->execute([$phone]);
$row = $stmt->fetch(PDO::FETCH_ASSOC);
$num = $row ? intval($row['num']) : 0;
echo json_encode(['success'=>true, 'num'=>$num]);
