<?php
// feed_count.php

// 禁用错误输出到浏览器，防止HTML错误信息污染JSON响应
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// 开启输出缓冲，确保只输出JSON
ob_start();

header('Content-Type: application/json; charset=utf-8');

// 统一的JSON响应函数
function sendJsonResponse($success, $message, $data = null) {
    ob_clean();
    $response = ['success' => $success, 'msg' => $message];
    if ($data !== null) {
        $response = array_merge($response, $data);
    }
    echo json_encode($response);
    exit;
}

// 注册致命错误处理器
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '服务器内部错误', 'num' => 0]);
        exit;
    }
});

try {
    require_once 'db.php';

    // 检查数据库连接
    if ($pdo === null) {
        sendJsonResponse(false, '数据库连接失败', ['num' => 0]);
    }

    $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    if (!$phone) {
        sendJsonResponse(false, '缺少参数', ['num' => 0]);
    }

    // 查询 feed 表，统计该手机号发布的动态数量
    $sql = "SELECT COUNT(*) as num FROM feed WHERE phone = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$phone]);
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    $num = $row ? intval($row['num']) : 0;

    sendJsonResponse(true, '获取成功', ['num' => $num]);

} catch (Exception $e) {
    error_log("Feed count error: " . $e->getMessage());
    sendJsonResponse(false, '获取动态数量失败，请稍后重试', ['num' => 0]);
}
