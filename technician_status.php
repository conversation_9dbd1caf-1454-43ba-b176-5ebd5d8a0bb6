<?php
// technician_status.php - 获取技师申请状态和资料
header('Content-Type: application/json; charset=utf-8');
require_once 'db.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

try {
    $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    
    if (!$phone) {
        echo json_encode(['success' => false, 'msg' => '手机号不能为空']);
        exit;
    }

    // 验证手机号格式
    if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
        echo json_encode(['success' => false, 'msg' => '手机号格式不正确']);
        exit;
    }

    // 查询技师申请信息
    $stmt = $pdo->prepare("SELECT * FROM technician WHERE phone = ?");
    $stmt->execute([$phone]);
    $technician = $stmt->fetch();
    
    if (!$technician) {
        echo json_encode([
            'success' => true,
            'data' => null,
            'msg' => '未找到申请记录'
        ]);
        exit;
    }

    // 格式化状态文本
    $statusText = '';
    switch ($technician['status']) {
        case 'pending':
        case 0:
            $statusText = '申请中';
            break;
        case 'approved':
        case 1:
            $statusText = '已通过';
            break;
        case 'rejected':
        case 2:
            $statusText = '已驳回';
            break;
        default:
            $statusText = '未知状态';
    }

    // 处理图片路径
    $workimg = $technician['workimg'];
    $lifeimg = $technician['lifeimg'];
    $video = $technician['video'];

    // 确保图片路径正确
    if ($workimg && strpos($workimg, 'http') !== 0 && strpos($workimg, '/') !== 0) {
        $workimg = $workimg;
    }

    if ($video && strpos($video, 'http') !== 0 && strpos($video, '/') !== 0) {
        $video = $video;
    }

    // 处理生活照（可能是逗号分隔的多张图片）
    $lifeimg_array = [];
    if ($lifeimg) {
        $lifeimg_array = array_filter(array_map('trim', explode(',', $lifeimg)));
    }

    // 返回技师信息
    echo json_encode([
        'success' => true,
        'data' => [
            'id' => $technician['id'],
            'nick' => $technician['nick'],
            'phone' => $technician['phone'],
            'age' => $technician['age'],
            'height' => $technician['height'],
            'weight' => $technician['weight'],
            'size' => $technician['size'],
            'model' => $technician['model'],
            'city' => $technician['city'],
            'hotel' => $technician['hotel'],
            'workimg' => $workimg,
            'lifeimg' => $lifeimg,
            'lifeimg_array' => $lifeimg_array,
            'video' => $video,
            'status' => $technician['status'],
            'status_text' => $statusText,
            'apply_time' => $technician['apply_time'],
            'reject_reason' => $technician['reject_reason'] ?? ''
        ],
        'msg' => '获取成功'
    ]);

} catch (Exception $e) {
    error_log("Technician status error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'msg' => '获取申请状态失败: ' . $e->getMessage()
    ]);
}
?>
