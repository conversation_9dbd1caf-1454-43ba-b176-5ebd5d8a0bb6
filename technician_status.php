<?php
// technician_status.php - 获取技师申请状态和资料

// 禁用错误输出到浏览器，防止HTML错误信息污染JSON响应
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// 开启输出缓冲，确保只输出JSON
ob_start();

header('Content-Type: application/json; charset=utf-8');

// 统一的JSON响应函数
function sendJsonResponse($success, $message, $data = null) {
    ob_clean();
    $response = ['success' => $success, 'msg' => $message];
    if ($data !== null) {
        $response = array_merge($response, $data);
    }
    echo json_encode($response);
    exit;
}

// 注册致命错误处理器
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '服务器内部错误']);
        exit;
    }
});

try {
    require_once 'db.php';

    // 检查数据库连接
    if ($pdo === null) {
        sendJsonResponse(false, '数据库连接失败');
    }
} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    sendJsonResponse(false, '数据库连接失败');
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendJsonResponse(false, '请求方法错误');
}

try {
    $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    
    if (!$phone) {
        sendJsonResponse(false, '手机号不能为空');
    }

    // 验证手机号格式
    if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
        sendJsonResponse(false, '手机号格式不正确');
    }

    // 查询技师申请信息
    $stmt = $pdo->prepare("SELECT * FROM technician WHERE phone = ?");
    $stmt->execute([$phone]);
    $technician = $stmt->fetch();
    
    if (!$technician) {
        sendJsonResponse(true, '未找到申请记录', ['data' => null]);
    }

    // 格式化状态文本
    $statusText = '';
    switch ($technician['status']) {
        case 'pending':
        case 0:
            $statusText = '申请中';
            break;
        case 'approved':
        case 1:
            $statusText = '已通过';
            break;
        case 'rejected':
        case 2:
            $statusText = '已驳回';
            break;
        default:
            $statusText = '未知状态';
    }

    // 处理图片路径
    $workimg = $technician['workimg'];
    $lifeimg = $technician['lifeimg'];
    $video = $technician['video'];

    // 确保图片路径正确
    if ($workimg && strpos($workimg, 'http') !== 0 && strpos($workimg, '/') !== 0) {
        $workimg = $workimg;
    }

    if ($video && strpos($video, 'http') !== 0 && strpos($video, '/') !== 0) {
        $video = $video;
    }

    // 处理生活照（可能是逗号分隔的多张图片）
    $lifeimg_array = [];
    if ($lifeimg) {
        $lifeimg_array = array_filter(array_map('trim', explode(',', $lifeimg)));
    }

    // 返回技师信息
    sendJsonResponse(true, '获取成功', [
        'data' => [
            'id' => $technician['id'],
            'nick' => $technician['nick'],
            'phone' => $technician['phone'],
            'age' => $technician['age'],
            'height' => $technician['height'],
            'weight' => $technician['weight'],
            'size' => $technician['size'],
            'model' => $technician['model'],
            'city' => $technician['city'],
            'hotel' => $technician['hotel'],
            'workimg' => $workimg,
            'lifeimg' => $lifeimg,
            'lifeimg_array' => $lifeimg_array,
            'video' => $video,
            'status' => $technician['status'],
            'status_text' => $statusText,
            'apply_time' => $technician['apply_time'],
            'reject_reason' => $technician['reject_reason'] ?? ''
        ]
    ]);

} catch (Exception $e) {
    error_log("Technician status error: " . $e->getMessage());
    sendJsonResponse(false, '获取申请状态失败，请稍后重试');
}
?>
