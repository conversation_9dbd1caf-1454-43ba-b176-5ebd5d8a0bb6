<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技师城市变更完整测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .btn.warning:hover {
            background: #e0a800;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .tech-info {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
            margin: 15px 0;
        }
        .tech-info h4 {
            margin-top: 0;
            color: #007bff;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .info-label {
            font-weight: bold;
            color: #555;
        }
        .info-value {
            color: #333;
        }
        .city-selector {
            margin: 15px 0;
        }
        .city-selector select {
            width: 200px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .feed-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px;
            margin: 10px 0;
            font-size: 14px;
        }
        .feed-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        .feed-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #ddd;
        }
        .feed-name {
            font-weight: bold;
            color: #333;
        }
        .feed-time {
            font-size: 11px;
            color: #999;
        }
        .feed-content {
            color: #333;
            margin: 8px 0;
        }
        .feed-details {
            background: #e3f2fd;
            padding: 6px 8px;
            border-radius: 4px;
            font-size: 11px;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 技师城市变更完整测试</h1>
        
        <div class="section">
            <h3>📋 测试说明</h3>
            <p style="color: #666; line-height: 1.6;">
                此页面用于完整测试技师城市变更时自动生成动态的功能。
                包括获取技师信息、模拟城市变更、查看生成的动态等步骤。
            </p>
        </div>
        
        <div class="section">
            <h3>👤 测试技师信息</h3>
            <div id="tech-info-container">
                <div class="loading">点击"获取技师信息"加载</div>
            </div>
            <div style="margin-top: 15px;">
                <button class="btn" onclick="loadTechInfo()">📋 获取技师信息</button>
                <button class="btn warning" onclick="resetTechCity()">🔄 重置到赣州市</button>
            </div>
        </div>
        
        <div class="section">
            <h3>🏙️ 城市变更测试</h3>
            <div class="city-selector">
                <label for="new-city-select" style="display: block; margin-bottom: 8px; font-weight: bold;">选择新城市：</label>
                <select id="new-city-select">
                    <option value="">请选择城市</option>
                    <option value="三亚市">三亚市</option>
                    <option value="海口市">海口市</option>
                    <option value="儋州市">儋州市</option>
                    <option value="琼海市">琼海市</option>
                    <option value="文昌市">文昌市</option>
                    <option value="万宁市">万宁市</option>
                    <option value="赣州市">赣州市</option>
                </select>
            </div>
            <div style="margin-top: 15px;">
                <button class="btn success" onclick="changeTechCity()">🚀 执行城市变更</button>
                <button class="btn" onclick="checkLatestFeeds()">📱 查看最新动态</button>
            </div>
            <div id="change-result"></div>
        </div>
        
        <div class="section">
            <h3>📱 生成的技师动态</h3>
            <div id="feeds-container">
                <div class="loading">点击"查看最新动态"加载</div>
            </div>
            <div style="margin-top: 15px;">
                <button class="btn" onclick="openFeedPage()">🔗 打开动态页面</button>
                <button class="btn warning" onclick="clearTestData()">🗑️ 清理测试数据</button>
            </div>
        </div>
    </div>

    <script>
        let currentTech = null;
        
        // 页面加载时自动获取技师信息
        document.addEventListener('DOMContentLoaded', function() {
            loadTechInfo();
        });
        
        // 获取技师信息
        async function loadTechInfo() {
            const container = document.getElementById('tech-info-container');
            container.innerHTML = '<div class="loading">正在获取技师信息...</div>';
            
            try {
                const response = await fetch('test_tech_city_change_api.php');
                const data = await response.json();
                
                if (data.success) {
                    currentTech = data.technician || null;
                    
                    if (data.action === 'created') {
                        container.innerHTML = `
                            <div class="success">✅ 测试技师创建成功！</div>
                            <div class="info">技师ID: ${data.technician_id}，请刷新页面获取详细信息。</div>
                        `;
                        setTimeout(loadTechInfo, 1000);
                    } else if (currentTech) {
                        container.innerHTML = `
                            <div class="tech-info">
                                <h4>技师：${currentTech.name}</h4>
                                <div class="info-row">
                                    <span class="info-label">ID:</span>
                                    <span class="info-value">${currentTech.id}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">手机号:</span>
                                    <span class="info-value">${currentTech.phone}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">年龄:</span>
                                    <span class="info-value">${currentTech.age}岁</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">身高:</span>
                                    <span class="info-value">${currentTech.height}cm</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">体重:</span>
                                    <span class="info-value">${currentTech.weight}kg</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">尺寸:</span>
                                    <span class="info-value">${currentTech.size}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">当前城市:</span>
                                    <span class="info-value" style="color: #007bff; font-weight: bold;">${currentTech.city}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">酒店:</span>
                                    <span class="info-value">${currentTech.hotel}</span>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    container.innerHTML = `<div class="error">❌ 获取技师信息失败: ${data.error}</div>`;
                }
            } catch (error) {
                container.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        // 执行城市变更
        async function changeTechCity() {
            const newCitySelect = document.getElementById('new-city-select');
            const newCity = newCitySelect.value;
            const resultContainer = document.getElementById('change-result');
            
            if (!newCity) {
                resultContainer.innerHTML = '<div class="error">❌ 请选择新城市</div>';
                return;
            }
            
            if (!currentTech) {
                resultContainer.innerHTML = '<div class="error">❌ 请先获取技师信息</div>';
                return;
            }
            
            if (currentTech.city === newCity) {
                resultContainer.innerHTML = `<div class="info">ℹ️ 技师已经在${newCity}，无需变更</div>`;
                return;
            }
            
            resultContainer.innerHTML = '<div class="loading">正在执行城市变更...</div>';
            
            try {
                const response = await fetch('test_tech_city_change_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        new_city: newCity
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultContainer.innerHTML = `
                        <div class="success">
                            ✅ 城市变更成功！<br>
                            • 技师：${data.technician_name}<br>
                            • 原城市：${data.old_city}<br>
                            • 新城市：${data.new_city}<br>
                            • 已自动生成技师动态
                        </div>
                    `;
                    
                    // 刷新技师信息
                    setTimeout(() => {
                        loadTechInfo();
                        checkLatestFeeds();
                    }, 500);
                } else {
                    resultContainer.innerHTML = `<div class="error">❌ 城市变更失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultContainer.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        // 查看最新动态
        async function checkLatestFeeds() {
            const container = document.getElementById('feeds-container');
            container.innerHTML = '<div class="loading">正在加载最新动态...</div>';
            
            try {
                const response = await fetch('technician_feed_list.php?limit=5');
                const data = await response.json();
                
                if (data.success && data.feeds && data.feeds.length > 0) {
                    container.innerHTML = data.feeds.map(feed => `
                        <div class="feed-preview">
                            <div class="feed-header">
                                <img src="${feed.avatar}" alt="头像" class="feed-avatar" onerror="this.style.background='#ddd'">
                                <div>
                                    <div class="feed-name">${feed.nickname}</div>
                                    <div class="feed-time">${feed.time} · ${feed.location}</div>
                                </div>
                            </div>
                            <div class="feed-content">${feed.content}</div>
                            ${feed.technician_info ? `
                                <div class="feed-details">
                                    <strong>${feed.technician_info.action_type === 'move' ? '更换城市' : '新加入'}</strong> 
                                    ${feed.technician_info.city}
                                    ${feed.technician_info.previous_city ? ` (从${feed.technician_info.previous_city})` : ''}<br>
                                    年龄:${feed.technician_info.age} | 身高:${feed.technician_info.height}cm | 
                                    体重:${feed.technician_info.weight}kg | 服务年限:${feed.technician_info.service_years}年
                                </div>
                            ` : ''}
                        </div>
                    `).join('');
                } else {
                    container.innerHTML = '<div class="loading">暂无技师动态</div>';
                }
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败：${error.message}</div>`;
            }
        }
        
        // 重置技师城市到赣州市
        async function resetTechCity() {
            const resultContainer = document.getElementById('change-result');
            resultContainer.innerHTML = '<div class="loading">正在重置城市...</div>';
            
            try {
                const response = await fetch('test_tech_city_change_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        new_city: '赣州市'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultContainer.innerHTML = `
                        <div class="success">✅ 已重置到赣州市，可以重新测试城市变更</div>
                    `;
                    setTimeout(loadTechInfo, 500);
                } else {
                    resultContainer.innerHTML = `<div class="info">ℹ️ ${data.message}</div>`;
                }
            } catch (error) {
                resultContainer.innerHTML = `<div class="error">❌ 重置失败: ${error.message}</div>`;
            }
        }
        
        // 打开动态页面
        function openFeedPage() {
            window.open('feed.html', '_blank');
        }
        
        // 清理测试数据
        async function clearTestData() {
            if (!confirm('确定要清理测试数据吗？这将删除测试技师的所有动态记录。')) {
                return;
            }
            
            // 这里可以添加清理测试数据的API调用
            alert('清理功能待实现，请手动清理数据库中的测试数据。');
        }
    </script>
</body>
</html>
