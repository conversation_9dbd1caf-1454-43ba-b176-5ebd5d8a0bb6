/**
 * 后台管理系统 - 通用工具函数
 */

// 全局变量
window.AdminCommon = {
  // 缓存数据
  cache: {
    users: [],
    techs: [],
    cities: [],
    services: [],
    feeds: [],
    banners: []
  },

  // API请求封装
  api: {
    get: function(url) {
      // 添加时间戳避免缓存
      const separator = url.includes('?') ? '&' : '?';
      const urlWithTimestamp = url + separator + '_t=' + Date.now();
      return fetch(urlWithTimestamp).then(response => response.json());
    },
    
    post: function(url, data) {
      const formData = new FormData();
      if (typeof data === 'object') {
        for (const key in data) {
          formData.append(key, data[key]);
        }
      }
      return fetch(url, {
        method: 'POST',
        body: formData
      }).then(response => response.json());
    },

    postForm: function(url, formData) {
      return fetch(url, {
        method: 'POST',
        body: formData
      }).then(response => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // 获取响应文本
        return response.text().then(text => {
          try {
            // 尝试解析为JSON
            return JSON.parse(text);
          } catch (e) {
            // 如果不是有效的JSON，记录原始响应并抛出错误
            console.error('响应不是有效的JSON:', text);
            throw new Error('服务器返回了无效的响应格式');
          }
        });
      }).catch(error => {
        console.error('API请求失败:', error);
        throw error;
      });
    },

    postJSON: function(url, data) {
      return fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams(data)
      }).then(response => {
        return response.text().then(text => {
          try {
            return JSON.parse(text);
          } catch (e) {
            console.error('响应不是有效的JSON:', text);
            throw new Error('服务器返回了无效的响应格式');
          }
        });
      });
    },

    postRawJSON: function(url, data) {
      return fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      }).then(response => response.json());
    }
  },

  // 通用UI工具
  ui: {
    // 显示加载状态
    showLoading: function(element, text = '加载中...') {
      if (typeof element === 'string') {
        element = document.getElementById(element);
      }
      if (element) {
        element.innerHTML = `<div class="text-center text-muted">${text}</div>`;
      }
    },

    // 显示错误信息
    showError: function(element, message = '加载失败') {
      if (typeof element === 'string') {
        element = document.getElementById(element);
      }
      if (element) {
        element.innerHTML = `<div class="text-center" style="color: #f44336;">${message}</div>`;
      }
    },

    // 显示空状态
    showEmpty: function(element, message = '暂无数据') {
      if (typeof element === 'string') {
        element = document.getElementById(element);
      }
      if (element) {
        element.innerHTML = `<div class="text-center text-muted">${message}</div>`;
      }
    },

    // 确认对话框
    confirm: function(message, callback) {
      if (window.confirm(message)) {
        callback();
      }
    },

    // 提示对话框
    alert: function(message, type = 'info') {
      // 简单的alert，可以后续扩展为更美观的提示框
      window.alert(message);
    },

    // 输入对话框
    prompt: function(message, defaultValue = '') {
      return window.prompt(message, defaultValue);
    }
  },

  // 数据格式化工具
  format: {
    // 格式化日期
    date: function(dateString) {
      if (!dateString) return '未知';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString;
      return date.toLocaleString('zh-CN');
    },

    // 格式化最后访问时间
    lastVisit: function(dateString) {
      if (!dateString || dateString === '从未访问' || dateString === 'null' || dateString === 'NULL') {
        return '<span style="color: #999;">从未访问</span>';
      }
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return '<span style="color: #999;">从未访问</span>';
      }
      return date.toLocaleString('zh-CN');
    },

    // 格式化状态
    status: function(status, type = 'tech') {
      const statusMap = {
        tech: {
          0: { text: '申请中', class: 'status-pending' },
          1: { text: '已通过', class: 'status-approved' },
          2: { text: '已驳回', class: 'status-rejected' },
          3: { text: '重新审核', class: 'status-review' },
          'pending': { text: '申请中', class: 'status-pending' },
          'approved': { text: '已通过', class: 'status-approved' },
          'rejected': { text: '已驳回', class: 'status-rejected' },
          'review': { text: '重新审核', class: 'status-review' }
        }
      };
      
      const map = statusMap[type] || statusMap.tech;
      const statusInfo = map[status] || { text: '未知', class: '' };
      return `<span class="${statusInfo.class}">${statusInfo.text}</span>`;
    },

    // 处理图片路径
    imagePath: function(path, defaultPath = 'images/tx.jpg') {
      if (!path || !path.trim()) {
        return '../' + defaultPath;
      }
      
      const trimmedPath = path.trim();
      if (trimmedPath.startsWith('images/')) {
        return '../' + trimmedPath;
      } else if (trimmedPath.startsWith('/w7/images/')) {
        return '..' + trimmedPath.substring(3);
      } else if (trimmedPath.startsWith('/images/')) {
        return '..' + trimmedPath;
      } else if (trimmedPath.startsWith('uploads/')) {
        return '../' + trimmedPath;
      } else if (trimmedPath.startsWith('/w7/uploads/')) {
        return '..' + trimmedPath.substring(3);
      } else if (trimmedPath.startsWith('/uploads/')) {
        return '..' + trimmedPath;
      } else {
        return '../images/' + trimmedPath;
      }
    }
  },

  // 模态框工具
  modal: {
    // 显示模态框
    show: function(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'flex';
      }
    },

    // 隐藏模态框
    hide: function(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'none';
      }
    },

    // 创建模态框
    create: function(id, title, content, options = {}) {
      const existingModal = document.getElementById(id);
      if (existingModal) {
        existingModal.remove();
      }

      const modal = document.createElement('div');
      modal.id = id;
      modal.className = 'modal';
      modal.innerHTML = `
        <div class="modal-content" style="max-width: ${options.width || '500px'};">
          <div class="modal-header">
            <h3 style="margin: 0;">${title}</h3>
            <button type="button" class="modal-close" onclick="AdminCommon.modal.hide('${id}')">&times;</button>
          </div>
          <div class="modal-body">
            ${content}
          </div>
        </div>
      `;

      document.body.appendChild(modal);
      
      // 点击背景关闭
      modal.addEventListener('click', function(e) {
        if (e.target === modal) {
          AdminCommon.modal.hide(id);
        }
      });

      return modal;
    }
  },

  // 表格工具
  table: {
    // 渲染表格
    render: function(containerId, data, columns, options = {}) {
      const container = document.getElementById(containerId);
      if (!container) return;

      if (!data || data.length === 0) {
        AdminCommon.ui.showEmpty(container, options.emptyText || '暂无数据');
        return;
      }

      const table = document.createElement('table');
      table.className = 'table';

      // 表头
      const thead = document.createElement('thead');
      const headerRow = document.createElement('tr');
      columns.forEach(col => {
        const th = document.createElement('th');
        th.textContent = col.title;
        if (col.width) th.style.width = col.width;
        headerRow.appendChild(th);
      });
      thead.appendChild(headerRow);
      table.appendChild(thead);

      // 表体
      const tbody = document.createElement('tbody');
      data.forEach(row => {
        const tr = document.createElement('tr');
        columns.forEach(col => {
          const td = document.createElement('td');
          if (col.render) {
            td.innerHTML = col.render(row[col.key], row);
          } else {
            td.textContent = row[col.key] || '';
          }
          tr.appendChild(td);
        });
        tbody.appendChild(tr);
      });
      table.appendChild(tbody);

      container.innerHTML = '';
      container.appendChild(table);
    }
  },

  // 分页工具
  pagination: {
    // 渲染分页组件
    render: function(containerId, currentPage, totalPages, onPageChange) {
      const container = document.getElementById(containerId);
      if (!container) return;

      if (totalPages <= 1) {
        container.innerHTML = '';
        return;
      }

      let paginationHtml = '<div class="pagination-container" style="display: flex; justify-content: center; align-items: center; margin: 20px 0; gap: 8px;">';

      // 上一页按钮
      if (currentPage > 1) {
        paginationHtml += `<button class="btn btn-secondary" onclick="${onPageChange}(${currentPage - 1})" style="padding: 6px 12px; font-size: 14px;">上一页</button>`;
      }

      // 页码按钮
      const startPage = Math.max(1, currentPage - 2);
      const endPage = Math.min(totalPages, currentPage + 2);

      if (startPage > 1) {
        paginationHtml += `<button class="btn btn-secondary" onclick="${onPageChange}(1)" style="padding: 6px 12px; font-size: 14px;">1</button>`;
        if (startPage > 2) {
          paginationHtml += '<span style="padding: 6px 12px; color: #666;">...</span>';
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        const isActive = i === currentPage;
        const btnClass = isActive ? 'btn-primary' : 'btn-secondary';
        paginationHtml += `<button class="btn ${btnClass}" onclick="${onPageChange}(${i})" style="padding: 6px 12px; font-size: 14px;">${i}</button>`;
      }

      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          paginationHtml += '<span style="padding: 6px 12px; color: #666;">...</span>';
        }
        paginationHtml += `<button class="btn btn-secondary" onclick="${onPageChange}(${totalPages})" style="padding: 6px 12px; font-size: 14px;">${totalPages}</button>`;
      }

      // 下一页按钮
      if (currentPage < totalPages) {
        paginationHtml += `<button class="btn btn-secondary" onclick="${onPageChange}(${currentPage + 1})" style="padding: 6px 12px; font-size: 14px;">下一页</button>`;
      }

      // 页面信息
      paginationHtml += `<span style="margin-left: 16px; color: #666; font-size: 14px;">第 ${currentPage} 页，共 ${totalPages} 页</span>`;

      paginationHtml += '</div>';

      container.innerHTML = paginationHtml;
    },

    // 计算分页数据
    paginate: function(data, page, pageSize) {
      const totalItems = data.length;
      const totalPages = Math.ceil(totalItems / pageSize);
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pageData = data.slice(startIndex, endIndex);

      return {
        data: pageData,
        currentPage: page,
        totalPages: totalPages,
        totalItems: totalItems,
        pageSize: pageSize
      };
    }
  }
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('后台管理系统已加载');
});
