<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .btn { 
            border: none; border-radius: 6px; padding: 8px 16px; 
            font-size: 14px; cursor: pointer; margin: 2px;
            transition: background-color 0.2s;
        }
        .btn-primary { background: #2196f3; color: #fff; }
        .btn-primary:hover { background: #1976d2; }
        .btn-secondary { background: #6c757d; color: #fff; }
        .btn-secondary:hover { background: #5a6268; }
        .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .table th, .table td { padding: 12px 8px; text-align: left; border-bottom: 1px solid #eee; }
        .table th { background: #f8f9fa; font-weight: 500; color: #666; }
        .pagination-container { margin: 20px 0; }
        .test-data { margin: 20px 0; }
    </style>
</head>
<body>
    <h1>技师管理分页功能测试</h1>
    
    <div class="test-data">
        <h3>测试数据</h3>
        <p>模拟25个技师数据，每页显示10个，共3页</p>
        <button class="btn btn-primary" onclick="generateTestData()">生成测试数据</button>
        <button class="btn btn-secondary" onclick="testPagination()">测试分页</button>
    </div>

    <div id="tech-list-container">
        <div style="text-align: center; color: #666;">点击"生成测试数据"开始测试</div>
    </div>
    
    <div id="tech-pagination-container"></div>

    <script>
        // 模拟AdminCommon对象
        window.AdminCommon = {
            table: {
                render: function(containerId, data, columns) {
                    const container = document.getElementById(containerId);
                    if (!container) return;

                    if (!data || data.length === 0) {
                        container.innerHTML = '<div style="text-align: center; color: #666;">暂无数据</div>';
                        return;
                    }

                    const table = document.createElement('table');
                    table.className = 'table';

                    // 表头
                    const thead = document.createElement('thead');
                    const headerRow = document.createElement('tr');
                    columns.forEach(col => {
                        const th = document.createElement('th');
                        th.textContent = col.title;
                        if (col.width) th.style.width = col.width;
                        headerRow.appendChild(th);
                    });
                    thead.appendChild(headerRow);
                    table.appendChild(thead);

                    // 表体
                    const tbody = document.createElement('tbody');
                    data.forEach(row => {
                        const tr = document.createElement('tr');
                        columns.forEach(col => {
                            const td = document.createElement('td');
                            if (col.render) {
                                td.innerHTML = col.render(row[col.key], row);
                            } else {
                                td.textContent = row[col.key] || '';
                            }
                            tr.appendChild(td);
                        });
                        tbody.appendChild(tr);
                    });
                    table.appendChild(tbody);

                    container.innerHTML = '';
                    container.appendChild(table);
                }
            },
            pagination: {
                render: function(containerId, currentPage, totalPages, onPageChange) {
                    const container = document.getElementById(containerId);
                    if (!container) return;

                    if (totalPages <= 1) {
                        container.innerHTML = '';
                        return;
                    }

                    let paginationHtml = '<div class="pagination-container" style="display: flex; justify-content: center; align-items: center; margin: 20px 0; gap: 8px;">';
                    
                    // 上一页按钮
                    if (currentPage > 1) {
                        paginationHtml += `<button class="btn btn-secondary" onclick="${onPageChange}(${currentPage - 1})" style="padding: 6px 12px; font-size: 14px;">上一页</button>`;
                    }

                    // 页码按钮
                    const startPage = Math.max(1, currentPage - 2);
                    const endPage = Math.min(totalPages, currentPage + 2);

                    if (startPage > 1) {
                        paginationHtml += `<button class="btn btn-secondary" onclick="${onPageChange}(1)" style="padding: 6px 12px; font-size: 14px;">1</button>`;
                        if (startPage > 2) {
                            paginationHtml += '<span style="padding: 6px 12px; color: #666;">...</span>';
                        }
                    }

                    for (let i = startPage; i <= endPage; i++) {
                        const isActive = i === currentPage;
                        const btnClass = isActive ? 'btn-primary' : 'btn-secondary';
                        paginationHtml += `<button class="btn ${btnClass}" onclick="${onPageChange}(${i})" style="padding: 6px 12px; font-size: 14px;">${i}</button>`;
                    }

                    if (endPage < totalPages) {
                        if (endPage < totalPages - 1) {
                            paginationHtml += '<span style="padding: 6px 12px; color: #666;">...</span>';
                        }
                        paginationHtml += `<button class="btn btn-secondary" onclick="${onPageChange}(${totalPages})" style="padding: 6px 12px; font-size: 14px;">${totalPages}</button>`;
                    }

                    // 下一页按钮
                    if (currentPage < totalPages) {
                        paginationHtml += `<button class="btn btn-secondary" onclick="${onPageChange}(${currentPage + 1})" style="padding: 6px 12px; font-size: 14px;">下一页</button>`;
                    }

                    // 页面信息
                    paginationHtml += `<span style="margin-left: 16px; color: #666; font-size: 14px;">第 ${currentPage} 页，共 ${totalPages} 页</span>`;
                    
                    paginationHtml += '</div>';

                    container.innerHTML = paginationHtml;
                },

                paginate: function(data, page, pageSize) {
                    const totalItems = data.length;
                    const totalPages = Math.ceil(totalItems / pageSize);
                    const startIndex = (page - 1) * pageSize;
                    const endIndex = startIndex + pageSize;
                    const pageData = data.slice(startIndex, endIndex);

                    return {
                        data: pageData,
                        currentPage: page,
                        totalPages: totalPages,
                        totalItems: totalItems,
                        pageSize: pageSize
                    };
                }
            }
        };

        // 测试数据
        let testTechs = [];
        let currentPage = 1;
        const pageSize = 10;

        function generateTestData() {
            testTechs = [];
            for (let i = 1; i <= 25; i++) {
                testTechs.push({
                    id: i,
                    nick: `技师${i}`,
                    phone: `1380000${String(i).padStart(4, '0')}`,
                    age: 20 + (i % 15),
                    city: ['北京', '上海', '广州', '深圳', '杭州'][i % 5],
                    status: [0, 1, 2][i % 3],
                    virtual_orders: Math.floor(Math.random() * 100)
                });
            }
            console.log('生成了', testTechs.length, '个测试技师数据');
            renderTestList();
        }

        function renderTestList() {
            const paginationResult = AdminCommon.pagination.paginate(testTechs, currentPage, pageSize);
            
            const columns = [
                { key: 'id', title: 'ID', width: '60px' },
                { key: 'nick', title: '昵称' },
                { key: 'phone', title: '手机号' },
                { key: 'age', title: '年龄' },
                { key: 'city', title: '城市' },
                { 
                    key: 'status', 
                    title: '状态',
                    render: (value) => {
                        const statusMap = { 0: '申请中', 1: '已通过', 2: '已驳回' };
                        return statusMap[value] || '未知';
                    }
                },
                { key: 'virtual_orders', title: '虚拟单量' }
            ];

            AdminCommon.table.render('tech-list-container', paginationResult.data, columns);
            AdminCommon.pagination.render('tech-pagination-container', paginationResult.currentPage, paginationResult.totalPages, 'changePage');
        }

        function changePage(page) {
            currentPage = page;
            renderTestList();
            console.log('切换到第', page, '页');
        }

        function testPagination() {
            if (testTechs.length === 0) {
                alert('请先生成测试数据');
                return;
            }
            
            console.log('开始分页测试...');
            console.log('总数据:', testTechs.length);
            console.log('每页显示:', pageSize);
            console.log('总页数:', Math.ceil(testTechs.length / pageSize));
            console.log('当前页:', currentPage);
            
            // 测试各页数据
            for (let i = 1; i <= Math.ceil(testTechs.length / pageSize); i++) {
                const result = AdminCommon.pagination.paginate(testTechs, i, pageSize);
                console.log(`第${i}页数据:`, result.data.length, '条记录');
            }
        }
    </script>
</body>
</html>
