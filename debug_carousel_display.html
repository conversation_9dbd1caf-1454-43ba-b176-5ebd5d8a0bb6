<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图显示调试</title>
    <style>
        body {
            margin: 0;
            font-family: '<PERSON><PERSON>', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: #222;
            min-height: 100vh;
            position: relative;
        }
        
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            max-width: 300px;
            z-index: 1000;
            font-family: monospace;
        }
        
        .debug-panel h4 {
            margin: 0 0 10px 0;
            color: #00c6a2;
        }
        
        /* 完全复制feed.html的轮播图样式 */
        .carousel-container {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
            border-radius: 0 0 18px 18px;
            background: #fff;
            border: 1px solid #eee;
        }
        
        .carousel-wrapper {
            display: flex;
            width: 100%;
            height: 100%;
            transition: transform 0.5s ease-in-out;
        }
        
        .carousel-slide {
            min-width: 100%;
            width: 100%;
            height: 100%;
            position: relative;
            flex-shrink: 0;
        }
        
        .carousel-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
            border: none;
            outline: none;
        }
        
        .carousel-indicators {
            position: absolute;
            bottom: 12px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
            z-index: 10;
        }
        
        .carousel-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .carousel-indicator.active {
            background: rgba(255, 255, 255, 0.9);
            transform: scale(1.2);
        }
        
        .carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.3);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10;
        }
        
        .carousel-nav:hover {
            background: rgba(0, 0, 0, 0.6);
            transform: translateY(-50%) scale(1.1);
        }
        
        .carousel-nav.prev {
            left: 12px;
        }
        
        .carousel-nav.next {
            right: 12px;
        }
        
        .carousel-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            background: #f5f5f5;
            color: #999;
            font-size: 14px;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .controls {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin: 2px;
            font-size: 12px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .measurement {
            position: absolute;
            background: rgba(255,0,0,0.3);
            border: 1px solid red;
            color: red;
            font-size: 10px;
            padding: 2px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <!-- 完全复制feed.html的轮播图结构 -->
    <div style="width:100%; background:#fff; border-radius:0 0 18px 18px; box-shadow:0 2px 8px rgba(0,0,0,0.04); margin-bottom: 20px;">
      <div id="carousel" class="carousel-container">
        <!-- 轮播图内容将通过JavaScript动态加载 -->
        <div class="carousel-loading">
          <div style="display: flex; align-items: center; justify-content: center; height: 200px; color: #999;">
            <div style="display: flex; align-items: center; gap: 8px;">
              <div style="width: 16px; height: 16px; border: 2px solid #ddd; border-top: 2px solid #00c6a2; border-radius: 50%; animation: spin 1s linear infinite;"></div>
              正在加载轮播图...
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 调试面板 -->
    <div class="debug-panel">
        <h4>🔍 轮播图调试</h4>
        <div id="debug-info">初始化中...</div>
    </div>
    
    <!-- 控制面板 -->
    <div class="controls">
        <div style="margin-bottom: 10px; font-weight: bold;">轮播图控制</div>
        <button class="btn" onclick="loadCarousel()">🔄 加载</button>
        <button class="btn" onclick="showMeasurements()">📏 测量</button>
        <button class="btn" onclick="testImages()">🖼️ 测试图片</button>
        <button class="btn" onclick="toggleDebug()">🐛 调试</button>
        <br>
        <button class="btn" onclick="prevSlide()">⬅️</button>
        <button class="btn" onclick="nextSlide()">➡️</button>
        <button class="btn" onclick="toggleAutoPlay()">⏸️</button>
    </div>

    <script>
        let currentSlideIndex = 0;
        let totalSlides = 0;
        let autoPlayInterval = null;
        let isAutoPlaying = false;
        let debugMode = false;
        
        // 更新调试信息
        function updateDebugInfo() {
            const carousel = document.getElementById('carousel');
            const wrapper = document.querySelector('.carousel-wrapper');
            
            const info = {
                容器尺寸: carousel ? `${carousel.offsetWidth}x${carousel.offsetHeight}px` : '未找到',
                容器位置: carousel ? `${carousel.offsetLeft},${carousel.offsetTop}` : '未找到',
                包装器尺寸: wrapper ? `${wrapper.offsetWidth}x${wrapper.offsetHeight}px` : '未找到',
                当前幻灯片: `${currentSlideIndex + 1}/${totalSlides}`,
                自动播放: isAutoPlaying ? '开启' : '关闭',
                变换位置: wrapper ? wrapper.style.transform : '无'
            };
            
            document.getElementById('debug-info').innerHTML = Object.entries(info)
                .map(([key, value]) => `${key}: ${value}`)
                .join('<br>');
        }
        
        // 加载轮播图
        async function loadCarousel() {
            console.log('开始加载轮播图');
            
            try {
                const response = await fetch('banner_list.php');
                const data = await response.json();
                
                console.log('API响应:', data);
                
                if (data.success && data.data && data.data.length > 0) {
                    renderCarousel(data.data);
                } else {
                    showEmptyState();
                }
            } catch (error) {
                console.error('加载失败:', error);
                showErrorState();
            }
            
            updateDebugInfo();
        }
        
        // 渲染轮播图（完全复制feed.html的逻辑）
        function renderCarousel(slides) {
            const carousel = document.getElementById('carousel');
            totalSlides = slides.length;
            currentSlideIndex = 0;
            
            const html = `
                <div class="carousel-wrapper" style="display: flex; width: 100%; height: 100%;">
                    ${slides.map((slide, index) => `
                        <div class="carousel-slide" style="min-width: 100%; height: 100%; flex-shrink: 0;">
                            <img src="${slide.img}" alt="轮播图${index + 1}" 
                                 style="width: 100%; height: 100%; object-fit: cover; display: block;"
                                 onerror="this.src='images/lbt.png'"
                                 onload="console.log('图片${index + 1}加载成功:', '${slide.img}'); updateDebugInfo();" />
                        </div>
                    `).join('')}
                </div>
                
                ${slides.length > 1 ? `
                    <button class="carousel-nav prev" onclick="prevSlide()">‹</button>
                    <button class="carousel-nav next" onclick="nextSlide()">›</button>
                    
                    <div class="carousel-indicators" style="position: absolute; bottom: 12px; left: 50%; transform: translateX(-50%); display: flex; gap: 8px; z-index: 10;">
                        ${slides.map((_, index) => `
                            <div class="carousel-indicator ${index === 0 ? 'active' : ''}" 
                                 style="width: 8px; height: 8px; border-radius: 50%; background: rgba(255, 255, 255, ${index === 0 ? '0.9' : '0.5'}); cursor: pointer; transition: all 0.3s ease;"
                                 onclick="goToSlide(${index})"></div>
                        `).join('')}
                    </div>
                ` : ''}
            `;
            
            carousel.innerHTML = html;
            console.log('轮播图渲染完成，数量:', slides.length);
            
            if (slides.length > 1) {
                startAutoPlay();
            }
            
            updateDebugInfo();
        }
        
        // 显示空状态
        function showEmptyState() {
            const carousel = document.getElementById('carousel');
            carousel.innerHTML = `
                <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 200px; color: #999;">
                    <div style="font-size: 48px; margin-bottom: 12px;">🖼️</div>
                    <div>暂无轮播图</div>
                </div>
            `;
        }
        
        // 显示错误状态
        function showErrorState() {
            const carousel = document.getElementById('carousel');
            carousel.innerHTML = `
                <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 200px; color: #999;">
                    <div style="font-size: 48px; margin-bottom: 12px;">⚠️</div>
                    <div>轮播图加载失败</div>
                </div>
            `;
        }
        
        // 轮播图控制功能
        function goToSlide(index) {
            if (index < 0 || index >= totalSlides) return;
            
            currentSlideIndex = index;
            updateSlidePosition();
            updateIndicators();
            updateDebugInfo();
            
            if (isAutoPlaying) {
                startAutoPlay();
            }
        }
        
        function nextSlide() {
            if (totalSlides <= 1) return;
            const nextIndex = (currentSlideIndex + 1) % totalSlides;
            goToSlide(nextIndex);
        }
        
        function prevSlide() {
            if (totalSlides <= 1) return;
            const prevIndex = (currentSlideIndex - 1 + totalSlides) % totalSlides;
            goToSlide(prevIndex);
        }
        
        function updateSlidePosition() {
            const wrapper = document.querySelector('.carousel-wrapper');
            if (wrapper) {
                const translateX = -currentSlideIndex * 100;
                wrapper.style.transform = `translateX(${translateX}%)`;
            }
        }
        
        function updateIndicators() {
            const indicators = document.querySelectorAll('.carousel-indicator');
            indicators.forEach((indicator, index) => {
                if (index === currentSlideIndex) {
                    indicator.style.background = 'rgba(255, 255, 255, 0.9)';
                    indicator.style.transform = 'scale(1.2)';
                    indicator.classList.add('active');
                } else {
                    indicator.style.background = 'rgba(255, 255, 255, 0.5)';
                    indicator.style.transform = 'scale(1)';
                    indicator.classList.remove('active');
                }
            });
        }
        
        function startAutoPlay() {
            if (totalSlides <= 1) return;
            
            stopAutoPlay();
            isAutoPlaying = true;
            autoPlayInterval = setInterval(() => {
                nextSlide();
            }, 4000);
            updateDebugInfo();
        }
        
        function stopAutoPlay() {
            if (autoPlayInterval) {
                clearInterval(autoPlayInterval);
                autoPlayInterval = null;
            }
            isAutoPlaying = false;
            updateDebugInfo();
        }
        
        function toggleAutoPlay() {
            if (isAutoPlaying) {
                stopAutoPlay();
            } else {
                startAutoPlay();
            }
        }
        
        // 显示测量信息
        function showMeasurements() {
            // 清除之前的测量
            document.querySelectorAll('.measurement').forEach(el => el.remove());
            
            const carousel = document.getElementById('carousel');
            const wrapper = document.querySelector('.carousel-wrapper');
            const slides = document.querySelectorAll('.carousel-slide');
            
            if (carousel) {
                addMeasurement(carousel, '容器', 'red');
            }
            
            if (wrapper) {
                addMeasurement(wrapper, '包装器', 'blue');
            }
            
            slides.forEach((slide, index) => {
                addMeasurement(slide, `幻灯片${index + 1}`, 'green');
            });
        }
        
        function addMeasurement(element, label, color) {
            const rect = element.getBoundingClientRect();
            const measurement = document.createElement('div');
            measurement.className = 'measurement';
            measurement.style.cssText = `
                position: fixed;
                left: ${rect.left}px;
                top: ${rect.top}px;
                width: ${rect.width}px;
                height: ${rect.height}px;
                border: 2px solid ${color};
                background: rgba(255,255,255,0.1);
                color: ${color};
                font-size: 10px;
                padding: 2px;
                pointer-events: none;
                z-index: 1000;
            `;
            measurement.textContent = `${label}: ${Math.round(rect.width)}x${Math.round(rect.height)}`;
            document.body.appendChild(measurement);
        }
        
        // 测试图片加载
        function testImages() {
            const testUrls = [
                'uploads/banner_20250718_200312_9681.jpg',
                'uploads/banner_20250718_223358_2505.jpg',
                'uploads/banner_20250718_223404_6006.jpg',
                'uploads/banner_20250718_223412_6801.jpg'
            ];
            
            testUrls.forEach((url, index) => {
                const img = new Image();
                img.onload = () => console.log(`✅ 图片${index + 1}加载成功: ${url} (${img.naturalWidth}x${img.naturalHeight})`);
                img.onerror = () => console.log(`❌ 图片${index + 1}加载失败: ${url}`);
                img.src = url;
            });
        }
        
        function toggleDebug() {
            debugMode = !debugMode;
            if (debugMode) {
                setInterval(updateDebugInfo, 1000);
            }
        }
        
        // 页面加载时自动初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('调试页面加载完成');
            updateDebugInfo();
            
            setTimeout(() => {
                loadCarousel();
            }, 500);
        });
    </script>
</body>
</html>
