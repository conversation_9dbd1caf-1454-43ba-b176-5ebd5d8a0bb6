<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技师卡片测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .technician-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
            max-width: 600px;
            margin: 0 auto;
        }

        .technician-card {
            background: white;
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 16px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .technician-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .tech-avatar {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            flex-shrink: 0;
        }

        .tech-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .tech-badge {
            position: absolute;
            bottom: -2px;
            left: -2px;
            background: #ff6b35;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: bold;
        }

        .tech-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .tech-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .tech-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .tech-distance {
            color: #999;
            font-size: 14px;
        }

        .tech-info-details {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 8px;
        }

        .tech-detail-item {
            display: flex;
            align-items: center;
            gap: 4px;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 12px;
        }

        .detail-label {
            color: #666;
            font-weight: 500;
        }

        .detail-value {
            color: #333;
            font-weight: bold;
        }

        .tech-tags {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .tech-tag {
            background: #f0f0f0;
            color: #666;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .tech-orders-tag {
            background: #e8f5e8;
            color: #2e7d32;
            font-weight: 500;
        }

        .tech-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .tech-status {
            display: flex;
            align-items: center;
        }

        .status-available {
            background: #a8e6cf;
            color: #2e7d32;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }



        .service-btn {
            background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
            color: #2e7d32;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .service-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(168, 230, 207, 0.4);
        }

        .recommended {
            border: 2px solid #ff6b35;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <h1>技师卡片显示效果测试</h1>
    
    <div class="technician-list">
        <!-- 测试卡片1 -->
        <div class="technician-card recommended">
            <div class="tech-avatar">
                <div style="width:100%;height:100%;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);display:flex;align-items:center;justify-content:center;color:white;font-size:24px;">👤</div>
                <div class="tech-badge">特别推荐</div>
            </div>
            <div class="tech-info">
                <div class="tech-header">
                    <div>
                        <div class="tech-name">小美</div>
                        <div class="tech-info-details">
                            <div class="tech-detail-item">
                                <span class="detail-label">年纪:</span>
                                <span class="detail-value">22</span>
                            </div>
                            <div class="tech-detail-item">
                                <span class="detail-label">身高:</span>
                                <span class="detail-value">165</span>
                            </div>
                            <div class="tech-detail-item">
                                <span class="detail-label">体重:</span>
                                <span class="detail-value">50</span>
                            </div>
                            <div class="tech-detail-item">
                                <span class="detail-label">尺寸:</span>
                                <span class="detail-value">34C</span>
                            </div>
                        </div>
                    </div>
                    <div class="tech-distance">&lt;1km</div>
                </div>
                
                <div class="tech-tags">
                    <div class="tech-tag">
                        <span>📍</span>
                        <span>三亚市</span>
                    </div>
                    <div class="tech-tag tech-orders-tag">
                        <span>📊</span>
                        <span>单量 128</span>
                    </div>
                </div>

                <div class="tech-footer">
                    <div class="tech-status">
                        <div class="status-available">最早可约 今14:30</div>
                    </div>
                    <button class="service-btn">可服务</button>
                </div>
            </div>
        </div>

        <!-- 测试卡片2 -->
        <div class="technician-card">
            <div class="tech-avatar">
                <div style="width:100%;height:100%;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);display:flex;align-items:center;justify-content:center;color:white;font-size:24px;">👤</div>
            </div>
            <div class="tech-info">
                <div class="tech-header">
                    <div>
                        <div class="tech-name">小丽</div>
                        <div class="tech-info-details">
                            <div class="tech-detail-item">
                                <span class="detail-label">年纪:</span>
                                <span class="detail-value">25</span>
                            </div>
                            <div class="tech-detail-item">
                                <span class="detail-label">身高:</span>
                                <span class="detail-value">168</span>
                            </div>
                            <div class="tech-detail-item">
                                <span class="detail-label">体重:</span>
                                <span class="detail-value">52</span>
                            </div>
                            <div class="tech-detail-item">
                                <span class="detail-label">尺寸:</span>
                                <span class="detail-value">36D</span>
                            </div>
                        </div>
                    </div>
                    <div class="tech-distance">&lt;2km</div>
                </div>
                
                <div class="tech-tags">
                    <div class="tech-tag">
                        <span>📍</span>
                        <span>海口市</span>
                    </div>
                    <div class="tech-tag tech-orders-tag">
                        <span>📊</span>
                        <span>单量 95</span>
                    </div>
                </div>

                <div class="tech-footer">
                    <div class="tech-status">
                        <div class="status-available">最早可约 今15:00</div>
                    </div>
                    <button class="service-btn">可服务</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
