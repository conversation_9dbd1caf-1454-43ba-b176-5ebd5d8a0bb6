<?php
// fix_image_paths.php - 修复数据库中的图片路径

session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo "未登录";
    exit;
}

header('Content-Type: text/html; charset=utf-8');

try {
    require_once '../db.php';
    
    if (!$pdo) {
        echo "数据库连接失败";
        exit;
    }

    echo "<h2>修复技师图片路径</h2>";
    echo "<style>
        .log { font-family: monospace; background: #f8f9fa; padding: 10px; margin: 10px 0; border-left: 4px solid #007bff; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>";

    // 获取所有技师数据
    $stmt = $pdo->prepare("SELECT id, nick, workimg, lifeimg, video FROM technician");
    $stmt->execute();
    $techs = $stmt->fetchAll();
    
    echo "<div class='log'>找到 " . count($techs) . " 个技师记录</div>";
    
    $updatedCount = 0;
    
    foreach ($techs as $tech) {
        $needUpdate = false;
        $newWorkimg = $tech['workimg'];
        $newLifeimg = $tech['lifeimg'];
        $newVideo = $tech['video'];
        
        echo "<div class='log'>";
        echo "<strong>技师 ID: {$tech['id']} ({$tech['nick']})</strong><br>";
        
        // 处理工作照
        if ($tech['workimg']) {
            $workimg = trim($tech['workimg']);
            if (strpos($workimg, 'uploads/') !== 0) {
                if (strpos($workimg, '/') === false) {
                    $newWorkimg = 'uploads/' . $workimg;
                } else {
                    $newWorkimg = 'uploads/' . basename($workimg);
                }
                echo "<span class='warning'>工作照: {$tech['workimg']} → $newWorkimg</span><br>";
                $needUpdate = true;
            } else {
                echo "工作照: {$tech['workimg']} (无需修改)<br>";
            }
        }
        
        // 处理生活照
        if ($tech['lifeimg']) {
            $lifeimgPaths = explode(',', $tech['lifeimg']);
            $processedPaths = [];
            $lifeimgChanged = false;
            
            foreach ($lifeimgPaths as $path) {
                $path = trim($path);
                if (!empty($path)) {
                    if (strpos($path, 'uploads/') !== 0) {
                        if (strpos($path, '/') === false) {
                            $newPath = 'uploads/' . $path;
                        } else {
                            $newPath = 'uploads/' . basename($path);
                        }
                        $processedPaths[] = $newPath;
                        $lifeimgChanged = true;
                        echo "<span class='warning'>生活照: $path → $newPath</span><br>";
                    } else {
                        $processedPaths[] = $path;
                        echo "生活照: $path (无需修改)<br>";
                    }
                }
            }
            
            if ($lifeimgChanged) {
                $newLifeimg = implode(',', $processedPaths);
                $needUpdate = true;
            }
        }
        
        // 处理视频
        if ($tech['video']) {
            $video = trim($tech['video']);
            if (strpos($video, 'uploads/') !== 0) {
                if (strpos($video, '/') === false) {
                    $newVideo = 'uploads/' . $video;
                } else {
                    $newVideo = 'uploads/' . basename($video);
                }
                echo "<span class='warning'>视频: {$tech['video']} → $newVideo</span><br>";
                $needUpdate = true;
            } else {
                echo "视频: {$tech['video']} (无需修改)<br>";
            }
        }
        
        // 更新数据库
        if ($needUpdate) {
            $updateStmt = $pdo->prepare("UPDATE technician SET workimg = ?, lifeimg = ?, video = ? WHERE id = ?");
            $result = $updateStmt->execute([$newWorkimg, $newLifeimg, $newVideo, $tech['id']]);
            
            if ($result) {
                echo "<span class='success'>✓ 更新成功</span><br>";
                $updatedCount++;
            } else {
                echo "<span class='error'>✗ 更新失败</span><br>";
            }
        } else {
            echo "无需更新<br>";
        }
        
        echo "</div>";
    }
    
    echo "<div class='log'>";
    echo "<strong>修复完成！</strong><br>";
    echo "总共处理: " . count($techs) . " 个技师<br>";
    echo "更新了: $updatedCount 个技师的路径<br>";
    echo "</div>";
    
    // 验证修复结果
    echo "<h3>验证修复结果</h3>";
    $verifyStmt = $pdo->prepare("SELECT id, nick, workimg, lifeimg, video FROM technician WHERE workimg NOT LIKE 'uploads/%' OR lifeimg NOT LIKE '%uploads/%' OR video NOT LIKE 'uploads/%'");
    $verifyStmt->execute();
    $problemRecords = $verifyStmt->fetchAll();
    
    if (count($problemRecords) == 0) {
        echo "<div class='log'><span class='success'>✓ 所有路径都已正确格式化</span></div>";
    } else {
        echo "<div class='log'><span class='warning'>⚠ 仍有 " . count($problemRecords) . " 个记录可能存在路径问题</span></div>";
        foreach ($problemRecords as $record) {
            echo "<div class='log'>";
            echo "ID: {$record['id']}, 昵称: {$record['nick']}<br>";
            echo "工作照: {$record['workimg']}<br>";
            echo "生活照: {$record['lifeimg']}<br>";
            echo "视频: {$record['video']}<br>";
            echo "</div>";
        }
    }

} catch (Exception $e) {
    echo "<div class='log'><span class='error'>错误: " . $e->getMessage() . "</span></div>";
}
?>

<br><br>
<a href="javascript:history.back()">返回</a>
<script>
// 自动刷新调试页面
setTimeout(function() {
    if (confirm('路径修复完成，是否刷新调试页面查看结果？')) {
        window.location.href = 'debug_tech_images.php';
    }
}, 2000);
</script>
