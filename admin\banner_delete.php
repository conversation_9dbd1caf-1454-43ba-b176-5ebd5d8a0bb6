<?php
// banner_delete.php - 删除轮播图
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

$bannerFile = __DIR__ . '/banner.json';

try {
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;

    if ($id <= 0) {
        echo json_encode(['success' => false, 'msg' => '无效的轮播图ID']);
        exit;
    }

    // 读取现有轮播图数据
    if (!file_exists($bannerFile)) {
        echo json_encode(['success' => false, 'msg' => '轮播图数据文件不存在']);
        exit;
    }

    $banners = json_decode(file_get_contents($bannerFile), true);
    if ($banners === null) {
        echo json_encode(['success' => false, 'msg' => '轮播图数据格式错误']);
        exit;
    }

    // 查找要删除的轮播图
    $targetBanner = null;
    $newBanners = [];

    foreach ($banners as $banner) {
        if ($banner['id'] == $id) {
            $targetBanner = $banner;
        } else {
            $newBanners[] = $banner;
        }
    }

    if ($targetBanner === null) {
        echo json_encode(['success' => false, 'msg' => '轮播图不存在']);
        exit;
    }

    // 删除图片文件
    if (isset($targetBanner['img'])) {
        // 处理新旧两种路径格式
        $imgPath = $targetBanner['img'];
        if (strpos($imgPath, '/w7/') === 0) {
            // 旧格式：/w7/uploads/xxx.jpg
            $imagePath = __DIR__ . '/../' . ltrim($imgPath, '/w7/');
        } else {
            // 新格式：uploads/xxx.jpg
            $imagePath = __DIR__ . '/../' . $imgPath;
        }
        if (file_exists($imagePath)) {
            unlink($imagePath);
        }
    }

    // 保存更新后的数据
    if (file_put_contents($bannerFile, json_encode($newBanners, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES)) === false) {
        echo json_encode(['success' => false, 'msg' => '保存数据失败']);
        exit;
    }

    echo json_encode(['success' => true, 'msg' => '轮播图删除成功']);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'msg' => '删除失败: ' . $e->getMessage()]);
}
?>