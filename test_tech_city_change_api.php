<?php
// test_tech_city_change_api.php - 测试技师城市变更API
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'db.php';

try {
    // 创建测试技师（如果不存在）
    $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM technician WHERE phone = ?");
    $checkStmt->execute(['13800138000']);
    $exists = $checkStmt->fetchColumn();
    
    if ($exists == 0) {
        // 插入测试技师
        $insertStmt = $pdo->prepare("
            INSERT INTO technician (nick, phone, age, height, weight, size, city, hotel, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        $insertStmt->execute([
            '李大宝',
            '13800138000', 
            22,
            175,
            60,
            16,
            '赣州市',
            '测试酒店'
        ]);
        $techId = $pdo->lastInsertId();
        echo json_encode([
            'success' => true,
            'message' => '测试技师创建成功',
            'technician_id' => $techId,
            'action' => 'created'
        ]);
    } else {
        // 获取现有技师信息
        $techStmt = $pdo->prepare("SELECT * FROM technician WHERE phone = ?");
        $techStmt->execute(['13800138000']);
        $tech = $techStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 模拟城市变更
            $input = json_decode(file_get_contents('php://input'), true);
            $newCity = $input['new_city'] ?? '三亚市';
            $oldCity = $tech['city'];
            
            if ($oldCity === $newCity) {
                echo json_encode([
                    'success' => false,
                    'message' => '城市没有变更',
                    'current_city' => $oldCity
                ]);
                exit;
            }
            
            // 更新技师城市
            $updateStmt = $pdo->prepare("UPDATE technician SET city = ? WHERE id = ?");
            $result = $updateStmt->execute([$newCity, $tech['id']]);
            
            if ($result) {
                // 添加技师动态
                addTechnicianFeed($pdo, $tech['id'], $tech['nick'], $tech['age'], $tech['height'], $tech['weight'], $tech['size'], $newCity, $oldCity);
                
                echo json_encode([
                    'success' => true,
                    'message' => '技师城市变更成功，已生成动态',
                    'technician_id' => $tech['id'],
                    'technician_name' => $tech['nick'],
                    'old_city' => $oldCity,
                    'new_city' => $newCity,
                    'action' => 'city_changed'
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => '城市变更失败'
                ]);
            }
        } else {
            // GET请求，返回当前技师信息
            echo json_encode([
                'success' => true,
                'message' => '获取技师信息成功',
                'technician' => [
                    'id' => $tech['id'],
                    'name' => $tech['nick'],
                    'phone' => $tech['phone'],
                    'age' => $tech['age'],
                    'height' => $tech['height'],
                    'weight' => $tech['weight'],
                    'size' => $tech['size'],
                    'city' => $tech['city'],
                    'hotel' => $tech['hotel']
                ],
                'action' => 'info'
            ]);
        }
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// 添加技师动态的函数
function addTechnicianFeed($pdo, $technician_id, $technician_name, $age, $height, $weight, $service_years, $city, $previous_city = null) {
    try {
        // 创建技师动态表（如果不存在）
        $createTableSql = "CREATE TABLE IF NOT EXISTS technician_feeds (
            id INT AUTO_INCREMENT PRIMARY KEY,
            technician_id INT NOT NULL,
            technician_name VARCHAR(100) NOT NULL,
            age INT DEFAULT NULL,
            height INT DEFAULT NULL,
            weight INT DEFAULT NULL,
            service_years INT DEFAULT NULL,
            city VARCHAR(100) NOT NULL,
            previous_city VARCHAR(100) DEFAULT NULL,
            action_type ENUM('join', 'move') NOT NULL DEFAULT 'join',
            content TEXT,
            avatar VARCHAR(255) DEFAULT 'images/tx.jpg',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_active TINYINT(1) DEFAULT 1,
            INDEX idx_technician_id (technician_id),
            INDEX idx_city (city),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createTableSql);
        
        // 确定动作类型
        $action_type = $previous_city ? 'move' : 'join';
        
        // 生成动态内容
        $specs = [];
        if ($age) $specs[] = $age;
        if ($height) $specs[] = $height;
        if ($weight) $specs[] = $weight;
        if ($service_years) $specs[] = $service_years;
        
        $specsText = !empty($specs) ? ' ' . implode('/', $specs) : '';
        $content = "技师{$technician_name}{$specsText} 到{$city}了";
        
        // 插入技师动态
        $insertSql = "INSERT INTO technician_feeds 
                      (technician_id, technician_name, age, height, weight, service_years, 
                       city, previous_city, action_type, content) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $pdo->prepare($insertSql);
        $result = $stmt->execute([
            $technician_id,
            $technician_name,
            $age,
            $height,
            $weight,
            $service_years,
            $city,
            $previous_city,
            $action_type,
            $content
        ]);
        
        return $result;
        
    } catch (Exception $e) {
        error_log("添加技师动态失败: " . $e->getMessage());
        return false;
    }
}
?>

<!-- 
使用说明：

1. GET请求 - 获取测试技师信息：
   GET /test_tech_city_change_api.php

2. POST请求 - 模拟城市变更：
   POST /test_tech_city_change_api.php
   Content-Type: application/json
   
   {
     "new_city": "三亚市"
   }

示例：
curl -X GET http://localhost/w7/test_tech_city_change_api.php
curl -X POST http://localhost/w7/test_tech_city_change_api.php \
     -H "Content-Type: application/json" \
     -d '{"new_city":"三亚市"}'
-->
