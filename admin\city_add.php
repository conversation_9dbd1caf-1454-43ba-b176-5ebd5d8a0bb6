<?php
// city_add.php
header('Content-Type: application/json; charset=utf-8');
require_once '../db.php'; // 数据库连接，需保证 $pdo 可用

$name = isset($_POST['name']) ? trim($_POST['name']) : '';
if ($name === '') {
    echo json_encode(['success' => false, 'msg' => '城市名称不能为空']);
    exit;
}

// 检查是否已存在
$stmt = $pdo->prepare("SELECT id FROM city WHERE name = ?");
$stmt->execute([$name]);
if ($stmt->fetch()) {
    echo json_encode(['success' => false, 'msg' => '城市已存在']);
    exit;
}

// 插入新城市
$stmt = $pdo->prepare("INSERT INTO city (name, created_at) VALUES (?, NOW())");
if ($stmt->execute([$name])) {
    echo json_encode(['success' => true, 'msg' => '新增成功']);
} else {
    echo json_encode(['success' => false, 'msg' => '数据库写入失败']);
}
