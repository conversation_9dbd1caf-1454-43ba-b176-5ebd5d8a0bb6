<?php
// tech_list.php - 公共技师列表API (无需登录)

// 禁用错误输出到浏览器，防止HTML错误信息污染JSON响应
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// 开启输出缓冲，确保只输出JSON
ob_start();

header('Content-Type: application/json; charset=utf-8');

// 统一的JSON响应函数
function sendJsonResponse($success, $message, $data = null) {
    ob_clean();
    $response = ['success' => $success, 'msg' => $message];
    if ($data !== null) {
        $response['data'] = $data;
        $response['total'] = is_array($data) ? count($data) : 0;
    }
    echo json_encode($response);
    exit;
}

// 注册致命错误处理器
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '服务器内部错误', 'data' => [], 'total' => 0]);
        exit;
    }
});

try {
    require_once 'db.php';

    // 检查数据库连接
    if ($pdo === null) {
        sendJsonResponse(false, '数据库连接失败', []);
    }
} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    sendJsonResponse(false, '数据库连接失败', []);
}

try {
    // 自动添加字段（兼容后台管理系统）
    try {
        $pdo->exec("ALTER TABLE technician ADD COLUMN is_recommended TINYINT(1) DEFAULT 0 COMMENT '是否推荐(1=推荐,0=普通)' AFTER is_black");
    } catch (Exception $e) {
        // 字段已存在，忽略错误
    }

    try {
        $pdo->exec("ALTER TABLE technician ADD COLUMN virtual_orders INT DEFAULT 0 COMMENT '虚拟单量' AFTER is_recommended");
    } catch (Exception $e) {
        // 字段已存在，忽略错误
    }

    try {
        $pdo->exec("ALTER TABLE technician ADD COLUMN is_resting TINYINT(1) DEFAULT 0 COMMENT '是否休息(1=休息中,0=工作中)' AFTER virtual_orders");
    } catch (Exception $e) {
        // 字段已存在，忽略错误
    }

    // 查询所有技师（复制后台逻辑）
    $stmt = $pdo->query("SELECT id, nick, age, height, weight, size, model, city, workimg, lifeimg, video, status, is_recommended, virtual_orders, is_resting, apply_time FROM technician ORDER BY is_recommended DESC, virtual_orders DESC, id DESC");
    $data = $stmt ? $stmt->fetchAll() : [];

    $fields = ['id', 'nick', 'age', 'height', 'weight', 'size', 'model', 'city', 'workimg', 'lifeimg', 'video', 'status', 'is_recommended', 'virtual_orders', 'is_resting', 'apply_time'];

    // 状态映射：pending->0, approved->1, rejected->2, review->3（复制后台逻辑）
    $statusMap = [
        'pending' => 0,
        'approved' => 1,
        'rejected' => 2,
        'review' => 3
    ];

    // 处理数据格式
    foreach ($data as &$row) {
        // 确保所有字段都存在
        foreach ($fields as $field) {
            if (!isset($row[$field])) {
                $row[$field] = '';
            }
        }

        // 确保数值字段的默认值
        $row['virtual_orders'] = $row['virtual_orders'] ?? 0;
        $row['is_recommended'] = $row['is_recommended'] ?? 0;
        $row['is_resting'] = $row['is_resting'] ?? 0;

        // 转换状态格式以匹配前端期望（复制后台逻辑）
        if (isset($row['status'])) {
            $row['status'] = isset($statusMap[$row['status']]) ? $statusMap[$row['status']] : $row['status'];
        }

        // 格式化状态文本
        $statusTextMap = [
            0 => '待审核',
            1 => '已通过',
            2 => '已拒绝',
            3 => '审核中'
        ];
        $row['status_text'] = $statusTextMap[$row['status']] ?? '待审核';

        // 格式化休息状态
        $row['rest_status_text'] = $row['is_resting'] ? '休息中' : '工作中';

        // 格式化推荐状态
        $row['recommend_status_text'] = $row['is_recommended'] ? '已推荐' : '未推荐';

        // 添加created_at字段兼容
        if (!isset($row['created_at']) && isset($row['apply_time'])) {
            $row['created_at'] = $row['apply_time'];
        }
    }

    sendJsonResponse(true, '获取成功', $data);

} catch (Exception $e) {
    error_log("Public tech list error: " . $e->getMessage());
    sendJsonResponse(false, '获取技师列表失败', []);
}
?>
