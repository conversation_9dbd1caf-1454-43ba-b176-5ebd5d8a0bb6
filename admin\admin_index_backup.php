<?php
// admin_index.php
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    header('Location: admin_login.html');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>后台首页</title>
  <style>
    body { background:#f7f8fa; font-family:'Roboto','PingFang SC','Microsoft YaHei',sans-serif; margin:0; }
    .admin-layout { display:flex; min-height:100vh; }
    .admin-nav {
      width:220px; background:#222; color:#fff; padding:0; display:flex; flex-direction:column; align-items:center; box-shadow:2px 0 12px rgba(0,0,0,0.06); min-height:100vh;
    }
    .admin-nav h3 { color:#fff; font-size:22px; font-weight:bold; margin:32px 0 24px 0; }
    .admin-nav ul { list-style:none; padding:0; width:100%; }
    .admin-nav li { padding:16px 0 16px 38px; font-size:16px; color:#bbb; cursor:pointer; transition:background 0.2s, color 0.2s; }
    .admin-nav li.active, .admin-nav li:hover { background:#111; color:#00c6a2; }
    .admin-content { flex:1; background:#fff; border-radius:0 0 0 0; box-shadow:0 4px 24px rgba(0,0,0,0.06); margin:38px 38px 38px 0; padding:38px 38px; min-width:0; }
    .admin-header { display:flex; justify-content:space-between; align-items:center; margin-bottom:28px; }
    .logout-btn { background:#ff6a6a; color:#fff; border:none; border-radius:8px; padding:8px 18px; font-size:15px; cursor:pointer; }
    @media (max-width:900px) {
      .admin-layout { flex-direction:column; }
      .admin-nav { width:100%; min-height:60px; flex-direction:row; align-items:center; justify-content:flex-start; box-shadow:none; }
      .admin-nav h3 { margin:0 18px 0 0; font-size:18px; }
      .admin-nav ul { display:flex; flex-direction:row; }
      .admin-nav li { padding:12px 18px; font-size:15px; }
      .admin-content { margin:0; padding:18px 8px; }
    }
  </style>
</head>
<body>
  <div class="admin-layout">
    <nav class="admin-nav">
      <h3>管理后台</h3>
      <ul>
        <li class="active" id="nav-dashboard">数据看板</li>
        <li id="nav-user">客户管理</li>
        <li id="nav-tech">技师管理</li>
        <li id="nav-service">服务管理</li>
        <li id="nav-feed">动态管理</li>
        <li id="nav-taxi">路费管理</li>
        <li id="nav-city">城市管理</li>
        <li id="nav-password">修改密码</li>
        <li id="nav-banner">轮播图管理</li>
        <li id="nav-amap">高德key管理</li>
        <li id="nav-project">项目介绍管理</li>
        <li id="nav-logout" style="color:#ff6a6a;">退出登入</li>
      </ul>
    </nav>
    <div class="admin-content">
      <div class="admin-header">
        <h2 id="admin-title">后台首页</h2>
      </div>
      <div id="admin-main">
        <p>欢迎，管理员！</p>
        <p>请选择左侧功能进行管理。</p>
      </div>
    </div>
  </div>
  <script>
    // 左侧导航切换逻辑（仅前端演示，可扩展为AJAX或跳转）
    var navs = document.querySelectorAll('.admin-nav li');
    var title = document.getElementById('admin-title');
    var main = document.getElementById('admin-main');
    navs.forEach(function(nav){
      nav.onclick = function(){
        navs.forEach(function(n){n.classList.remove('active');});
        nav.classList.add('active');
        switch(nav.id){
          case 'nav-city':
            title.textContent = '城市设置';
            main.innerHTML = `
            <div style="background:#fff;padding:28px 24px 18px 24px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.04);margin-bottom:24px;">
              <div style="font-size:20px;font-weight:500;color:#222;margin-bottom:18px;">城市设置</div>
              <div style="display:flex;gap:12px 18px;margin-bottom:18px;">
                <button type="button" id="add-city-btn" style="background:#2196f3;color:#fff;border:none;border-radius:6px;padding:6px 22px;font-size:15px;">+ 新增城市</button>
              </div>
              <table style="width:100%;border-collapse:collapse;font-size:15px;">
                <tbody id="city-table-body">
                  <tr><td colspan="3" style="text-align:center;color:#bbb;">正在加载...</td></tr>
                </tbody>
              </table>
            </div>
            `;
            // 新增城市弹窗HTML
            var cityModalHtml = `
            <div class="modal" id="cityAddModal" tabindex="-1" style="display:none;position:fixed;z-index:9999;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.35);justify-content:center;align-items:center;">
              <div style="background:#fff;border-radius:12px;max-width:340px;width:96vw;padding:32px 24px;box-shadow:0 4px 24px rgba(0,0,0,0.12);position:relative;">
                <button onclick="document.getElementById('cityAddModal').style.display='none'" style="position:absolute;right:18px;top:12px;font-size:22px;background:none;border:none;color:#888;cursor:pointer;">×</button>
                <div style="font-size:18px;font-weight:500;color:#222;margin-bottom:18px;">新增城市</div>
                <form id="city-add-form" style="display:flex;flex-direction:column;gap:16px;">
                  <label style="font-size:15px;color:#888;">城市名称 <input type="text" id="city-name" name="name" required maxlength="20" style="width:100%;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 12px;font-size:15px;" /></label>
                  <button type="submit" style="height:38px;background:#2196f3;color:#fff;font-size:15px;font-weight:500;border:none;border-radius:8px;">保存</button>
                  <div id="city-add-msg" style="color:#ff6a6a;font-size:15px;display:none;margin-top:8px;"></div>
                </form>
              </div>
            </div>`;
            if(!document.getElementById('cityAddModal')){
              document.body.insertAdjacentHTML('beforeend', cityModalHtml);
            }
            // 绑定新增城市按钮事件
            setTimeout(function(){
              var addBtn = document.getElementById('add-city-btn');
              if(addBtn){
                addBtn.onclick = function(){
                  document.getElementById('cityAddModal').style.display = 'flex';
                  document.getElementById('city-add-msg').style.display = 'none';
                  document.getElementById('city-add-form').reset();
                };
              }
              var cityForm = document.getElementById('city-add-form');
              if(cityForm){
                cityForm.onsubmit = function(e){
                  e.preventDefault();
                  var name = document.getElementById('city-name').value.trim();
                  var msg = document.getElementById('city-add-msg');
                  msg.style.display = 'none';
                  if(!name){
                    msg.textContent = '请填写城市名称';
                    msg.style.display = 'block';
                    return;
                  }
                  fetch('city_add.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'name='+encodeURIComponent(name)
                  })
                  .then(r=>r.json())
                  .then(function(res){
                    if(res.success){
                      msg.textContent = '新增成功';
                      msg.style.color = '#00c6a2';
                      msg.style.display = 'block';
                      setTimeout(function(){
                        document.getElementById('cityAddModal').style.display = 'none';
                        loadCityList();
                      }, 800);
                    }else{
                      msg.textContent = res.msg||'新增失败';
                      msg.style.color = '#ff6a6a';
                      msg.style.display = 'block';
                    }
                  })
                  .catch(function(){
                    msg.textContent = '网络错误，新增失败';
                    msg.style.color = '#ff6a6a';
                    msg.style.display = 'block';
                  });
                };
              }
              // 加载城市列表
              function loadCityList(){
                var tbody = document.getElementById('city-table-body');
                if(!tbody) return;
                fetch('city_list.php')
                  .then(r=>r.json())
                  .then(function(res){
                    if(Array.isArray(res)&&res.length){
                      var cols = 4; // 每行显示3个城市
                      var html = '';
                      for(var i=0;i<res.length;i+=cols){
                        html += '<tr>';
                        for(var j=0;j<cols;j++){
                          var c = res[i+j];
                          if(c){
                            html += `<td style='padding:10px 18px;vertical-align:top;'>
                              <div style='display:flex;align-items:center;gap:10px;'>
                                <span style='font-size:16px;'>${c.name||''}</span>
                                <label class='switch-hot' style='display:inline-block;cursor:pointer;'>
                                  <input type='checkbox' data-id='${c.id}' ${c.is_hot?'checked':''} style='display:none;'>
                                  <span style='display:inline-block;width:38px;height:22px;background:${c.is_hot?'#00c6a2':'#e0e0e0'};border-radius:12px;position:relative;vertical-align:middle;transition:background 0.2s;'>
                                    <span style='position:absolute;top:3px;left:${c.is_hot?'20px':'3px'};width:16px;height:16px;background:#fff;border-radius:50%;box-shadow:0 1px 4px rgba(0,0,0,0.08);transition:left 0.2s;'></span>
                                  </span>
                                </label>
                                <button class='del-city-btn' data-id='${c.id}' style='background:#ff6a6a;color:#fff;border:none;border-radius:6px;padding:4px 12px;font-size:14px;'>删除</button>
                              </div>
                            </td>`;
                          }else{
                            html += `<td></td>`;
                          }
                        }
                        html += '</tr>';
                      }
                      tbody.innerHTML = html;
                    }else{
                      tbody.innerHTML = '<tr><td colspan="3" style="text-align:center;color:#bbb;">暂无城市数据</td></tr>';
                    }
                    // 绑定删除按钮和热门开关
                    setTimeout(function(){
                      document.querySelectorAll('.del-city-btn').forEach(function(btn){
                        btn.onclick = function(){
                          var id = btn.getAttribute('data-id');
                          if(!id) return;
                          if(!confirm('确定要删除该城市吗？')) return;
                          btn.disabled = true;
                          btn.textContent = '删除中...';
                          fetch('city_delete.php', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                            body: 'id='+encodeURIComponent(id)
                          })
                          .then(r=>r.json())
                          .then(function(res){
                            if(res.success){
                              loadCityList();
                            }else{
                              btn.textContent = '删除';
                              btn.disabled = false;
                              alert(res.msg||'删除失败');
                            }
                          })
                          .catch(function(){
                            btn.textContent = '删除';
                            btn.disabled = false;
                            alert('网络错误，删除失败');
                          });
                        };
                      });
                      document.querySelectorAll('.switch-hot input[type="checkbox"]').forEach(function(cb){
                        cb.onchange = function(){
                          var id = cb.getAttribute('data-id');
                          var checked = cb.checked ? 1 : 0;
                          fetch('city_hot.php', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                            body: 'id='+encodeURIComponent(id)+'&is_hot='+checked
                          })
                          .then(r=>r.json())
                          .then(function(res){
                            if(res.success){
                              loadCityList();
                            }else{
                              alert(res.msg||'操作失败');
                              loadCityList();
                            }
                          })
                          .catch(function(){
                            alert('网络错误，操作失败');
                            loadCityList();
                          });
                        };
                      });
                    }, 50);
                  })
                  .catch(function(){
                    tbody.innerHTML = '<tr><td colspan="6" style="text-align:center;color:#ff6a6a;">城市数据加载失败</td></tr>';
                  });
              }
              loadCityList();
            }, 10);
            // TODO: 加载城市数据并渲染，绑定新增、删除、全部关闭等事件
            break;
          case 'nav-dashboard':
            title.textContent = '数据大看板';
            main.innerHTML = '<p>这里展示平台核心数据统计和分析。</p>';
            break;
          case 'nav-user':
            title.textContent = '客户管理';
            main.innerHTML = `
            <div style="background:#fff;padding:28px 24px 18px 24px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.04);margin-bottom:24px;">
              <div style="font-size:20px;font-weight:500;color:#222;margin-bottom:18px;">客户管理</div>
              <form id="user-search-form" style="display:flex;flex-wrap:wrap;gap:16px 18px;align-items:center;" autocomplete="on">
                <label for="search-nickname" style="font-size:15px;color:#888;">昵称/手机号</label>
                <input type="text" id="search-nickname" name="nickname" autocomplete="username" placeholder="请输入用户昵称/手机号" style="width:200px;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 12px;font-size:15px;" />
                <label for="search-userid" style="font-size:15px;color:#888;">用户ID</label>
                <input type="text" id="search-userid" name="userid" autocomplete="off" placeholder="请输入用户ID" style="width:120px;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 12px;font-size:15px;" />
                <label for="search-auth" style="font-size:15px;color:#888;">授权类型</label>
                <select id="search-auth" name="auth_type" autocomplete="off" style="width:120px;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 8px;font-size:15px;">
                  <option>全部</option>
                </select>
                <label for="search-level" style="font-size:15px;color:#888;">会员等级</label>
                <select id="search-level" name="level" autocomplete="off" style="width:120px;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 8px;font-size:15px;">
                  <option>全部</option>
                </select>
                <label for="search-source" style="font-size:15px;color:#888;">客户来源</label>
                <select id="search-source" name="source" autocomplete="off" style="width:120px;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 8px;font-size:15px;">
                  <option>全部</option>
                </select>
                <label for="search-region" style="font-size:15px;color:#888;">所在地区</label>
                <input type="text" id="search-region" name="region" autocomplete="address-level2" placeholder="请选择客户所在地区" style="width:180px;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 12px;font-size:15px;" />
                <label for="search-join-start" style="font-size:15px;color:#888;">加入时间</label>
                <input type="date" id="search-join-start" name="join_start" autocomplete="off" style="height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 8px;font-size:15px;" />
                <span style="color:#888;">至</span>
                <input type="date" id="search-join-end" name="join_end" autocomplete="off" style="height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 8px;font-size:15px;" />
                <label for="search-lastvisit-start" style="font-size:15px;color:#888;">最后访问时间</label>
                <input type="date" id="search-lastvisit-start" name="lastvisit_start" autocomplete="off" style="height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 8px;font-size:15px;" />
                <span style="color:#888;">至</span>
                <input type="date" id="search-lastvisit-end" name="lastvisit_end" autocomplete="off" style="height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 8px;font-size:15px;" />
                <label for="search-black" style="font-size:15px;color:#888;">拉黑状态</label>
                <select id="search-black" name="is_black" autocomplete="off" style="width:120px;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 8px;font-size:15px;">
                  <option value="all">全部</option>
                  <option value="0">未拉黑</option>
                  <option value="1">已拉黑</option>
                </select>
                <button type="submit" style="height:36px;background:#2196f3;color:#fff;font-size:15px;font-weight:500;border:none;border-radius:6px;min-width:80px;">搜索</button>
                <button type="button" style="height:36px;background:#fff;color:#666;font-size:15px;font-weight:500;border:1px solid #e0e0e0;border-radius:6px;min-width:80px;margin-left:8px;">重置</button>
                <button type="button" style="height:36px;background:#f5f7fa;color:#2196f3;font-size:15px;font-weight:500;border:1px solid #e0e0e0;border-radius:6px;min-width:80px;margin-left:8px;">导出</button>
              </form>
            </div>
            <div style="background:#fff;padding:0 0 18px 0;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.04);">
              <table style="width:100%;border-collapse:collapse;font-size:15px;">
                <thead>
                  <tr style="background:#f7f8fa;color:#888;">
                    <th style="padding:10px 8px;font-weight:500;">ID</th>
                    <th style="padding:10px 8px;font-weight:500;">客户昵称</th>
                    <th style="padding:10px 8px;font-weight:500;">客户头像</th>
                    <th style="padding:10px 8px;font-weight:500;">手机号</th>
                    <th style="padding:10px 8px;font-weight:500;">客户密码</th>
                    <th style="padding:10px 8px;font-weight:500;">会员等级</th>
                    <th style="padding:10px 8px;font-weight:500;">消费总金额 <span title='统计所有订单实际支付金额' style='cursor:help;'>?</span></th>
                    <th style="padding:10px 8px;font-weight:500;">账户余额</th>
                    <th style="padding:10px 8px;font-weight:500;">所在地区</th>
                    <th style="padding:10px 8px;font-weight:500;">最后访问时间</th>
                    <th style="padding:10px 8px;font-weight:500;">加入时间</th>
                    <th style="padding:10px 8px;font-weight:500;">操作</th>
                  </tr>
                </thead>
                <tbody id="user-table-body">
                  <tr><td colspan="12" style="text-align:center;color:#bbb;">正在加载...</td></tr>
                </tbody>
              </table>
            </div>
            `;
            // 客户管理筛选功能实现
            var userDataCache = [];
            function renderUserTable(data) {
              var selectBlack = document.getElementById('search-black');
              var filter = selectBlack ? selectBlack.value : 'all';
              var filtered = data.filter(function(u){
                if(filter === 'all') return true;
                if(filter === '0') return !(u.is_black == 1 || u.is_black === '1');
                if(filter === '1') return (u.is_black == 1 || u.is_black === '1');
                return true;
              });
              var tbody = document.getElementById('user-table-body');
              if(filtered.length){
                tbody.innerHTML = filtered.map(function(u){
                  var isBlack = u.is_black == 1 || u.is_black === '1';
                  // 处理头像路径
                  var avatarUrl = '../images/tx.jpg'; // 默认头像
                  if (u.avatar && u.avatar.trim()) {
                    var avatar = u.avatar.trim();
                    if (avatar.startsWith('images/')) {
                      avatarUrl = '../' + avatar;
                    } else if (avatar.startsWith('/w7/images/')) {
                      avatarUrl = '..' + avatar.substring(3);
                    } else if (avatar.startsWith('/images/')) {
                      avatarUrl = '..' + avatar;
                    } else {
                      avatarUrl = '../images/' + avatar;
                    }
                  }
                  var cacheBust = Date.now();
                  avatarUrl += (avatarUrl.indexOf('?') === -1 ? '?' : '&') + 'v=' + cacheBust;
                  return `<tr>
                    <td>${u.id}</td>
                    <td>${u.nickname||''}</td>
                    <td><img src="${avatarUrl}" style="width:38px;height:38px;border-radius:8px;object-fit:cover;" onerror="this.onerror=null;this.src='/w7/images/tx.jpg';" /></td>
                    <td>${u.mobile||''}</td>
                    <td>${u.password_plain||u.password||''}</td>
                    <td>${u.level||''}</td>
                    <td>¥${u.total_amount||0}</td>
                    <td>¥${u.balance||0}</td>
                    <td>${u.region||''}</td>
                    <td>${u.last_visit||''}</td>
                    <td>${u.created_at||''}</td>
                    <td>
                      <button class="black-btn" data-id="${u.id}" data-black="${isBlack?1:0}" style="background:${isBlack?'#bbb':'#ff6a6a'};color:#fff;border:none;border-radius:6px;padding:4px 16px;">${isBlack?'解除拉黑':'拉黑'}</button>
                    </td>
                  </tr>`;
                }).join('');
              }else{
                tbody.innerHTML = '<tr><td colspan="12" style="text-align:center;color:#bbb;">暂无客户数据</td></tr>';
              }
              // 绑定拉黑/解除拉黑按钮事件
              setTimeout(function(){
                var btns = document.querySelectorAll('.black-btn');
                btns.forEach(function(btn){
                  btn.onclick = function(){
                    var id = btn.getAttribute('data-id');
                    var isBlack = btn.getAttribute('data-black') == '1';
                    if(!id) return;
                    var action = isBlack ? 'unblack' : 'black';
                    var confirmMsg = isBlack ? '确定要解除拉黑该用户吗？' : '确定要拉黑该用户吗？';
                    if(!confirm(confirmMsg)) return;
                    btn.disabled = true;
                    btn.textContent = '操作中...';
                    fetch(action+'_user.php', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                      body: 'id=' + encodeURIComponent(id)
                    })
                    .then(r=>r.json())
                    .then(function(res){
                      if(res.success){
                        // 刷新缓存数据
                        fetch('user_list.php')
                          .then(r=>r.json())
                          .then(function(newRes){
                            userDataCache = Array.isArray(newRes) ? newRes : [];
                            renderUserTable(userDataCache);
                          });
                      }else{
                        btn.textContent = isBlack ? '解除拉黑' : '拉黑';
                        btn.disabled = false;
                        alert(res.msg||'操作失败');
                      }
                    })
                    .catch(function(){
                      btn.textContent = isBlack ? '解除拉黑' : '拉黑';
                      btn.disabled = false;
                      alert('网络错误，操作失败');
                    });
                  };
                });
              }, 50);
            }
            // 阻止搜索表单默认提交，改为前端筛选
            setTimeout(function(){
              fetch('user_list.php')
                .then(r=>r.json())
                .then(function(res){
                  userDataCache = Array.isArray(res) ? res : [];
                  renderUserTable(userDataCache);
                  // 绑定筛选项事件
                  var selectBlack = document.getElementById('search-black');
                  if(selectBlack){
                    selectBlack.onchange = function(){
                      renderUserTable(userDataCache);
                    };
                  }
                  var searchForm = document.getElementById('user-search-form');
                  if(searchForm){
                    searchForm.onsubmit = function(e){
                      e.preventDefault();
                      // 可扩展：根据表单内容筛选 userDataCache
                      renderUserTable(userDataCache);
                    };
                  }
                })
                .catch(function(){
                  var tbody = document.getElementById('user-table-body');
                  tbody.innerHTML = '<tr><td colspan="12" style="text-align:center;color:#ff6a6a;">客户数据加载失败</td></tr>';
                });
            }, 10);
            break;
          case 'nav-tech':
            title.textContent = '技师管理';
            main.innerHTML = `
            <div style="background:#fff;padding:28px 24px 18px 24px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.04);margin-bottom:24px;">
              <div style="font-size:20px;font-weight:500;color:#222;margin-bottom:18px;">技师管理</div>
              <div style="display:flex;gap:12px 18px;margin-bottom:18px;">
                <button type="button" class="tab-btn" data-tab="all" style="background:#2196f3;color:#fff;border:none;border-radius:6px;padding:6px 22px;font-size:15px;">全部（<span id="tab-all-count">0</span>）</button>
                <button type="button" class="tab-btn" data-tab="pending" style="background:#fff;color:#2196f3;border:1px solid #2196f3;border-radius:6px;padding:6px 22px;font-size:15px;">申请中（<span id="tab-pending-count">0</span>）</button>
                <button type="button" class="tab-btn" data-tab="approved" style="background:#fff;color:#2196f3;border:1px solid #2196f3;border-radius:6px;padding:6px 22px;font-size:15px;">已授权（<span id="tab-approved-count">0</span>）</button>
                <button type="button" class="tab-btn" data-tab="rejected" style="background:#fff;color:#2196f3;border:1px solid #2196f3;border-radius:6px;padding:6px 22px;font-size:15px;">已驳回（<span id="tab-rejected-count">0</span>）</button>
                <button type="button" class="tab-btn" data-tab="review" style="background:#fff;color:#2196f3;border:1px solid #2196f3;border-radius:6px;padding:6px 22px;font-size:15px;">重新审核（<span id="tab-review-count">0</span>）</button>
              </div>
              <form id="tech-search-form" style="display:flex;flex-wrap:wrap;gap:16px 18px;align-items:center;" autocomplete="on">
                <label for="search-nick" style="font-size:15px;color:#888;">技师姓名/手机号</label>
                <input type="text" id="search-nick" name="nick" autocomplete="username" placeholder="请输入技师姓名/手机号" style="width:180px;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 12px;font-size:15px;" />
                <label for="search-techid" style="font-size:15px;color:#888;">技师ID</label>
                <input type="text" id="search-techid" name="techid" autocomplete="off" placeholder="请输入技师ID" style="width:100px;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 12px;font-size:15px;" />
                <label for="search-broker" style="font-size:15px;color:#888;">所属经纪人</label>
                <input type="text" id="search-broker" name="broker" autocomplete="off" placeholder="请输入经纪人姓名" style="width:120px;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 12px;font-size:15px;" />
            <label for="search-agent" style="font-size:15px;color:#888;">所属代理商</label>
            <input type="text" id="search-agent" name="agent" autocomplete="off" placeholder="请输入代理商" style="width:120px;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 12px;font-size:15px;" />
            <label for="search-city" style="font-size:15px;color:#888;">所在城市</label>
            <select id="search-city" name="city" style="width:140px;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 8px;font-size:15px;">
              <option value="">请选择</option>
              <option value="北京">北京</option>
              <option value="上海">上海</option>
              <option value="广州">广州</option>
              <option value="深圳">深圳</option>
              <option value="成都">成都</option>
              <option value="重庆">重庆</option>
              <option value="杭州">杭州</option>
              <option value="南京">南京</option>
              <option value="苏州">苏州</option>
              <option value="其他">其他</option>
            </select>
            <label for="search-bind" style="font-size:15px;color:#888;">绑定状态</label>
            <select id="search-bind" name="bind" style="width:100px;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 8px;font-size:15px;">
              <option value="">全部</option>
              <option value="1">已绑定</option>
              <option value="0">未绑定</option>
            </select>
            <div style="display:flex;flex-basis:100%;height:0;"></div>
            <div style="display:flex;align-items:center;gap:8px;margin-top:2px;">
              <label for="search-date-start" style="font-size:15px;color:#888;">申请时间</label>
              <input type="date" id="search-date-start" name="date_start" style="height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 8px;font-size:15px;width:130px;" />
              <span style="color:#888;">至</span>
              <input type="date" id="search-date-end" name="date_end" style="height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 8px;font-size:15px;width:130px;" />
            </div>
                <button type="submit" style="height:36px;background:#2196f3;color:#fff;font-size:15px;font-weight:500;border:none;border-radius:6px;min-width:80px;">搜索</button>
                <button type="button" style="height:36px;background:#fff;color:#666;font-size:15px;font-weight:500;border:1px solid #e0e0e0;border-radius:6px;min-width:80px;margin-left:8px;">重置</button>
              </form>
            </div>
            <div style="background:#fff;padding:0 0 18px 0;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.04);">
              <table style="width:100%;border-collapse:collapse;font-size:15px;">
                <thead>
                  <tr style="background:#f7f8fa;color:#888;">
                    <th style="padding:10px 8px;font-weight:500;">ID</th>
                    <th style="padding:10px 8px;font-weight:500;">头像</th>
                    <th style="padding:10px 8px;font-weight:500;">昵称</th>
                    <th style="padding:10px 8px;font-weight:500;">手机号</th>
                    <th style="padding:10px 8px;font-weight:500;">密码</th>
                    <th style="padding:10px 8px;font-weight:500;">年纪</th>
                    <th style="padding:10px 8px;font-weight:500;">身高</th>
                    <th style="padding:10px 8px;font-weight:500;">体重</th>
                    <th style="padding:10px 8px;font-weight:500;">尺寸</th>
                    <th style="padding:10px 8px;font-weight:500;">工作城市</th>
                    <th style="padding:10px 8px;font-weight:500;">住址</th>
                    <th style="padding:10px 8px;font-weight:500;">申请时间</th>
                    <th style="padding:10px 8px;font-weight:500;">当前</th>
                  </tr>
                </thead>
                <tbody id="tech-table-body">
                  <tr><td colspan="16" style="text-align:center;color:#bbb;">正在加载...</td></tr>
                </tbody>
              </table>
            </div>
            `;
            // 技师管理表格渲染
            var techDataCache = [];
            function renderTechTable(data, tab) {
              var tbody = document.getElementById('tech-table-body');
              if(!tbody) return;
              var filtered = data;
              // tab筛选
              if(tab && tab!=='all') {
                filtered = filtered.filter(function(t){
                  if(tab==='pending') return t.status==0;
                  if(tab==='approved') return t.status==1;
                  if(tab==='rejected') return t.status==2;
                  if(tab==='review') return t.status==3;
                  return true;
                });
              }
              // 搜索筛选
              var nickVal = document.getElementById('search-nick')?.value.trim();
              var techidVal = document.getElementById('search-techid')?.value.trim();
              var brokerVal = document.getElementById('search-broker')?.value.trim();
              var agentVal = document.getElementById('search-agent')?.value.trim();
              var bindVal = document.getElementById('search-bind')?.value;
              var dateStart = document.getElementById('search-date-start')?.value;
              var dateEnd = document.getElementById('search-date-end')?.value;
              if(nickVal) filtered = filtered.filter(function(t){ return (t.nick||'').includes(nickVal) || (t.phone||'').includes(nickVal); });
              if(techidVal) filtered = filtered.filter(function(t){ return String(t.id||'').includes(techidVal); });
              if(brokerVal) filtered = filtered.filter(function(t){ return (t.broker||'').includes(brokerVal); });
              if(agentVal) filtered = filtered.filter(function(t){ return (t.agent||'').includes(agentVal); });
              if(bindVal) filtered = filtered.filter(function(t){ return String(t.is_bind||'')===bindVal; });
              if(dateStart) filtered = filtered.filter(function(t){ return t.created_at && t.created_at >= dateStart; });
              if(dateEnd) filtered = filtered.filter(function(t){ return t.created_at && t.created_at <= dateEnd; });
              // 统计tab数量
              var allCount = data.length;
              var pendingCount = data.filter(t=>t.status==0).length;
              var approvedCount = data.filter(t=>t.status==1).length;
              var rejectedCount = data.filter(t=>t.status==2).length;
              var reviewCount = data.filter(t=>t.status==3).length;
              document.getElementById('tab-all-count').textContent = allCount;
              document.getElementById('tab-pending-count').textContent = pendingCount;
              document.getElementById('tab-approved-count').textContent = approvedCount;
              document.getElementById('tab-rejected-count').textContent = rejectedCount;
              document.getElementById('tab-review-count').textContent = reviewCount;
              if(filtered.length){
                tbody.innerHTML = filtered.map(function(t){
                  var avatarUrl = (t.workimg && t.workimg.trim()) ? t.workimg : '/w7/images/tx.jpg';
                  let auditBtns = '';
                  if(tab==='pending'){
                    auditBtns = `<button class='view-tech-btn' data-id='${t.id}' style='background:#2196f3;color:#fff;border:none;border-radius:6px;padding:4px 12px;margin-right:8px;'>查看信息</button>`+
                      `<button class='audit-pass-btn' data-id='${t.id}' style='background:#00c6a2;color:#fff;border:none;border-radius:6px;padding:4px 12px;margin-right:8px;'>审核通过</button>`+
                      `<button class='audit-reject-btn' data-id='${t.id}' style='background:#ff6a6a;color:#fff;border:none;border-radius:6px;padding:4px 12px;'>审核不通过</button>`;
                  }
                  return `<tr>
                    <td>${t.id}</td>
                    <td><img src="${avatarUrl}" style="width:38px;height:38px;border-radius:8px;object-fit:cover;" onerror="this.onerror=null;this.src='/w7/images/tx.jpg';" /></td>
                    <td>${t.nick||''}</td>
                    <td>${t.phone||''}</td>
                    <td>${t.password||''}</td>
                    <td>${t.age||''}</td>
                    <td>${t.height||''}</td>
                    <td>${t.weight||''}</td>
                    <td>${t.size||''}</td>
                    <td>${t.city||''}</td>
                    <td>${t.hotel||''}</td>
                    <td>${t.apply_time||''}</td>
                    <td>${t.current||''}</td>
                    <td>${auditBtns}</td>
                  </tr>`;
              // 查看信息按钮事件绑定
              setTimeout(function(){
                document.querySelectorAll('.view-tech-btn').forEach(function(btn){
                  btn.onclick = function(){
                    var id = btn.getAttribute('data-id');
                    var tech = techDataCache.find(t=>String(t.id)===String(id));
                    if(!tech){alert('未找到技师信息');return;}
                    showTechDetailModal(tech);
                  };
                });
              }, 50);
              // 审核按钮事件绑定
              setTimeout(function(){
                document.querySelectorAll('.audit-pass-btn').forEach(function(btn){
                  btn.onclick = function(){
                    var id = btn.getAttribute('data-id');
                    if(!confirm('确定要审核通过该技师吗？')) return;
                    btn.disabled = true;
                    fetch('tech_audit.php', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                      body: 'id='+encodeURIComponent(id)+'&action=approve'
                    })
                    .then(r=>r.json())
                    .then(function(res){
                      alert(res.msg||'操作完成');
                      location.reload();
                    });
                  };
                });
                document.querySelectorAll('.audit-reject-btn').forEach(function(btn){
                  btn.onclick = function(){
                    var id = btn.getAttribute('data-id');
                    var reason = prompt('请输入不通过原因：');
                    if(reason===null) return;
                    if(!reason.trim()){alert('请填写不通过原因');return;}
                    btn.disabled = true;
                    fetch('tech_audit.php', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                      body: 'id='+encodeURIComponent(id)+'&action=reject&reason='+encodeURIComponent(reason)
                    })
                    .then(r=>r.json())
                    .then(function(res){
                      alert(res.msg||'操作完成');
                      location.reload();
                    });
                  };
                });
              }, 50);
                }).join('');
              }else{
                tbody.innerHTML = '<tr><td colspan="16" style="text-align:center;color:#bbb;">暂无技师数据</td></tr>';
              }
            }
            // 加载技师数据
            setTimeout(function(){
              fetch('tech_list.php')
                .then(r=>r.json())
                .then(function(res){
                  techDataCache = Array.isArray(res.data) ? res.data : [];
                  var currentTab = 'all';
                  renderTechTable(techDataCache, currentTab);
                  // tab切换
                  var tabBtns = document.querySelectorAll('.tab-btn');
                  tabBtns.forEach(function(btn){
                    btn.onclick = function(){
                      tabBtns.forEach(function(b){b.style.background='#fff';b.style.color='#2196f3';b.style.border='1px solid #2196f3';});
                      btn.style.background='#2196f3';btn.style.color='#fff';btn.style.border='none';
                      currentTab = btn.getAttribute('data-tab');
                      renderTechTable(techDataCache, currentTab);
                    };
                  });
                  // 搜索表单
                  var searchForm = document.getElementById('tech-search-form');
                  if(searchForm){
                    searchForm.onsubmit = function(e){
                      e.preventDefault();
                      renderTechTable(techDataCache, currentTab);
                    };
                  }
                  // 重置按钮
                  var resetBtn = searchForm.querySelector('button[type="button"]');
                  if(resetBtn){
                    resetBtn.onclick = function(){
                      searchForm.reset();
                      renderTechTable(techDataCache, currentTab);
                    };
                  }
                })
                .catch(function(){
                  var tbody = document.getElementById('tech-table-body');
                  if(tbody) tbody.innerHTML = '<tr><td colspan="16" style="text-align:center;color:#ff6a6a;">技师数据加载失败</td></tr>';
                });
            }, 10);
            break;
          case 'nav-service':
            title.textContent = '服务管理';
            main.innerHTML = '<p>这里可以管理服务项目和内容。</p>';
            break;
          case 'nav-taxi':
            title.textContent = '路费管理';
            main.innerHTML = `
              <form id="taxi-article-form" style="max-width:480px;margin:32px auto 0 auto;background:#fff;border-radius:16px;box-shadow:0 2px 8px rgba(0,0,0,0.06);padding:32px 28px;display:flex;flex-direction:column;gap:22px;">
                <div style="display:flex;align-items:center;gap:8px;">
                  <span style="color:#ff6a6a;font-size:18px;">*</span>
                  <label for="taxi-title" style="font-size:15px;color:#444;">文章标题</label>
                  <input id="taxi-title" type="text" maxlength="20" placeholder="请输入文章标题" style="flex:1;height:38px;border-radius:8px;border:1px solid #e0e0e0;padding:0 12px;font-size:15px;margin-left:8px;" />
                  <span id="taxi-title-count" style="font-size:13px;color:#bbb;margin-left:8px;">0/20</span>
                </div>
                <div style="display:flex;flex-direction:column;gap:6px;">
                  <label for="taxi-content" style="font-size:15px;color:#444;">内容</label>
                  <textarea id="taxi-content" rows="7" maxlength="1000" placeholder="请输入内容" style="border-radius:8px;border:1px solid #e0e0e0;padding:10px 12px;font-size:15px;resize:vertical;"></textarea>
                  <span id="taxi-content-count" style="font-size:13px;color:#bbb;align-self:flex-end;">0/1000</span>
                </div>
                <button type="submit" style="height:44px;background:#2196f3;color:#fff;font-size:17px;font-weight:bold;border:none;border-radius:10px;cursor:pointer;">保存</button>
                <div id="taxi-article-msg" style="color:#ff6a6a;font-size:15px;display:none;"></div>
              </form>
            `;
            setTimeout(function(){
              var titleInput = document.getElementById('taxi-title');
              var titleCount = document.getElementById('taxi-title-count');
              var contentInput = document.getElementById('taxi-content');
              var contentCount = document.getElementById('taxi-content-count');
              var msg = document.getElementById('taxi-article-msg');
              titleInput.oninput = function(){
                titleCount.textContent = titleInput.value.length + '/20';
              };
              contentInput.oninput = function(){
                contentCount.textContent = contentInput.value.length + '/1000';
              };
              // 读取已保存内容
              fetch('taxi_article.php')
                .then(r=>r.json())
                .then(function(res){
                  if(res.success && res.data){
                    titleInput.value = res.data.title || '';
                    contentInput.value = res.data.content || '';
                    titleCount.textContent = titleInput.value.length + '/20';
                    contentCount.textContent = contentInput.value.length + '/1000';
                  }
                });
              document.getElementById('taxi-article-form').onsubmit = function(e){
                e.preventDefault();
                msg.style.display = 'none';
                if(!titleInput.value.trim()){
                  msg.textContent = '请输入文章标题';
                  msg.style.display = 'block';
                  return;
                }
                if(!contentInput.value.trim()){
                  msg.textContent = '请输入内容';
                  msg.style.display = 'block';
                  return;
                }
                fetch('taxi_article.php', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                  body: 'title=' + encodeURIComponent(titleInput.value) + '&content=' + encodeURIComponent(contentInput.value)
                })
                .then(r=>r.json())
                .then(function(res){
                  msg.textContent = res.msg || (res.success ? '保存成功' : '保存失败');
                  msg.style.display = 'block';
                  if(res.success){
                    msg.style.color = '#00c6a2';
                  }else{
                    msg.style.color = '#ff6a6a';
                  }
                });
              };
            }, 10);
            break;
          case 'nav-feed':
            title.textContent = '动态管理';
            main.innerHTML = '<p>这里可以管理所有用户发布的动态。</p>';
            break;
          case 'nav-password':
            title.textContent = '修改密码';
            main.innerHTML = `
            <div style="max-width:420px;margin:32px auto 0 auto;background:#fff;border-radius:16px;box-shadow:0 2px 8px rgba(0,0,0,0.06);padding:32px 28px;">
              <div style="font-size:20px;font-weight:bold;color:#222;margin-bottom:18px;">修改管理员密码</div>
              <form id="password-change-form" style="display:flex;flex-direction:column;gap:18px;">
                <label for="current-password" style="font-size:15px;color:#888;">
                  当前密码
                  <input type="password" id="current-password" name="current_password" required
                         style="width:100%;height:40px;border-radius:8px;border:1px solid #eee;padding:0 12px;font-size:16px;margin-top:6px;"
                         placeholder="请输入当前密码" />
                </label>
                <label for="new-password" style="font-size:15px;color:#888;">
                  新密码
                  <input type="password" id="new-password" name="new_password" required minlength="6"
                         style="width:100%;height:40px;border-radius:8px;border:1px solid #eee;padding:0 12px;font-size:16px;margin-top:6px;"
                         placeholder="请输入新密码（至少6位）" />
                </label>
                <label for="confirm-password" style="font-size:15px;color:#888;">
                  确认新密码
                  <input type="password" id="confirm-password" name="confirm_password" required minlength="6"
                         style="width:100%;height:40px;border-radius:8px;border:1px solid #eee;padding:0 12px;font-size:16px;margin-top:6px;"
                         placeholder="请再次输入新密码" />
                </label>
                <button type="submit" style="height:44px;background:#2196f3;color:#fff;font-size:17px;font-weight:bold;border:none;border-radius:10px;cursor:pointer;">
                  修改密码
                </button>
                <div id="password-change-msg" style="color:#ff6a6a;font-size:15px;display:none;"></div>
              </form>
              <div style="font-size:13px;color:#888;margin-top:18px;line-height:1.5;">
                <strong>安全提示：</strong><br>
                • 密码长度至少6位<br>
                • 建议使用字母、数字组合<br>
                • 修改后请牢记新密码
              </div>
            </div>
            `;

            // 绑定修改密码表单事件
            setTimeout(function(){
              var passwordForm = document.getElementById('password-change-form');
              var msg = document.getElementById('password-change-msg');

              if(passwordForm){
                passwordForm.onsubmit = function(e){
                  e.preventDefault();

                  var currentPassword = document.getElementById('current-password').value.trim();
                  var newPassword = document.getElementById('new-password').value.trim();
                  var confirmPassword = document.getElementById('confirm-password').value.trim();

                  msg.style.display = 'none';

                  // 前端验证
                  if(!currentPassword){
                    msg.textContent = '请输入当前密码';
                    msg.style.color = '#ff6a6a';
                    msg.style.display = 'block';
                    return;
                  }

                  if(!newPassword){
                    msg.textContent = '请输入新密码';
                    msg.style.color = '#ff6a6a';
                    msg.style.display = 'block';
                    return;
                  }

                  if(newPassword.length < 6){
                    msg.textContent = '新密码长度不能少于6位';
                    msg.style.color = '#ff6a6a';
                    msg.style.display = 'block';
                    return;
                  }

                  if(newPassword !== confirmPassword){
                    msg.textContent = '两次输入的新密码不一致';
                    msg.style.color = '#ff6a6a';
                    msg.style.display = 'block';
                    return;
                  }

                  if(currentPassword === newPassword){
                    msg.textContent = '新密码不能与当前密码相同';
                    msg.style.color = '#ff6a6a';
                    msg.style.display = 'block';
                    return;
                  }

                  // 提交修改
                  var submitBtn = passwordForm.querySelector('button[type="submit"]');
                  submitBtn.disabled = true;
                  submitBtn.textContent = '修改中...';

                  fetch('admin_password.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'current_password=' + encodeURIComponent(currentPassword) +
                          '&new_password=' + encodeURIComponent(newPassword) +
                          '&confirm_password=' + encodeURIComponent(confirmPassword)
                  })
                  .then(r=>r.json())
                  .then(function(res){
                    submitBtn.disabled = false;
                    submitBtn.textContent = '修改密码';

                    msg.textContent = res.msg || (res.success ? '密码修改成功' : '修改失败');
                    msg.style.display = 'block';

                    if(res.success){
                      msg.style.color = '#00c6a2';
                      // 清空表单
                      passwordForm.reset();
                      // 提示用户重新登录
                      setTimeout(function(){
                        if(confirm('密码修改成功！为了安全，请重新登录。')){
                          window.location.href = 'admin_logout.php';
                        }
                      }, 1500);
                    }else{
                      msg.style.color = '#ff6a6a';
                    }
                  })
                  .catch(function(){
                    submitBtn.disabled = false;
                    submitBtn.textContent = '修改密码';
                    msg.textContent = '网络错误，修改失败';
                    msg.style.color = '#ff6a6a';
                    msg.style.display = 'block';
                  });
                };
              }
            }, 10);
            break;
          case 'nav-banner':
            title.textContent = '轮播图管理';
            main.innerHTML = `
            <div style="background:#fff;padding:28px 24px 18px 24px;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,0.04);margin-bottom:24px;">
              <div style="font-size:20px;font-weight:500;color:#222;margin-bottom:18px;">轮播图管理</div>
              <div style="display:flex;gap:12px 18px;margin-bottom:18px;">
                <button type="button" id="add-banner-btn" style="background:#2196f3;color:#fff;border:none;border-radius:6px;padding:6px 22px;font-size:15px;">+ 新增轮播图</button>
              </div>
              <div id="banner-list" style="display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:18px;">
                <div style="text-align:center;color:#bbb;grid-column:1/-1;">正在加载...</div>
              </div>
            </div>
            `;

            // 新增轮播图弹窗HTML
            var bannerModalHtml = `
            <div class="modal" id="bannerAddModal" tabindex="-1" style="display:none;position:fixed;z-index:9999;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.35);justify-content:center;align-items:center;">
              <div style="background:#fff;border-radius:12px;max-width:420px;width:96vw;padding:32px 24px;box-shadow:0 4px 24px rgba(0,0,0,0.12);position:relative;">
                <button onclick="document.getElementById('bannerAddModal').style.display='none'" style="position:absolute;right:18px;top:12px;font-size:22px;background:none;border:none;color:#888;cursor:pointer;">×</button>
                <div style="font-size:18px;font-weight:500;color:#222;margin-bottom:18px;">新增轮播图</div>
                <form id="banner-add-form" style="display:flex;flex-direction:column;gap:16px;">
                  <label style="font-size:15px;color:#888;">选择图片 <input type="file" id="banner-file" name="banner" accept="image/*" required style="width:100%;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:6px 12px;font-size:15px;" /></label>
                  <label style="font-size:15px;color:#888;">跳转链接（可选） <input type="url" id="banner-link" name="link" placeholder="https://example.com" style="width:100%;height:36px;border-radius:6px;border:1px solid #e0e0e0;padding:0 12px;font-size:15px;" /></label>
                  <button type="submit" style="height:38px;background:#2196f3;color:#fff;font-size:15px;font-weight:500;border:none;border-radius:8px;">上传</button>
                  <div id="banner-add-msg" style="color:#ff6a6a;font-size:15px;display:none;margin-top:8px;"></div>
                </form>
              </div>
            </div>`;

            if(!document.getElementById('bannerAddModal')){
              document.body.insertAdjacentHTML('beforeend', bannerModalHtml);
            }

            // 绑定新增轮播图按钮事件
            setTimeout(function(){
              var addBtn = document.getElementById('add-banner-btn');
              if(addBtn){
                addBtn.onclick = function(){
                  document.getElementById('bannerAddModal').style.display = 'flex';
                  document.getElementById('banner-add-msg').style.display = 'none';
                  document.getElementById('banner-add-form').reset();
                };
              }

              var bannerForm = document.getElementById('banner-add-form');
              if(bannerForm){
                bannerForm.onsubmit = function(e){
                  e.preventDefault();
                  var fileInput = document.getElementById('banner-file');
                  var linkInput = document.getElementById('banner-link');
                  var msg = document.getElementById('banner-add-msg');
                  msg.style.display = 'none';

                  if(!fileInput.files.length){
                    msg.textContent = '请选择要上传的图片';
                    msg.style.display = 'block';
                    return;
                  }

                  var formData = new FormData();
                  formData.append('banner', fileInput.files[0]);
                  formData.append('link', linkInput.value.trim());

                  fetch('banner_upload.php', {
                    method: 'POST',
                    body: formData
                  })
                  .then(r=>r.json())
                  .then(function(res){
                    if(res.success){
                      msg.textContent = '上传成功';
                      msg.style.color = '#00c6a2';
                      msg.style.display = 'block';
                      setTimeout(function(){
                        document.getElementById('bannerAddModal').style.display = 'none';
                        loadBannerList();
                      }, 800);
                    }else{
                      msg.textContent = res.msg||'上传失败';
                      msg.style.color = '#ff6a6a';
                      msg.style.display = 'block';
                    }
                  })
                  .catch(function(){
                    msg.textContent = '网络错误，上传失败';
                    msg.style.color = '#ff6a6a';
                    msg.style.display = 'block';
                  });
                };
              }

              // 加载轮播图列表
              function loadBannerList(){
                var container = document.getElementById('banner-list');
                if(!container) return;

                fetch('banner_list.php')
                  .then(r=>r.json())
                  .then(function(res){
                    if(res.success && Array.isArray(res.data)){
                      if(res.data.length){
                        container.innerHTML = res.data.map(function(banner){
                          // 处理图片路径，确保从admin目录访问时路径正确
                          var imgSrc = banner.img;
                          if (imgSrc.startsWith('uploads/')) {
                            imgSrc = '../' + imgSrc;
                          } else if (imgSrc.startsWith('/w7/')) {
                            imgSrc = '..' + imgSrc.substring(3);
                          }
                          return `<div style="border:1px solid #e0e0e0;border-radius:8px;overflow:hidden;background:#fff;">
                            <img src="${imgSrc}" style="width:100%;height:160px;object-fit:cover;" onerror="this.onerror=null;this.src='../images/lbt.png';" />
                            <div style="padding:12px;">
                              <div style="font-size:14px;color:#666;margin-bottom:8px;">ID: ${banner.id}</div>
                              <div style="font-size:14px;color:#666;margin-bottom:8px;word-break:break-all;">链接: ${banner.link || '无'}</div>
                              <button class="del-banner-btn" data-id="${banner.id}" style="background:#ff6a6a;color:#fff;border:none;border-radius:6px;padding:6px 16px;font-size:14px;width:100%;">删除</button>
                            </div>
                          </div>`;
                        }).join('');
                      }else{
                        container.innerHTML = '<div style="text-align:center;color:#bbb;grid-column:1/-1;">暂无轮播图</div>';
                      }
                    }else{
                      container.innerHTML = '<div style="text-align:center;color:#ff6a6a;grid-column:1/-1;">加载失败</div>';
                    }

                    // 绑定删除按钮事件
                    setTimeout(function(){
                      document.querySelectorAll('.del-banner-btn').forEach(function(btn){
                        btn.onclick = function(){
                          var id = btn.getAttribute('data-id');
                          if(!id) return;
                          if(!confirm('确定要删除这个轮播图吗？')) return;

                          btn.disabled = true;
                          btn.textContent = '删除中...';

                          fetch('banner_delete.php', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                            body: 'id='+encodeURIComponent(id)
                          })
                          .then(r=>r.json())
                          .then(function(res){
                            if(res.success){
                              loadBannerList();
                            }else{
                              btn.textContent = '删除';
                              btn.disabled = false;
                              alert(res.msg||'删除失败');
                            }
                          })
                          .catch(function(){
                            btn.textContent = '删除';
                            btn.disabled = false;
                            alert('网络错误，删除失败');
                          });
                        };
                      });
                    }, 50);
                  })
                  .catch(function(){
                    container.innerHTML = '<div style="text-align:center;color:#ff6a6a;grid-column:1/-1;">网络错误，加载失败</div>';
                  });
              }

              loadBannerList();
            }, 10);
            break;
          case 'nav-amap':
            title.textContent = '高德key管理';
            main.innerHTML = '<div style="max-width:420px;margin:32px auto 0 auto;background:#fff;border-radius:16px;box-shadow:0 2px 8px rgba(0,0,0,0.06);padding:32px 28px;">'
              + '<div style="font-size:20px;font-weight:bold;color:#222;margin-bottom:18px;">高德地图API Key管理</div>'
              + '<form id="amap-key-form" style="display:flex;flex-direction:column;gap:18px;">'
              + '<label for="amap-key-input" style="font-size:15px;color:#888;">请输入高德地图API Key：</label>'
              + '<input type="text" id="amap-key-input" name="amap_key" style="height:40px;border-radius:8px;border:1px solid #eee;padding:0 12px;font-size:16px;" placeholder="请输入Key" />'
              + '<button type="submit" style="height:44px;background:#2196f3;color:#fff;font-size:17px;font-weight:bold;border:none;border-radius:10px;cursor:pointer;">保存</button>'
              + '<div id="amap-key-msg" style="color:#ff6a6a;font-size:15px;display:none;"></div>'
              + '</form>'
              + '<div style="font-size:13px;color:#888;margin-top:18px;">高德地图Key用于地图选址、定位等功能。请前往<a href="https://console.amap.com/dev/key/app" target="_blank" style="color:#2196f3;">高德开放平台</a>申请。</div>'
              + '</div>';
            // 动态插入后再绑定事件和请求
            setTimeout(function(){
              fetch('amap_key.php')
                .then(r=>r.json())
                .then(function(res){
                  if(res.success && res.amap_key){
                    document.getElementById('amap-key-input').value = res.amap_key;
                  }
                });
              document.getElementById('amap-key-form').onsubmit = function(e){
                e.preventDefault();
                var key = document.getElementById('amap-key-input').value.trim();
                var msg = document.getElementById('amap-key-msg');
                msg.style.display = 'none';
                fetch('amap_key.php', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                  body: 'amap_key=' + encodeURIComponent(key)
                })
                .then(r=>r.json())
                .then(function(res){
                  msg.textContent = res.msg || (res.success ? '保存成功' : '保存失败');
                  msg.style.display = 'block';
                  if(res.success){
                    msg.style.color = '#00c6a2';
                  }else{
                    msg.style.color = '#ff6a6a';
                  }
                });
              };
            }, 10);
            break;
          case 'nav-project':
            title.textContent = '项目介绍管理';
            main.innerHTML = `
              <form id="project-article-form" style="max-width:480px;margin:32px auto 0 auto;background:#fff;border-radius:16px;box-shadow:0 2px 8px rgba(0,0,0,0.06);padding:32px 28px;display:flex;flex-direction:column;gap:22px;">
                <div style="display:flex;align-items:center;gap:8px;">
                  <span style="color:#ff6a6a;font-size:18px;">*</span>
                  <label for="project-title" style="font-size:15px;color:#444;">文章标题</label>
                  <input id="project-title" type="text" maxlength="20" placeholder="请输入文章标题" style="flex:1;height:38px;border-radius:8px;border:1px solid #e0e0e0;padding:0 12px;font-size:15px;margin-left:8px;" />
                  <span id="project-title-count" style="font-size:13px;color:#bbb;margin-left:8px;">0/20</span>
                </div>
                <div style="display:flex;flex-direction:column;gap:6px;">
                  <label for="project-content" style="font-size:15px;color:#444;">内容</label>
                  <textarea id="project-content" rows="7" maxlength="1000" placeholder="请输入内容" style="border-radius:8px;border:1px solid #e0e0e0;padding:10px 12px;font-size:15px;resize:vertical;"></textarea>
                  <span id="project-content-count" style="font-size:13px;color:#bbb;align-self:flex-end;">0/1000</span>
                </div>
                <button type="submit" style="height:44px;background:#2196f3;color:#fff;font-size:17px;font-weight:bold;border:none;border-radius:10px;cursor:pointer;">保存</button>
                <div id="project-article-msg" style="color:#ff6a6a;font-size:15px;display:none;"></div>
              </form>
            `;
            setTimeout(function(){
              var titleInput = document.getElementById('project-title');
              var titleCount = document.getElementById('project-title-count');
              var contentInput = document.getElementById('project-content');
              var contentCount = document.getElementById('project-content-count');
              var msg = document.getElementById('project-article-msg');
              titleInput.oninput = function(){
                titleCount.textContent = titleInput.value.length + '/20';
              };
              contentInput.oninput = function(){
                contentCount.textContent = contentInput.value.length + '/1000';
              };
              // 读取已保存内容
              fetch('project_article.php')
                .then(r=>r.json())
                .then(function(res){
                  if(res.success && res.data){
                    titleInput.value = res.data.title || '';
                    contentInput.value = res.data.content || '';
                    titleCount.textContent = titleInput.value.length + '/20';
                    contentCount.textContent = contentInput.value.length + '/1000';
                  }
                });
              document.getElementById('project-article-form').onsubmit = function(e){
                e.preventDefault();
                msg.style.display = 'none';
                if(!titleInput.value.trim()){
                  msg.textContent = '请输入文章标题';
                  msg.style.display = 'block';
                  return;
                }
                if(!contentInput.value.trim()){
                  msg.textContent = '请输入内容';
                  msg.style.display = 'block';
                  return;
                }
                fetch('project_article.php', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                  body: 'title=' + encodeURIComponent(titleInput.value) + '&content=' + encodeURIComponent(contentInput.value)
                })
                .then(r=>r.json())
                .then(function(res){
                  msg.textContent = res.msg || (res.success ? '保存成功' : '保存失败');
                  msg.style.display = 'block';
                  if(res.success){
                    msg.style.color = '#00c6a2';
                  }else{
                    msg.style.color = '#ff6a6a';
                  }
                });
              };
            }, 10);
            break;
          case 'nav-logout':
            window.location.href = 'admin_logout.php';
            break;
          default:
            title.textContent = '后台首页';
            main.innerHTML = '<p>欢迎，管理员！</p><p>请选择左侧功能进行管理。</p>';
        }
      };
    });
    // 技师详情弹窗HTML
    var techDetailModalHtml = `
    <div class="modal" id="techDetailModal" tabindex="-1" style="display:none;position:fixed;z-index:9999;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.35);justify-content:center;align-items:center;">
      <div style="background:#fff;border-radius:12px;max-width:520px;width:96vw;padding:32px 24px;box-shadow:0 4px 24px rgba(0,0,0,0.12);position:relative;">
        <button onclick="document.getElementById('techDetailModal').style.display='none'" style="position:absolute;right:18px;top:12px;font-size:22px;background:none;border:none;color:#888;cursor:pointer;">×</button>
        <div id="techDetailBody"></div>
      </div>
    </div>
    `;
    if(!document.getElementById('techDetailModal')){
      document.body.insertAdjacentHTML('beforeend', techDetailModalHtml);
    }

    function showTechDetailModal(row) {
      let html = `
        <div style="display:flex;flex-wrap:wrap;gap:12px 18px;">
          <div style="flex:1 1 220px;min-width:180px;">
            <p><b>昵称：</b>${row.nick||''}</p>
            <p><b>手机号：</b>${row.phone||''}</p>
            <p><b>年龄：</b>${row.age||''}</p>
            <p><b>身高：</b>${row.height||''}</p>
            <p><b>体重：</b>${row.weight||''}</p>
            <p><b>三围：</b>${row.size||''}</p>
            <p><b>城市：</b>${row.city||''}</p>
            <p><b>酒店：</b>${row.hotel||''}</p>
            <p><b>密码：</b>${row.password||''}</p>
            <p><b>状态：</b>${row.status}</p>
            <p><b>申请时间：</b>${row.apply_time||''}</p>
            <p><b>驳回原因：</b>${row.reject_reason||''}</p>
          </div>
          <div style="flex:1 1 220px;min-width:180px;">
            <p><b>工作照：</b>${row.workimg?`<img src='${row.workimg}' style='max-width:120px;border-radius:8px;margin-bottom:6px;'>`:''}</p>
            <p><b>生活照：</b>${renderLifeImgs(row.lifeimg)}</p>
            <p><b>视频：</b>${row.video?`<video src='${row.video}' controls style='max-width:180px;border-radius:8px;'></video>`:''}</p>
          </div>
        </div>
      `;
      document.getElementById('techDetailBody').innerHTML = html;
      document.getElementById('techDetailModal').style.display = 'flex';
    }

    function renderLifeImgs(lifeimg) {
      if (!lifeimg) return '';
      let imgs = [];
      try {
        imgs = JSON.parse(lifeimg);
        if (!Array.isArray(imgs)) imgs = [lifeimg];
      } catch (e) {
        imgs = lifeimg.split(',');
      }
      return imgs.map(url => url ? `<img src='${url}' style='max-width:80px;border-radius:6px;margin:2px;'>` : '').join('');
    }
  </script>
</body>
</html>
