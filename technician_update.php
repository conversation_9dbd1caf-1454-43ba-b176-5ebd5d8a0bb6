<?php
// technician_update.php - 更新技师申请资料

// 禁用错误输出到浏览器，防止HTML错误信息污染JSON响应
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// 开启输出缓冲，确保只输出JSON
ob_start();

header('Content-Type: application/json; charset=utf-8');

// 注册致命错误处理器
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '服务器内部错误']);
        exit;
    }
});

// 统一的JSON响应函数
function sendJsonResponse($success, $message, $data = null) {
    ob_clean();
    $response = ['success' => $success, 'msg' => $message];
    if ($data !== null) {
        $response['data'] = $data;
    }
    echo json_encode($response);
    exit;
}

// 在包含数据库文件前设置错误处理
try {
    require_once 'db.php';

    // 检查数据库连接
    if ($pdo === null) {
        sendJsonResponse(false, '数据库连接失败');
    }
} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    sendJsonResponse(false, '数据库连接失败');
}



if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendJsonResponse(false, '请求方法错误');
}

try {
    // 获取表单数据
    $nick = isset($_POST['nick']) ? trim($_POST['nick']) : '';
    $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    $original_phone = isset($_POST['original_phone']) ? trim($_POST['original_phone']) : '';
    $age = isset($_POST['age']) ? trim($_POST['age']) : '';
    $height = isset($_POST['height']) ? trim($_POST['height']) : '';
    $weight = isset($_POST['weight']) ? trim($_POST['weight']) : '';
    $size = isset($_POST['size']) ? trim($_POST['size']) : '';
    $model = isset($_POST['model']) ? trim($_POST['model']) : '';
    $city = isset($_POST['city']) ? trim($_POST['city']) : '';
    $hotel = isset($_POST['hotel']) ? trim($_POST['hotel']) : '';

    // 基本验证
    if (!$nick || !$phone || !$age || !$height || !$weight || !$size || $model === '' || !$city || !$hotel) {
        sendJsonResponse(false, '请填写完整信息');
    }

    // 如果没有原手机号，使用当前手机号作为原手机号（向后兼容）
    if (!$original_phone) {
        $original_phone = $phone;
    }

    // 验证手机号格式
    if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
        sendJsonResponse(false, '手机号格式不正确');
    }

    // 使用原手机号查询现有技师信息
    $stmt = $pdo->prepare("SELECT * FROM technician WHERE phone = ?");
    $stmt->execute([$original_phone]);
    $existingTech = $stmt->fetch();

    if (!$existingTech) {
        sendJsonResponse(false, '未找到您的申请记录');
    }

    // 如果手机号发生变更，检查新手机号是否已被其他技师使用
    if ($original_phone !== $phone) {
        $checkStmt = $pdo->prepare("SELECT id FROM technician WHERE phone = ? AND id != ?");
        $checkStmt->execute([$phone, $existingTech['id']]);
        $phoneExists = $checkStmt->fetch();

        if ($phoneExists) {
            sendJsonResponse(false, '该手机号已被其他技师使用');
        }
    }

    // 处理文件上传
    $uploadDir = __DIR__ . '/uploads/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    $workimg_path = $existingTech['workimg']; // 保持原有图片
    $lifeimg_str = $existingTech['lifeimg']; // 保持原有图片
    $video_path = $existingTech['video']; // 保持原有视频

    // 处理工作照上传
    if (isset($_FILES['workimg']) && $_FILES['workimg']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['workimg'];
        
        // 验证文件类型
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!in_array($file['type'], $allowedTypes)) {
            sendJsonResponse(false, '工作照只支持 JPG、PNG、GIF 格式');
        }

        // 验证文件大小 (5MB)
        if ($file['size'] > 5 * 1024 * 1024) {
            sendJsonResponse(false, '工作照大小不能超过5MB');
        }

        // 删除旧文件
        if ($workimg_path && file_exists($uploadDir . basename($workimg_path))) {
            unlink($uploadDir . basename($workimg_path));
        }

        // 生成新文件名
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'work_' . date('Ymd_His') . '_' . rand(1000, 9999) . '.' . $extension;
        $filepath = $uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            $workimg_path = 'uploads/' . $filename;
        }
    }

    // 处理生活照上传
    if (isset($_FILES['lifeimg'])) {
        // 检查是否是多文件上传格式
        if (is_array($_FILES['lifeimg']['name'])) {
            // 多文件上传格式
            if (!empty($_FILES['lifeimg']['name'][0])) {
                $files = $_FILES['lifeimg'];
                $lifeimg_paths = [];

                // 删除旧的生活照
                if ($lifeimg_str) {
                    $oldLifeimgs = explode(',', $lifeimg_str);
                    foreach ($oldLifeimgs as $oldImg) {
                        $oldImg = trim($oldImg);
                        if ($oldImg && file_exists($uploadDir . basename($oldImg))) {
                            unlink($uploadDir . basename($oldImg));
                        }
                    }
                }

                for ($i = 0; $i < count($files['name']); $i++) {
                    if ($files['error'][$i] === UPLOAD_ERR_OK) {
                        // 验证文件类型
                        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                        if (!in_array($files['type'][$i], $allowedTypes)) {
                            continue; // 跳过不支持的文件
                        }

                        // 验证文件大小
                        if ($files['size'][$i] > 5 * 1024 * 1024) {
                            continue; // 跳过过大的文件
                        }

                        $extension = pathinfo($files['name'][$i], PATHINFO_EXTENSION);
                        $filename = 'life_' . date('Ymd_His') . '_' . rand(1000, 9999) . '_' . $i . '.' . $extension;
                        $filepath = $uploadDir . $filename;

                        if (move_uploaded_file($files['tmp_name'][$i], $filepath)) {
                            $lifeimg_paths[] = 'uploads/' . $filename;
                        }
                    }
                }

                if (!empty($lifeimg_paths)) {
                    $lifeimg_str = implode(',', $lifeimg_paths);
                }
            }
        } else {
            // 单文件上传格式
            if ($_FILES['lifeimg']['error'] === UPLOAD_ERR_OK) {
                $file = $_FILES['lifeimg'];

                // 验证文件类型
                $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (in_array($file['type'], $allowedTypes)) {
                    // 验证文件大小
                    if ($file['size'] <= 5 * 1024 * 1024) {
                        // 删除旧的生活照
                        if ($lifeimg_str) {
                            $oldLifeimgs = explode(',', $lifeimg_str);
                            foreach ($oldLifeimgs as $oldImg) {
                                $oldImg = trim($oldImg);
                                if ($oldImg && file_exists($uploadDir . basename($oldImg))) {
                                    unlink($uploadDir . basename($oldImg));
                                }
                            }
                        }

                        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                        $filename = 'life_' . date('Ymd_His') . '_' . rand(1000, 9999) . '.' . $extension;
                        $filepath = $uploadDir . $filename;

                        if (move_uploaded_file($file['tmp_name'], $filepath)) {
                            $lifeimg_str = 'uploads/' . $filename;
                        }
                    }
                }
            }
        }
    }

    // 处理视频上传
    if (isset($_FILES['video']) && $_FILES['video']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['video'];
        
        // 验证文件类型
        $allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'];
        if (!in_array($file['type'], $allowedTypes)) {
            sendJsonResponse(false, '视频只支持 MP4、AVI、MOV、WMV 格式');
        }

        // 验证文件大小 (50MB)
        if ($file['size'] > 50 * 1024 * 1024) {
            sendJsonResponse(false, '视频大小不能超过50MB');
        }

        // 删除旧文件
        if ($video_path && file_exists($uploadDir . basename($video_path))) {
            unlink($uploadDir . basename($video_path));
        }

        // 生成新文件名
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'video_' . date('Ymd_His') . '_' . rand(1000, 9999) . '.' . $extension;
        $filepath = $uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            $video_path = 'uploads/' . $filename;
        }
    }

    // 更新数据库
    $stmt = $pdo->prepare("UPDATE technician SET nick=?, phone=?, age=?, height=?, weight=?, size=?, model=?, city=?, hotel=?, workimg=?, lifeimg=?, video=? WHERE id=?");
    $result = $stmt->execute([
        $nick, $phone, $age, $height, $weight, $size, $model, $city, $hotel,
        $workimg_path, $lifeimg_str, $video_path, $existingTech['id']
    ]);

    if ($result) {
        $message = "技师资料修改成功";
        if ($original_phone !== $phone) {
            $message .= "，登录账号已更新";
        }

        sendJsonResponse(true, $message, [
            'phone_changed' => $original_phone !== $phone,
            'new_phone' => $phone
        ]);
    } else {
        sendJsonResponse(false, '修改失败');
    }

} catch (Exception $e) {
    error_log("Technician update error: " . $e->getMessage());
    sendJsonResponse(false, '修改失败，请稍后重试');
}
?>
