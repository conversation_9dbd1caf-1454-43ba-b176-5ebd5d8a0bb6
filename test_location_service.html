<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>位置服务测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #00c6a2;
        }
        .status-card.error {
            border-left-color: #dc3545;
        }
        .status-card.warning {
            border-left-color: #ffc107;
        }
        .btn {
            background: #00c6a2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #00a085;
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.secondary:hover {
            background: #545b62;
        }
        .location-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .city-display {
            display: inline-block;
            background: #00c6a2;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin: 5px;
        }
        .feature-list {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin-bottom: 8px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📍 位置服务功能测试</h1>
        
        <div class="demo-section">
            <h3>🎯 功能概述</h3>
            <p style="color: #666; margin-bottom: 15px;">
                位置服务会在用户进入网站时自动获取用户位置，并在技师页面自动显示对应城市。
            </p>
            
            <div class="feature-list">
                <h4>✨ 主要功能</h4>
                <ul>
                    <li><strong>自动定位</strong> - 页面加载时自动获取用户位置</li>
                    <li><strong>多种定位方式</strong> - GPS定位、IP定位、坐标范围匹配</li>
                    <li><strong>智能回退</strong> - GPS失败时自动使用IP定位</li>
                    <li><strong>城市匹配</strong> - 自动匹配到可用城市列表</li>
                    <li><strong>缓存机制</strong> - 位置信息本地缓存，避免重复请求</li>
                    <li><strong>用户友好</strong> - 清晰的提示和错误处理</li>
                </ul>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>📊 当前状态</h3>
            <div id="status-display">
                <div class="status-card">
                    <strong>位置服务状态:</strong> <span id="service-status">检查中...</span>
                </div>
                <div class="status-card">
                    <strong>当前位置:</strong> <span id="current-location">未获取</span>
                </div>
                <div class="status-card">
                    <strong>当前城市:</strong> <span id="current-city">未设置</span>
                </div>
                <div class="status-card">
                    <strong>数据来源:</strong> <span id="data-source">无</span>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🔧 操作测试</h3>
            <button class="btn" onclick="requestLocation()">🎯 请求位置权限</button>
            <button class="btn" onclick="forceGPSLocation()">📡 强制GPS定位</button>
            <button class="btn" onclick="tryIPLocation()">🌐 尝试IP定位</button>
            <button class="btn secondary" onclick="clearLocationData()">🗑️ 清除位置数据</button>
            <button class="btn secondary" onclick="showStoredData()">💾 查看存储数据</button>
            
            <div id="operation-result" class="location-info" style="display: none;"></div>
        </div>
        
        <div class="demo-section">
            <h3>🏙️ 城市匹配测试</h3>
            <p style="color: #666; font-size: 14px;">
                测试不同城市名称的匹配效果
            </p>
            <input type="text" id="test-city-input" placeholder="输入城市名称测试匹配..." style="
                width: 200px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px;
            ">
            <button class="btn" onclick="testCityMatch()">测试匹配</button>
            
            <div id="match-result" style="margin-top: 10px;"></div>
        </div>
        
        <div class="demo-section">
            <h3>🔗 集成测试</h3>
            <p style="color: #666; font-size: 14px;">
                测试与技师页面的集成效果
            </p>
            <button class="btn" onclick="simulateTechPageVisit()">🎭 模拟访问技师页面</button>
            <button class="btn" onclick="openTechPage()">🔗 打开技师页面</button>
            
            <div id="integration-result" class="location-info" style="display: none;"></div>
        </div>
    </div>

    <script src="js/location-service.js"></script>
    <script>
        // 模拟可用城市列表
        const availableCities = [
            '全部城市', '北京市', '上海市', '天津市', '重庆市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市', '西安市', '郑州市', '济南市', '青岛市', '大连市', '沈阳市', '长春市', '哈尔滨市', '石家庄市', '太原市', '呼和浩特市', '南昌市', '合肥市', '福州市', '厦门市', '长沙市', '南宁市', '海口市', '贵阳市', '昆明市', '拉萨市', '兰州市', '西宁市', '银川市', '乌鲁木齐市',
            '苏州市', '无锡市', '常州市', '徐州市', '扬州市', '泰州市', '南通市', '盐城市', '淮安市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '台州市', '丽水市', '佛山市', '东莞市', '中山市', '珠海市', '泉州市', '漳州市', '莆田市', '三明市', '龙岩市', '宁德市', '烟台市', '潍坊市', '临沂市', '济宁市', '泰安市', '威海市', '日照市', '淄博市', '枣庄市', '东营市', '聊城市', '德州市', '滨州市', '洛阳市', '开封市', '新乡市', '焦作市', '安阳市', '平顶山市'
        ];

        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            
            // 监听位置服务更新
            if (window.LocationService) {
                LocationService.onLocationUpdate((location, city) => {
                    updateStatus();
                    showLocationUpdateToast(city);
                });
            }
            
            // 定期更新状态
            setInterval(updateStatus, 2000);
        });

        // 更新状态显示
        function updateStatus() {
            const serviceStatus = document.getElementById('service-status');
            const currentLocation = document.getElementById('current-location');
            const currentCity = document.getElementById('current-city');
            const dataSource = document.getElementById('data-source');

            if (window.LocationService) {
                serviceStatus.textContent = '✅ 已加载';
                serviceStatus.parentElement.className = 'status-card';
                
                const location = LocationService.getCurrentLocation();
                const city = LocationService.getCurrentCity();
                
                if (location) {
                    currentLocation.textContent = `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)} (精度: ${location.accuracy}m)`;
                    currentLocation.parentElement.className = 'status-card';
                } else {
                    currentLocation.textContent = '未获取';
                    currentLocation.parentElement.className = 'status-card warning';
                }
                
                if (city) {
                    currentCity.innerHTML = `<span class="city-display">${city.name}</span>`;
                    currentCity.parentElement.className = 'status-card';
                    dataSource.textContent = getSourceText(city.source);
                } else {
                    currentCity.textContent = '未设置';
                    currentCity.parentElement.className = 'status-card warning';
                    dataSource.textContent = '无';
                }
            } else {
                serviceStatus.textContent = '❌ 未加载';
                serviceStatus.parentElement.className = 'status-card error';
            }
        }

        // 获取数据源文本
        function getSourceText(source) {
            const sourceMap = {
                'gps': '🛰️ GPS定位',
                'ip': '🌐 IP定位',
                'amap': '🗺️ 高德地图',
                'coordinate_range': '📍 坐标范围匹配',
                'default': '🏠 默认设置',
                'error_fallback': '⚠️ 错误回退'
            };
            return sourceMap[source] || source;
        }

        // 请求位置权限
        function requestLocation() {
            const resultDiv = document.getElementById('operation-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在请求位置权限...';
            
            if (window.LocationService) {
                LocationService.getUserLocation({
                    showPrompt: true,
                    fallbackToIP: true
                }).then(location => {
                    resultDiv.textContent = `✅ 位置获取成功:
纬度: ${location.latitude}
经度: ${location.longitude}
精度: ${location.accuracy}m
时间: ${new Date(location.timestamp).toLocaleString()}`;
                    updateStatus();
                }).catch(error => {
                    resultDiv.textContent = `❌ 位置获取失败: ${error.message}`;
                });
            } else {
                resultDiv.textContent = '❌ 位置服务未加载';
            }
        }

        // 强制GPS定位
        function forceGPSLocation() {
            const resultDiv = document.getElementById('operation-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在进行GPS定位...';
            
            if (window.LocationService) {
                LocationService.getUserLocation({
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 0,
                    showPrompt: true,
                    fallbackToIP: false
                }).then(location => {
                    resultDiv.textContent = `✅ GPS定位成功:
纬度: ${location.latitude}
经度: ${location.longitude}
精度: ${location.accuracy}m
来源: GPS`;
                    updateStatus();
                }).catch(error => {
                    resultDiv.textContent = `❌ GPS定位失败: ${error.message}`;
                });
            } else {
                resultDiv.textContent = '❌ 位置服务未加载';
            }
        }

        // 尝试IP定位
        function tryIPLocation() {
            const resultDiv = document.getElementById('operation-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在进行IP定位...';
            
            if (window.LocationService) {
                LocationService.getLocationByIP().then(location => {
                    resultDiv.textContent = `✅ IP定位成功:
纬度: ${location.latitude}
经度: ${location.longitude}
城市: ${LocationService.getCurrentCity().name}
来源: IP定位`;
                    updateStatus();
                }).catch(error => {
                    resultDiv.textContent = `❌ IP定位失败: ${error.message}`;
                });
            } else {
                resultDiv.textContent = '❌ 位置服务未加载';
            }
        }

        // 清除位置数据
        function clearLocationData() {
            if (window.LocationService) {
                LocationService.clearLocation();
                updateStatus();
                
                const resultDiv = document.getElementById('operation-result');
                resultDiv.style.display = 'block';
                resultDiv.textContent = '✅ 位置数据已清除';
            }
        }

        // 查看存储数据
        function showStoredData() {
            const resultDiv = document.getElementById('operation-result');
            resultDiv.style.display = 'block';
            
            try {
                const stored = localStorage.getItem('userLocationData');
                if (stored) {
                    const data = JSON.parse(stored);
                    resultDiv.textContent = `💾 存储的位置数据:
${JSON.stringify(data, null, 2)}

存储时间: ${new Date(data.timestamp).toLocaleString()}`;
                } else {
                    resultDiv.textContent = '📭 没有存储的位置数据';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 读取存储数据失败: ${error.message}`;
            }
        }

        // 测试城市匹配
        function testCityMatch() {
            const input = document.getElementById('test-city-input');
            const resultDiv = document.getElementById('match-result');
            const testCityName = input.value.trim();
            
            if (!testCityName) {
                resultDiv.innerHTML = '<div style="color: #dc3545;">请输入城市名称</div>';
                return;
            }
            
            const matchedCity = findMatchingCity(testCityName);
            
            if (matchedCity) {
                resultDiv.innerHTML = `
                    <div style="color: #28a745; margin-top: 10px;">
                        ✅ 匹配成功: <span class="city-display">${matchedCity}</span>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div style="color: #dc3545; margin-top: 10px;">
                        ❌ 未找到匹配的城市: "${testCityName}"
                    </div>
                `;
            }
        }

        // 查找匹配的城市（复用technicians.html的逻辑）
        function findMatchingCity(locationCityName) {
            const cleanLocationName = locationCityName.replace(/市$/, '');
            
            for (const city of availableCities) {
                const cleanCityName = city.replace(/市$/, '');
                if (cleanCityName === cleanLocationName || 
                    city === locationCityName ||
                    cleanLocationName.includes(cleanCityName) ||
                    cleanCityName.includes(cleanLocationName)) {
                    return city;
                }
            }
            
            return null;
        }

        // 模拟技师页面访问
        function simulateTechPageVisit() {
            const resultDiv = document.getElementById('integration-result');
            resultDiv.style.display = 'block';
            
            const city = window.LocationService ? LocationService.getCurrentCity() : null;
            
            if (city && city.name && city.name !== '全部城市') {
                const matchedCity = findMatchingCity(city.name);
                if (matchedCity) {
                    resultDiv.textContent = `🎭 模拟技师页面访问:

1. 检测到用户位置: ${city.name}
2. 匹配到可用城市: ${matchedCity}
3. 自动设置城市筛选: ${matchedCity}
4. 显示该城市的技师列表

✅ 集成测试成功！用户将看到 ${matchedCity} 的技师。`;
                } else {
                    resultDiv.textContent = `🎭 模拟技师页面访问:

1. 检测到用户位置: ${city.name}
2. ❌ 未找到匹配的城市
3. 使用默认设置: 全部城市
4. 显示所有技师列表

⚠️ 城市匹配失败，使用默认显示。`;
                }
            } else {
                resultDiv.textContent = `🎭 模拟技师页面访问:

1. ❌ 未获取到用户位置
2. 使用默认设置: 全部城市
3. 显示所有技师列表

💡 建议用户手动选择城市或授权位置权限。`;
            }
        }

        // 打开技师页面
        function openTechPage() {
            window.open('technicians.html', '_blank');
        }

        // 显示位置更新提示
        function showLocationUpdateToast(city) {
            // 避免重复显示
            if (document.getElementById('location-update-toast')) return;

            const toast = document.createElement('div');
            toast.id = 'location-update-toast';
            toast.style.cssText = `
                position: fixed; top: 20px; right: 20px;
                background: rgba(40, 167, 69, 0.95); color: white; padding: 12px 20px;
                border-radius: 8px; z-index: 10000; font-size: 14px;
                box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
                animation: slideInRight 0.3s ease;
            `;
            
            toast.innerHTML = `
                📍 位置更新: <strong>${city.name}</strong>
            `;

            // 添加动画样式
            if (!document.getElementById('toast-styles')) {
                const style = document.createElement('style');
                style.id = 'toast-styles';
                style.textContent = `
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(toast);
            
            // 3秒后自动隐藏
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.style.animation = 'slideInRight 0.3s ease reverse';
                    setTimeout(() => toast.remove(), 300);
                }
            }, 3000);
        }
    </script>
</body>
</html>
