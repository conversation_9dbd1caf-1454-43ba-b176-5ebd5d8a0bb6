<?php
// 测试文件上传功能
session_start();
$_SESSION['admin_login'] = true; // 模拟登录状态

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo json_encode([
        'success' => true,
        'msg' => '测试成功',
        'data' => [
            'POST' => $_POST,
            'FILES' => $_FILES,
            'has_workimg' => isset($_FILES['workimg']),
            'has_lifeimg' => isset($_FILES['lifeimg']),
            'has_video' => isset($_FILES['video']),
            'workimg_error' => isset($_FILES['workimg']) ? $_FILES['workimg']['error'] : 'not set',
            'lifeimg_error' => isset($_FILES['lifeimg']) ? $_FILES['lifeimg']['error'] : 'not set',
            'video_error' => isset($_FILES['video']) ? $_FILES['video']['error'] : 'not set'
        ]
    ]);
} else {
    echo json_encode(['success' => false, 'msg' => '只支持POST请求']);
}
?>
