<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ad<PERSON><PERSON><PERSON><PERSON>测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .status.loaded {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 AdminCommon功能测试</h1>
        
        <div id="load-status" class="status">正在检查AdminCommon加载状态...</div>
        
        <div class="test-section">
            <h3>📋 AdminCommon对象检查</h3>
            <button onclick="checkAdminCommon()">检查AdminCommon对象</button>
            <div id="object-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🔌 API方法测试</h3>
            <button onclick="testAPIMethods()">测试所有API方法</button>
            <button onclick="testPostRawJSON()">专门测试postRawJSON</button>
            <div id="api-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>⭐ 热门城市API测试</h3>
            <button onclick="testHotCityAPI()">测试热门城市API</button>
            <div id="hot-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🔄 缓存清理</h3>
            <p style="color: #666; font-size: 14px;">
                如果遇到方法不存在的错误，可能是浏览器缓存问题
            </p>
            <button onclick="clearCache()">清理缓存并重新加载</button>
            <button onclick="forceReload()">强制刷新页面</button>
            <div id="cache-result" class="result"></div>
        </div>
    </div>

    <!-- 加载AdminCommon -->
    <script src="admin/js/admin-common.js?v=<?php echo time(); ?>"></script>
    
    <script>
        // 检查AdminCommon加载状态
        function checkLoadStatus() {
            const statusDiv = document.getElementById('load-status');
            
            if (typeof AdminCommon !== 'undefined') {
                statusDiv.textContent = '✅ AdminCommon已成功加载';
                statusDiv.className = 'status loaded';
            } else {
                statusDiv.textContent = '❌ AdminCommon加载失败';
                statusDiv.className = 'status error';
            }
        }
        
        function checkAdminCommon() {
            const resultDiv = document.getElementById('object-result');
            
            try {
                if (typeof AdminCommon === 'undefined') {
                    resultDiv.textContent = '❌ AdminCommon对象未定义';
                    resultDiv.className = 'result error';
                    return;
                }
                
                const apiMethods = AdminCommon.api ? Object.keys(AdminCommon.api) : [];
                const uiMethods = AdminCommon.ui ? Object.keys(AdminCommon.ui) : [];
                
                resultDiv.textContent = `AdminCommon对象检查结果:

✅ AdminCommon对象存在
✅ AdminCommon.api存在: ${AdminCommon.api ? '是' : '否'}
✅ AdminCommon.ui存在: ${AdminCommon.ui ? '是' : '否'}

API方法列表 (${apiMethods.length}个):
${apiMethods.map(method => `- ${method}`).join('\n')}

UI方法列表 (${uiMethods.length}个):
${uiMethods.slice(0, 5).map(method => `- ${method}`).join('\n')}
${uiMethods.length > 5 ? `... 还有 ${uiMethods.length - 5} 个方法` : ''}

postRawJSON方法存在: ${AdminCommon.api && typeof AdminCommon.api.postRawJSON === 'function' ? '✅ 是' : '❌ 否'}`;
                
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `检查过程中出错: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        function testAPIMethods() {
            const resultDiv = document.getElementById('api-result');
            
            if (typeof AdminCommon === 'undefined' || !AdminCommon.api) {
                resultDiv.textContent = '❌ AdminCommon.api不可用';
                resultDiv.className = 'result error';
                return;
            }
            
            const methods = ['get', 'post', 'postForm', 'postJSON', 'postRawJSON'];
            const results = [];
            
            methods.forEach(method => {
                const exists = typeof AdminCommon.api[method] === 'function';
                results.push(`${exists ? '✅' : '❌'} ${method}: ${exists ? '存在' : '不存在'}`);
            });
            
            resultDiv.textContent = `API方法测试结果:

${results.join('\n')}

${AdminCommon.api.postRawJSON ? '✅ postRawJSON方法可用，可以测试热门城市功能' : '❌ postRawJSON方法不可用，需要检查代码'}`;
            
            resultDiv.className = AdminCommon.api.postRawJSON ? 'result success' : 'result error';
        }
        
        function testPostRawJSON() {
            const resultDiv = document.getElementById('api-result');
            
            if (!AdminCommon.api || typeof AdminCommon.api.postRawJSON !== 'function') {
                resultDiv.textContent = '❌ postRawJSON方法不存在';
                resultDiv.className = 'result error';
                return;
            }
            
            resultDiv.textContent = '正在测试postRawJSON方法...';
            resultDiv.className = 'result';
            
            // 测试调用（发送到一个不存在的端点来测试方法本身）
            AdminCommon.api.postRawJSON('test_endpoint.php', {
                test: 'data',
                timestamp: Date.now()
            })
            .then(response => {
                resultDiv.textContent = `postRawJSON方法测试结果:

✅ 方法调用成功
✅ 能够发送JSON数据
✅ 返回Promise对象

响应: ${JSON.stringify(response, null, 2)}

注意: 这是预期的404错误，说明方法本身工作正常`;
                resultDiv.className = 'result success';
            })
            .catch(error => {
                resultDiv.textContent = `postRawJSON方法测试结果:

✅ 方法调用成功
✅ 能够发送JSON数据
✅ 正确处理错误

错误信息: ${error.message}

注意: 这是预期的网络错误，说明方法本身工作正常`;
                resultDiv.className = 'result success';
            });
        }
        
        function testHotCityAPI() {
            const resultDiv = document.getElementById('hot-result');
            
            if (!AdminCommon.api || typeof AdminCommon.api.postRawJSON !== 'function') {
                resultDiv.textContent = '❌ postRawJSON方法不可用，无法测试热门城市API';
                resultDiv.className = 'result error';
                return;
            }
            
            resultDiv.textContent = '正在测试热门城市API...';
            resultDiv.className = 'result';
            
            // 测试热门城市API
            AdminCommon.api.postRawJSON('admin/city_set_hot.php', {
                id: 1,
                is_hot: 1
            })
            .then(response => {
                resultDiv.textContent = `热门城市API测试结果:

✅ postRawJSON方法工作正常
✅ 能够调用热门城市API

API响应:
${JSON.stringify(response, null, 2)}

${response.success ? '✅ API调用成功' : '⚠️ API返回错误（可能是权限或数据问题）'}`;
                resultDiv.className = response.success ? 'result success' : 'result error';
            })
            .catch(error => {
                resultDiv.textContent = `热门城市API测试失败:

错误信息: ${error.message}

可能原因:
1. 未登录后台管理系统
2. API文件不存在
3. 网络连接问题`;
                resultDiv.className = 'result error';
            });
        }
        
        function clearCache() {
            const resultDiv = document.getElementById('cache-result');
            
            resultDiv.textContent = `缓存清理操作:

1. 尝试清理浏览器缓存...
2. 重新加载AdminCommon脚本...

请手动按 Ctrl+F5 (Windows) 或 Cmd+Shift+R (Mac) 强制刷新页面`;
            resultDiv.className = 'result';
            
            // 尝试重新加载脚本
            const script = document.createElement('script');
            script.src = 'admin/js/admin-common.js?v=' + Date.now();
            script.onload = function() {
                resultDiv.textContent += '\n\n✅ AdminCommon脚本重新加载完成';
                checkLoadStatus();
                checkAdminCommon();
            };
            script.onerror = function() {
                resultDiv.textContent += '\n\n❌ AdminCommon脚本重新加载失败';
            };
            document.head.appendChild(script);
        }
        
        function forceReload() {
            window.location.reload(true);
        }
        
        // 页面加载时检查状态
        window.onload = function() {
            checkLoadStatus();
            setTimeout(checkAdminCommon, 100);
        };
    </script>
</body>
</html>
