<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单轮播图测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        /* 轮播图样式 */
        .carousel-container {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;
            border-radius: 12px;
            border: 2px solid #ddd;
            margin: 20px 0;
        }
        
        .carousel-wrapper {
            display: flex;
            width: 100%;
            height: 100%;
            transition: transform 0.5s ease-in-out;
        }
        
        .carousel-slide {
            min-width: 100%;
            height: 100%;
            position: relative;
        }
        
        .carousel-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }
        
        .carousel-indicators {
            position: absolute;
            bottom: 12px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
            z-index: 10;
        }
        
        .carousel-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .carousel-indicator.active {
            background: rgba(255, 255, 255, 0.9);
            transform: scale(1.2);
        }
        
        .carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.3);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10;
        }
        
        .carousel-nav:hover {
            background: rgba(0, 0, 0, 0.6);
            transform: translateY(-50%) scale(1.1);
        }
        
        .carousel-nav.prev {
            left: 12px;
        }
        
        .carousel-nav.next {
            right: 12px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎠 简单轮播图测试</h1>
        
        <div class="info">
            <strong>测试说明：</strong><br>
            1. 使用真实的轮播图数据<br>
            2. 测试图片加载和显示<br>
            3. 测试轮播图切换功能
        </div>
        
        <div>
            <button class="btn" onclick="loadFromAPI()">从API加载</button>
            <button class="btn" onclick="loadTestData()">加载测试数据</button>
            <button class="btn" onclick="nextSlide()">下一张</button>
            <button class="btn" onclick="prevSlide()">上一张</button>
        </div>
        
        <div id="carousel" class="carousel-container">
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">
                点击按钮加载轮播图
            </div>
        </div>
        
        <div id="info" class="info" style="display: none;"></div>
    </div>

    <script>
        let currentSlide = 0;
        let slides = [];
        
        // 从API加载轮播图
        async function loadFromAPI() {
            const infoDiv = document.getElementById('info');
            infoDiv.style.display = 'block';
            infoDiv.innerHTML = '正在从API加载轮播图...';
            
            try {
                const response = await fetch('banner_list.php');
                const data = await response.json();
                
                infoDiv.innerHTML = `API响应: ${JSON.stringify(data, null, 2)}`;
                
                if (data.success && Array.isArray(data.data) && data.data.length > 0) {
                    slides = data.data;
                    renderCarousel();
                    infoDiv.innerHTML += `<br><br>✅ 成功加载 ${slides.length} 个轮播图`;
                } else {
                    infoDiv.innerHTML += '<br><br>❌ 没有轮播图数据';
                }
            } catch (error) {
                infoDiv.innerHTML += `<br><br>❌ 加载失败: ${error.message}`;
            }
        }
        
        // 加载测试数据
        function loadTestData() {
            slides = [
                {
                    id: 1,
                    img: 'uploads/banner_20250718_200312_9681.jpg',
                    link: ''
                },
                {
                    id: 2,
                    img: 'uploads/banner_20250718_223358_2505.jpg',
                    link: ''
                },
                {
                    id: 3,
                    img: 'uploads/banner_20250718_223404_6006.jpg',
                    link: ''
                },
                {
                    id: 4,
                    img: 'uploads/banner_20250718_223412_6801.jpg',
                    link: ''
                }
            ];
            
            renderCarousel();
            
            const infoDiv = document.getElementById('info');
            infoDiv.style.display = 'block';
            infoDiv.innerHTML = `✅ 加载了 ${slides.length} 个测试轮播图`;
        }
        
        // 渲染轮播图
        function renderCarousel() {
            const carousel = document.getElementById('carousel');
            currentSlide = 0;
            
            if (slides.length === 0) {
                carousel.innerHTML = `
                    <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">
                        没有轮播图数据
                    </div>
                `;
                return;
            }
            
            const html = `
                <div class="carousel-wrapper" id="carousel-wrapper">
                    ${slides.map((slide, index) => `
                        <div class="carousel-slide">
                            <img src="${slide.img}" 
                                 alt="轮播图${index + 1}"
                                 onerror="this.src='images/lbt.png'; console.log('图片加载失败:', '${slide.img}')"
                                 onload="console.log('图片加载成功:', '${slide.img}')"
                                 style="${slide.link ? 'cursor: pointer;' : ''}"
                                 ${slide.link ? `onclick="window.open('${slide.link}', '_blank')"` : ''} />
                        </div>
                    `).join('')}
                </div>
                
                ${slides.length > 1 ? `
                    <button class="carousel-nav prev" onclick="prevSlide()">‹</button>
                    <button class="carousel-nav next" onclick="nextSlide()">›</button>
                    
                    <div class="carousel-indicators">
                        ${slides.map((_, index) => `
                            <div class="carousel-indicator ${index === 0 ? 'active' : ''}"
                                 onclick="goToSlide(${index})"></div>
                        `).join('')}
                    </div>
                ` : ''}
            `;
            
            carousel.innerHTML = html;
            console.log('轮播图渲染完成，数量:', slides.length);
        }
        
        // 切换到指定幻灯片
        function goToSlide(index) {
            if (index < 0 || index >= slides.length) return;
            
            currentSlide = index;
            updateSlidePosition();
            updateIndicators();
        }
        
        // 下一张
        function nextSlide() {
            const nextIndex = (currentSlide + 1) % slides.length;
            goToSlide(nextIndex);
        }
        
        // 上一张
        function prevSlide() {
            const prevIndex = (currentSlide - 1 + slides.length) % slides.length;
            goToSlide(prevIndex);
        }
        
        // 更新幻灯片位置
        function updateSlidePosition() {
            const wrapper = document.getElementById('carousel-wrapper');
            if (wrapper) {
                const translateX = -currentSlide * 100;
                wrapper.style.transform = `translateX(${translateX}%)`;
            }
        }
        
        // 更新指示器
        function updateIndicators() {
            const indicators = document.querySelectorAll('.carousel-indicator');
            indicators.forEach((indicator, index) => {
                indicator.classList.toggle('active', index === currentSlide);
            });
        }
        
        // 自动播放
        let autoPlayInterval;
        
        function startAutoPlay() {
            if (slides.length <= 1) return;
            
            stopAutoPlay();
            autoPlayInterval = setInterval(() => {
                nextSlide();
            }, 3000);
        }
        
        function stopAutoPlay() {
            if (autoPlayInterval) {
                clearInterval(autoPlayInterval);
                autoPlayInterval = null;
            }
        }
        
        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            // 自动加载测试数据
            setTimeout(() => {
                loadTestData();
                setTimeout(() => {
                    startAutoPlay();
                }, 1000);
            }, 500);
        });
    </script>
</body>
</html>
