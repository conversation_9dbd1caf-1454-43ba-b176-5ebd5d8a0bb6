<?php
// tech_edit.php - 编辑技师资料

// 禁用错误输出到浏览器，防止HTML错误信息污染JSON响应
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// 开启输出缓冲，确保只输出JSON
ob_start();

// 设置错误处理
set_error_handler(function($severity, $message, $file, $line) {
    error_log("PHP Error: $message in $file on line $line");
    return true; // 阻止默认错误处理
});

// 捕获致命错误
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['success' => false, 'msg' => '服务器内部错误']);
    }
});

session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    ob_clean();
    http_response_code(401);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

// 检查数据库连接
if (!$pdo) {
    ob_clean();
    echo json_encode(['success' => false, 'msg' => '数据库连接失败']);
    exit;
}



if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_clean();
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

try {
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    
    if ($id <= 0) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '无效的技师ID']);
        exit;
    }

    // 获取表单数据
    $nick = isset($_POST['nick']) ? trim($_POST['nick']) : '';
    $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    $age = isset($_POST['age']) ? trim($_POST['age']) : '';
    $height = isset($_POST['height']) ? trim($_POST['height']) : '';
    $weight = isset($_POST['weight']) ? trim($_POST['weight']) : '';
    $size = isset($_POST['size']) ? trim($_POST['size']) : '';
    $model = isset($_POST['model']) ? trim($_POST['model']) : '';
    $city = isset($_POST['city']) ? trim($_POST['city']) : '';
    $hotel = isset($_POST['hotel']) ? trim($_POST['hotel']) : '';
    $virtual_orders = isset($_POST['virtual_orders']) ? intval($_POST['virtual_orders']) : 0;

    // 基本验证
    if (!$nick || !$phone || !$age || !$height || !$weight || !$size || $model === '' || !$city || !$hotel) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '请填写完整信息']);
        exit;
    }

    // 验证虚拟单量
    if ($virtual_orders < 0 || $virtual_orders > 9999) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '虚拟单量必须在0-9999之间']);
        exit;
    }

    // 查询现有技师信息
    $stmt = $pdo->prepare("SELECT * FROM technician WHERE id = ?");
    $stmt->execute([$id]);
    $existingTech = $stmt->fetch();
    
    if (!$existingTech) {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '技师不存在']);
        exit;
    }

    // 处理文件上传
    $uploadDir = __DIR__ . '/../uploads/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    $workimg_path = $existingTech['workimg']; // 保持原有图片
    $lifeimg_str = $existingTech['lifeimg']; // 保持原有图片
    $video_path = $existingTech['video']; // 保持原有视频

    // 处理工作照上传
    if (isset($_FILES['workimg']) && $_FILES['workimg']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['workimg'];
        
        // 验证文件类型
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!in_array($file['type'], $allowedTypes)) {
            ob_clean();
            echo json_encode(['success' => false, 'msg' => '工作照只支持 JPG、PNG、GIF 格式']);
            exit;
        }

        // 验证文件大小 (5MB)
        if ($file['size'] > 5 * 1024 * 1024) {
            ob_clean();
            echo json_encode(['success' => false, 'msg' => '工作照大小不能超过5MB']);
            exit;
        }

        // 删除旧文件
        if ($workimg_path && file_exists($uploadDir . basename($workimg_path))) {
            unlink($uploadDir . basename($workimg_path));
        }

        // 生成新文件名
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'work_' . date('Ymd_His') . '_' . rand(1000, 9999) . '.' . $extension;
        $filepath = $uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            $workimg_path = 'uploads/' . $filename;
        }
    }

    // 处理生活照上传
    if (isset($_FILES['lifeimg']) && !empty($_FILES['lifeimg']['name'][0])) {
        $files = $_FILES['lifeimg'];
        $lifeimg_paths = [];
        
        // 删除旧的生活照
        if ($lifeimg_str) {
            $oldLifeimgs = explode(',', $lifeimg_str);
            foreach ($oldLifeimgs as $oldImg) {
                $oldImg = trim($oldImg);
                if ($oldImg && file_exists($uploadDir . basename($oldImg))) {
                    unlink($uploadDir . basename($oldImg));
                }
            }
        }

        for ($i = 0; $i < count($files['name']); $i++) {
            if ($files['error'][$i] === UPLOAD_ERR_OK) {
                // 验证文件类型
                $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (!in_array($files['type'][$i], $allowedTypes)) {
                    continue; // 跳过不支持的文件
                }

                // 验证文件大小
                if ($files['size'][$i] > 5 * 1024 * 1024) {
                    continue; // 跳过过大的文件
                }

                $extension = pathinfo($files['name'][$i], PATHINFO_EXTENSION);
                $filename = 'life_' . date('Ymd_His') . '_' . rand(1000, 9999) . '_' . $i . '.' . $extension;
                $filepath = $uploadDir . $filename;

                if (move_uploaded_file($files['tmp_name'][$i], $filepath)) {
                    $lifeimg_paths[] = 'uploads/' . $filename;
                }
            }
        }
        
        if (!empty($lifeimg_paths)) {
            $lifeimg_str = implode(',', $lifeimg_paths);
        }
    }

    // 处理视频上传
    if (isset($_FILES['video']) && $_FILES['video']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['video'];
        
        // 验证文件类型
        $allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'];
        if (!in_array($file['type'], $allowedTypes)) {
            ob_clean();
            echo json_encode(['success' => false, 'msg' => '视频只支持 MP4、AVI、MOV、WMV 格式']);
            exit;
        }

        // 验证文件大小 (50MB)
        if ($file['size'] > 50 * 1024 * 1024) {
            ob_clean();
            echo json_encode(['success' => false, 'msg' => '视频大小不能超过50MB']);
            exit;
        }

        // 删除旧文件
        if ($video_path && file_exists($uploadDir . basename($video_path))) {
            unlink($uploadDir . basename($video_path));
        }

        // 生成新文件名
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'video_' . date('Ymd_His') . '_' . rand(1000, 9999) . '.' . $extension;
        $filepath = $uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            $video_path = 'uploads/' . $filename;
        }
    }

    // 检查城市是否发生变更
    $cityChanged = false;
    $previousCity = $existingTech['city'];
    if ($previousCity !== $city) {
        $cityChanged = true;
    }

    // 更新数据库
    $stmt = $pdo->prepare("UPDATE technician SET nick=?, phone=?, age=?, height=?, weight=?, size=?, model=?, city=?, hotel=?, workimg=?, lifeimg=?, video=?, virtual_orders=? WHERE id=?");
    $result = $stmt->execute([
        $nick, $phone, $age, $height, $weight, $size, $model, $city, $hotel,
        $workimg_path, $lifeimg_str, $video_path, $virtual_orders, $id
    ]);

    if ($result) {
        // 如果城市发生变更，自动添加技师动态
        if ($cityChanged) {
            addTechnicianFeed($pdo, $id, $nick, $age, $height, $weight, $size, $city, $previousCity);
        }

        ob_clean();
        echo json_encode([
            'success' => true,
            'msg' => "技师资料修改成功" . ($cityChanged ? "，已生成城市变更动态" : "")
        ]);
    } else {
        ob_clean();
        echo json_encode(['success' => false, 'msg' => '修改失败']);
    }

} catch (Exception $e) {
    error_log("Tech edit error: " . $e->getMessage());
    ob_clean();
    echo json_encode(['success' => false, 'msg' => '修改失败: ' . $e->getMessage()]);
}

// 添加技师动态的函数
function addTechnicianFeed($pdo, $technician_id, $technician_name, $age, $height, $weight, $service_years, $city, $previous_city = null) {
    try {
        // 创建技师动态表（如果不存在）
        $createTableSql = "CREATE TABLE IF NOT EXISTS technician_feeds (
            id INT AUTO_INCREMENT PRIMARY KEY,
            technician_id INT NOT NULL,
            technician_name VARCHAR(100) NOT NULL,
            age INT DEFAULT NULL,
            height INT DEFAULT NULL,
            weight INT DEFAULT NULL,
            service_years INT DEFAULT NULL,
            city VARCHAR(100) NOT NULL,
            previous_city VARCHAR(100) DEFAULT NULL,
            action_type ENUM('join', 'move') NOT NULL DEFAULT 'join',
            content TEXT,
            avatar VARCHAR(255) DEFAULT 'images/tx.jpg',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_active TINYINT(1) DEFAULT 1,
            INDEX idx_technician_id (technician_id),
            INDEX idx_city (city),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        $pdo->exec($createTableSql);

        // 确定动作类型
        $action_type = $previous_city ? 'move' : 'join';

        // 生成动态内容
        $specs = [];
        if ($age) $specs[] = $age;
        if ($height) $specs[] = $height;
        if ($weight) $specs[] = $weight;
        if ($service_years) $specs[] = $service_years;

        $specsText = !empty($specs) ? ' ' . implode('/', $specs) : '';
        $content = "技师{$technician_name}{$specsText} 到{$city}了";

        // 插入技师动态
        $insertSql = "INSERT INTO technician_feeds
                      (technician_id, technician_name, age, height, weight, service_years,
                       city, previous_city, action_type, content)
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $pdo->prepare($insertSql);
        $result = $stmt->execute([
            $technician_id,
            $technician_name,
            $age,
            $height,
            $weight,
            $service_years,
            $city,
            $previous_city,
            $action_type,
            $content
        ]);

        if ($result) {
            error_log("技师动态添加成功: {$content}");
        } else {
            error_log("技师动态添加失败");
        }

    } catch (Exception $e) {
        error_log("添加技师动态失败: " . $e->getMessage());
    }
}
?>
