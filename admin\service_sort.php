<?php
// service_sort.php - 服务项目排序
session_start();
if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'msg' => '未登录']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');
require_once '../db.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'msg' => '请求方法错误']);
    exit;
}

try {
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $direction = isset($_POST['direction']) ? trim($_POST['direction']) : '';
    
    if ($id <= 0) {
        echo json_encode(['success' => false, 'msg' => '无效的服务ID']);
        exit;
    }

    if (!in_array($direction, ['up', 'down'])) {
        echo json_encode(['success' => false, 'msg' => '无效的移动方向']);
        exit;
    }

    // 查询当前服务信息
    $stmt = $pdo->prepare("SELECT id, name, sort_order FROM services WHERE id = ?");
    $stmt->execute([$id]);
    $currentService = $stmt->fetch();
    
    if (!$currentService) {
        echo json_encode(['success' => false, 'msg' => '服务项目不存在']);
        exit;
    }

    $currentSort = $currentService['sort_order'];
    
    if ($direction === 'up') {
        // 上移：找到排序值小于当前值的最大项目
        $stmt = $pdo->prepare("SELECT id, sort_order FROM services WHERE sort_order < ? ORDER BY sort_order DESC LIMIT 1");
        $stmt->execute([$currentSort]);
        $targetService = $stmt->fetch();
        
        if (!$targetService) {
            echo json_encode(['success' => false, 'msg' => '已经是第一个了']);
            exit;
        }
        
        $targetSort = $targetService['sort_order'];
        $targetId = $targetService['id'];
        
    } else { // down
        // 下移：找到排序值大于当前值的最小项目
        $stmt = $pdo->prepare("SELECT id, sort_order FROM services WHERE sort_order > ? ORDER BY sort_order ASC LIMIT 1");
        $stmt->execute([$currentSort]);
        $targetService = $stmt->fetch();
        
        if (!$targetService) {
            echo json_encode(['success' => false, 'msg' => '已经是最后一个了']);
            exit;
        }
        
        $targetSort = $targetService['sort_order'];
        $targetId = $targetService['id'];
    }

    // 交换排序值
    $pdo->beginTransaction();
    
    try {
        // 更新当前项目的排序值
        $stmt = $pdo->prepare("UPDATE services SET sort_order = ? WHERE id = ?");
        $stmt->execute([$targetSort, $id]);
        
        // 更新目标项目的排序值
        $stmt = $pdo->prepare("UPDATE services SET sort_order = ? WHERE id = ?");
        $stmt->execute([$currentSort, $targetId]);
        
        $pdo->commit();
        
        $directionText = $direction === 'up' ? '上移' : '下移';
        echo json_encode([
            'success' => true, 
            'msg' => "服务项目「{$currentService['name']}」{$directionText}成功"
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }

} catch (Exception $e) {
    error_log("Service sort error: " . $e->getMessage());
    echo json_encode(['success' => false, 'msg' => '排序失败: ' . $e->getMessage()]);
}
?>
