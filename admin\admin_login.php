<?php
// admin_login.php
session_start();

// 获取存储的管理员密码
function getAdminPassword() {
    $passwordFile = __DIR__ . '/admin_password.txt';
    if (file_exists($passwordFile)) {
        return trim(file_get_contents($passwordFile));
    }
    // 默认密码（向后兼容）
    return 'liyuchun110';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = isset($_POST['username']) ? trim($_POST['username']) : '';
    $password = isset($_POST['password']) ? trim($_POST['password']) : '';

    $adminPassword = getAdminPassword();

    if ($username === 'admin' && $password === $adminPassword) {
        $_SESSION['admin_login'] = true;
        header('Location: admin_index.php');
        exit;
    } else {
        header('Location: admin_login.html?error=1');
        exit;
    }
}
// 非POST请求跳转到登录页
header('Location: admin_login.html');
exit;
