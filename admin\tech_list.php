<?php
ob_clean();
error_reporting(0);
ini_set('display_errors', 0);
require_once '../db.php';
header('Content-Type: application/json; charset=utf-8');
// 自动建表（如无则创建）
$pdo->exec("CREATE TABLE IF NOT EXISTS technician (
  id INT AUTO_INCREMENT PRIMARY KEY,
  nick VA<PERSON>HA<PERSON>(64),
  phone VARCHAR(32),
  age INT,
  height INT,
  weight INT,
  size VARCHAR(32),
  city VARCHAR(32),
  hotel VARCHAR(64),
  workimg VARCHAR(255),
  lifeimg TEXT,
  video VARCHAR(255),
  status VARCHAR(16) DEFAULT 'pending',
  apply_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  password VARCHAR(64),
  reject_reason VARCHAR(255),
  last_visit DATETIME DEFAULT NULL,
  is_black TINYINT(1) DEFAULT 0,
  is_recommended TINYINT(1) DEFAULT 0 COMMENT '是否推荐(1=推荐,0=普通)',
  virtual_orders INT DEFAULT 0 COMMENT '虚拟单量',
  is_resting TINYINT(1) DEFAULT 0 COMMENT '是否休息(1=休息中,0=工作中)'
) CHARSET=utf8mb4;");

// 检查并添加推荐字段（兼容已有数据）
try {
    $pdo->exec("ALTER TABLE technician ADD COLUMN is_recommended TINYINT(1) DEFAULT 0 COMMENT '是否推荐(1=推荐,0=普通)' AFTER is_black");
} catch (Exception $e) {
    // 字段已存在，忽略错误
}

// 检查并添加虚拟单量字段（兼容已有数据）
try {
    $pdo->exec("ALTER TABLE technician ADD COLUMN virtual_orders INT DEFAULT 0 COMMENT '虚拟单量' AFTER is_recommended");
} catch (Exception $e) {
    // 字段已存在，忽略错误
}

// 检查并添加休息状态字段（兼容已有数据）
try {
    $pdo->exec("ALTER TABLE technician ADD COLUMN is_resting TINYINT(1) DEFAULT 0 COMMENT '是否休息(1=休息中,0=工作中)' AFTER virtual_orders");
} catch (Exception $e) {
    // 字段已存在，忽略错误
}

if (!$pdo) {
  echo json_encode([
    'success' => false,
    'msg' => '数据库连接失败',
    'data' => []
  ], JSON_UNESCAPED_UNICODE);
  exit;
}

try {
$stmt = $pdo->query("SELECT id, nick, phone, password, age, height, weight, size, model, city, hotel, workimg, lifeimg, video, status, apply_time, reject_reason, last_visit, is_black, is_recommended, virtual_orders, is_resting FROM technician ORDER BY is_recommended DESC, id DESC");
$data = $stmt ? $stmt->fetchAll() : [];
$fields = ['id','nick','phone','password','age','height','weight','size','model','city','hotel','workimg','lifeimg','video','status','apply_time','reject_reason','last_visit','is_black','is_recommended','virtual_orders','is_resting'];
  // 状态映射：pending->0, approved->1, rejected->2, review->3
  $statusMap = [
    'pending' => 0,
    'approved' => 1,
    'rejected' => 2,
    'review' => 3
  ];
  $data = array_map(function($row) use ($fields, $statusMap){
    foreach($fields as $f){
      if(!isset($row[$f])) $row[$f] = '';
    }
    // 兼容前端：status 字段转为数字
    if(isset($row['status'])){
      $row['status'] = isset($statusMap[$row['status']]) ? $statusMap[$row['status']] : $row['status'];
    }

    // 添加推荐状态文本
    $row['is_recommended_text'] = $row['is_recommended'] ? '推荐' : '普通';

    // 确保虚拟单量字段存在
    $row['virtual_orders'] = $row['virtual_orders'] ?? 0;

    // 确保休息状态字段存在
    $row['is_resting'] = $row['is_resting'] ?? 0;

    // 添加休息状态文本
    $row['is_resting_text'] = $row['is_resting'] ? '休息中' : '工作中';

    // 处理最后访问时间
    if (!empty($row['last_visit'])) {
      $row['last_visit'] = date('Y-m-d H:i:s', strtotime($row['last_visit']));
    } else {
      $row['last_visit'] = null; // 设置为null，前端会处理为"从未访问"
    }

    // 处理申请时间
    if (!empty($row['apply_time'])) {
      $row['apply_time'] = date('Y-m-d H:i:s', strtotime($row['apply_time']));
    }

    return $row;
  }, $data);
  echo json_encode([
    'success' => true,
    'msg' => 'ok',
    'data' => $data
  ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
  echo json_encode([
    'success' => false,
    'msg' => '查询失败: ' . $e->getMessage(),
    'data' => []
  ], JSON_UNESCAPED_UNICODE);
}
exit;
