<!DOCTYPE html>
<html>
<head>
    <title>数据库配置一键工具</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-test {
            background: #28a745;
            margin-top: 10px;
        }
        .btn-test:hover {
            background: #1e7e34;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .file-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .file-list h3 {
            margin-top: 0;
            color: #495057;
        }
        .file-item {
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .file-item:last-child {
            border-bottom: none;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 数据库配置一键工具</h1>
        
        <div class="warning">
            <strong>⚠️ 注意：</strong>此工具将修改以下文件的数据库配置，请确保已备份重要文件！
        </div>
        
        <div class="file-list">
            <h3>将要修改的文件：</h3>
            <div class="file-item">📄 db.php</div>
            <div class="file-item">📄 technician_apply.php</div>
            <div class="file-item">📄 login.php</div>
            <div class="file-item">📄 register.php</div>
            <div class="file-item">📄 admin/project_article.php</div>
        </div>
        
        <form id="configForm">
            <div class="form-group">
                <label for="host">数据库主机地址：</label>
                <input type="text" id="host" name="host" value="localhost" required>
            </div>
            
            <div class="form-group">
                <label for="username">数据库用户名：</label>
                <input type="text" id="username" name="username" value="root" required>
            </div>
            
            <div class="form-group">
                <label for="password">数据库密码：</label>
                <input type="password" id="password" name="password" value="" placeholder="如无密码请留空">
            </div>
            
            <div class="form-group">
                <label for="database">数据库名称：</label>
                <input type="text" id="database" name="database" value="7spa" required>
            </div>
            
            <button type="button" class="btn btn-test" onclick="testConnection()">🔍 测试数据库连接</button>
            <button type="submit" class="btn">🚀 一键配置所有文件</button>
        </form>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        // 测试数据库连接
        function testConnection() {
            const formData = new FormData(document.getElementById('configForm'));
            formData.append('action', 'test');
            
            showResult('正在测试数据库连接...', 'info');
            
            fetch('db_config_tool.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult('✅ 数据库连接测试成功！', 'success');
                } else {
                    showResult('❌ 数据库连接失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                showResult('❌ 测试失败：' + error.message, 'error');
            });
        }
        
        // 配置所有文件
        document.getElementById('configForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('action', 'config');
            
            showResult('正在配置所有文件，请稍候...', 'info');
            
            fetch('db_config_tool.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let message = '🎉 配置完成！\n\n';
                    message += '成功配置的文件：\n';
                    data.files.forEach(file => {
                        message += '✅ ' + file + '\n';
                    });
                    if (data.errors && data.errors.length > 0) {
                        message += '\n配置失败的文件：\n';
                        data.errors.forEach(error => {
                            message += '❌ ' + error + '\n';
                        });
                    }
                    showResult(message, data.errors && data.errors.length > 0 ? 'error' : 'success');
                } else {
                    showResult('❌ 配置失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                showResult('❌ 配置失败：' + error.message, 'error');
            });
        });
        
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.className = 'result ' + (type === 'success' ? 'success' : type === 'error' ? 'error' : '');
            result.innerHTML = '<pre>' + message + '</pre>';
            result.style.display = 'block';
        }
    </script>
</body>
</html>

<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json; charset=utf-8');
    
    $action = $_POST['action'] ?? '';
    $host = $_POST['host'] ?? 'localhost';
    $username = $_POST['username'] ?? '7spa';
    $password = $_POST['password'] ?? '7spa';
    $database = $_POST['database'] ?? '7spa';
    
    if ($action === 'test') {
        // 测试数据库连接
        try {
            $dsn = "mysql:host=$host;dbname=$database;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            echo json_encode(['success' => true, 'message' => '数据库连接成功']);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'config') {
        // 配置所有文件
        $files = [
            'db.php' => [
                'pattern' => '/\$user\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement' => "\$user = '$username';",
                'pattern2' => '/\$pass\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement2' => "\$pass = '$password';",
                'pattern3' => '/\$dbname\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement3' => "\$dbname = '$database';"
            ],
            'technician_apply.php' => [
                'pattern' => '/\$db_user\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement' => "\$db_user = '$username';",
                'pattern2' => '/\$db_pass\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement2' => "\$db_pass = '$password';",
                'pattern3' => '/\$db_name\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement3' => "\$db_name = '$database';"
            ],
            'login.php' => [
                'pattern' => '/\$user\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement' => "\$user = '$username';",
                'pattern2' => '/\$pass\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement2' => "\$pass = '$password';",
                'pattern3' => '/\$db\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement3' => "\$db = '$database';"
            ],
            'register.php' => [
                'pattern' => '/\$user\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement' => "\$user = '$username';",
                'pattern2' => '/\$pass\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement2' => "\$pass = '$password';",
                'pattern3' => '/\$db\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement3' => "\$db = '$database';"
            ],
            'admin/project_article.php' => [
                'pattern' => '/\$user\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement' => "\$user = '$username';",
                'pattern2' => '/\$pass\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement2' => "\$pass = '$password';",
                'pattern3' => '/\$dbname\s*=\s*[\'"][^\'"]*[\'"];/',
                'replacement3' => "\$dbname = '$database';"
            ]
        ];
        
        $successFiles = [];
        $errors = [];
        
        foreach ($files as $filename => $config) {
            if (file_exists($filename)) {
                $content = file_get_contents($filename);
                if ($content !== false) {
                    // 替换用户名
                    $content = preg_replace($config['pattern'], $config['replacement'], $content);
                    // 替换密码
                    $content = preg_replace($config['pattern2'], $config['replacement2'], $content);
                    // 替换数据库名
                    $content = preg_replace($config['pattern3'], $config['replacement3'], $content);
                    
                    if (file_put_contents($filename, $content) !== false) {
                        $successFiles[] = $filename;
                    } else {
                        $errors[] = "$filename - 写入失败";
                    }
                } else {
                    $errors[] = "$filename - 读取失败";
                }
            } else {
                $errors[] = "$filename - 文件不存在";
            }
        }
        
        echo json_encode([
            'success' => count($successFiles) > 0,
            'files' => $successFiles,
            'errors' => $errors,
            'message' => count($successFiles) > 0 ? '配置完成' : '配置失败'
        ]);
        exit;
    }
}
?>
