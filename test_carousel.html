<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        .carousel-preview {
            width: 100%;
            max-width: 400px;
            margin: 20px auto;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .btn {
            background: #00c6a2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #00a085;
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.secondary:hover {
            background: #545b62;
        }
        .status-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .feature-list {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin-bottom: 8px;
            color: #666;
        }
        
        /* 复制feed.html的轮播图样式 */
        .carousel-container {
            position: relative;
            width: 100%;
            height: 180px;
            overflow: hidden;
            border-radius: 12px;
        }
        
        .carousel-wrapper {
            display: flex;
            width: 100%;
            height: 100%;
            transition: transform 0.5s ease-in-out;
        }
        
        .carousel-slide {
            min-width: 100%;
            height: 100%;
            position: relative;
        }
        
        .carousel-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }
        
        .carousel-indicators {
            position: absolute;
            bottom: 12px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
            z-index: 10;
        }
        
        .carousel-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .carousel-indicator.active {
            background: rgba(255, 255, 255, 0.9);
            transform: scale(1.2);
        }
        
        .carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.3);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10;
        }
        
        .carousel-nav:hover {
            background: rgba(0, 0, 0, 0.6);
            transform: translateY(-50%) scale(1.1);
        }
        
        .carousel-nav.prev {
            left: 12px;
        }
        
        .carousel-nav.next {
            right: 12px;
        }
        
        .carousel-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 180px;
            background: #f5f5f5;
            color: #999;
            font-size: 14px;
        }
        
        .carousel-error {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 180px;
            background: #f5f5f5;
            color: #999;
            font-size: 14px;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .carousel-slide {
            animation: fadeIn 0.5s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎠 轮播图功能测试</h1>
        
        <div class="demo-section">
            <h3>🎯 功能概述</h3>
            <p style="color: #666; margin-bottom: 15px;">
                动态页面的轮播图现在可以从后台管理系统获取您上传的图片，支持自动播放、手动切换和触摸滑动。
            </p>
            
            <div class="feature-list">
                <h4>✨ 主要功能</h4>
                <ul>
                    <li><strong>动态加载</strong> - 从后台获取轮播图数据</li>
                    <li><strong>自动播放</strong> - 4秒自动切换到下一张</li>
                    <li><strong>手动控制</strong> - 左右箭头按钮切换</li>
                    <li><strong>指示器</strong> - 底部圆点显示当前位置</li>
                    <li><strong>触摸滑动</strong> - 移动端支持左右滑动</li>
                    <li><strong>链接跳转</strong> - 支持点击图片跳转到指定链接</li>
                    <li><strong>错误处理</strong> - 优雅处理加载失败的情况</li>
                </ul>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>📊 轮播图预览</h3>
            <div class="carousel-preview">
                <div id="test-carousel" class="carousel-container">
                    <div class="carousel-loading">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div style="width: 16px; height: 16px; border: 2px solid #ddd; border-top: 2px solid #00c6a2; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                            正在加载轮播图...
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🔧 操作测试</h3>
            <button class="btn" onclick="reloadCarousel()">🔄 重新加载轮播图</button>
            <button class="btn" onclick="testCarouselAPI()">📡 测试API接口</button>
            <button class="btn" onclick="showCarouselInfo()">ℹ️ 显示轮播图信息</button>
            <button class="btn secondary" onclick="openFeedPage()">🔗 打开动态页面</button>
            
            <div id="operation-result" class="status-info" style="display: none;"></div>
        </div>
        
        <div class="demo-section">
            <h3>📱 使用说明</h3>
            <div style="color: #666; line-height: 1.6;">
                <p><strong>管理员操作：</strong></p>
                <ol>
                    <li>登录后台管理系统</li>
                    <li>进入"轮播图管理"页面</li>
                    <li>上传图片并设置排序</li>
                    <li>启用轮播图显示</li>
                </ol>
                
                <p><strong>用户体验：</strong></p>
                <ol>
                    <li>打开动态页面自动加载轮播图</li>
                    <li>图片每4秒自动切换</li>
                    <li>可以点击左右箭头手动切换</li>
                    <li>移动端支持左右滑动切换</li>
                    <li>点击图片可跳转到设置的链接</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // 复制feed.html的轮播图类
        class Carousel {
            constructor(containerId) {
                this.container = document.getElementById(containerId);
                this.currentSlide = 0;
                this.slides = [];
                this.autoPlayInterval = null;
                this.autoPlayDelay = 4000;
                
                this.init();
            }
            
            async init() {
                try {
                    await this.loadBanners();
                    if (this.slides.length > 0) {
                        this.render();
                        this.bindEvents();
                        this.startAutoPlay();
                    } else {
                        this.showEmptyState();
                    }
                } catch (error) {
                    console.error('轮播图初始化失败:', error);
                    this.showErrorState();
                }
            }
            
            async loadBanners() {
                const response = await fetch('banner_list.php');
                const data = await response.json();
                
                if (data.success && Array.isArray(data.data)) {
                    this.slides = data.data;
                } else {
                    throw new Error(data.msg || '获取轮播图失败');
                }
            }
            
            render() {
                if (this.slides.length === 0) {
                    this.showEmptyState();
                    return;
                }
                
                const html = `
                    <div class="carousel-wrapper" id="carousel-wrapper">
                        ${this.slides.map((slide, index) => `
                            <div class="carousel-slide" data-index="${index}">
                                <img src="${slide.img}" alt="轮播图${index + 1}" 
                                     onerror="this.src='images/lbt.png'"
                                     ${slide.link ? `onclick="window.open('${slide.link}', '_blank')"` : ''}
                                     style="${slide.link ? 'cursor: pointer;' : ''}" />
                            </div>
                        `).join('')}
                    </div>
                    
                    ${this.slides.length > 1 ? `
                        <button class="carousel-nav prev" onclick="testCarousel.prevSlide()">‹</button>
                        <button class="carousel-nav next" onclick="testCarousel.nextSlide()">›</button>
                        
                        <div class="carousel-indicators">
                            ${this.slides.map((_, index) => `
                                <div class="carousel-indicator ${index === 0 ? 'active' : ''}" 
                                     onclick="testCarousel.goToSlide(${index})"></div>
                            `).join('')}
                        </div>
                    ` : ''}
                `;
                
                this.container.innerHTML = html;
                this.wrapper = document.getElementById('carousel-wrapper');
            }
            
            showEmptyState() {
                this.container.innerHTML = `
                    <div class="carousel-error">
                        <div style="font-size: 48px; margin-bottom: 12px;">🖼️</div>
                        <div>暂无轮播图</div>
                        <div style="font-size: 12px; margin-top: 4px; opacity: 0.7;">管理员还未上传轮播图</div>
                    </div>
                `;
            }
            
            showErrorState() {
                this.container.innerHTML = `
                    <div class="carousel-error">
                        <div style="font-size: 48px; margin-bottom: 12px;">⚠️</div>
                        <div>轮播图加载失败</div>
                        <button onclick="testCarousel.init()" style="
                            margin-top: 12px; padding: 6px 12px; background: #00c6a2; 
                            color: white; border: none; border-radius: 4px; cursor: pointer;
                        ">重新加载</button>
                    </div>
                `;
            }
            
            bindEvents() {
                if (this.slides.length <= 1) return;
                
                let startX = 0;
                let startY = 0;
                let isDragging = false;
                
                this.container.addEventListener('touchstart', (e) => {
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                    isDragging = true;
                    this.stopAutoPlay();
                });
                
                this.container.addEventListener('touchmove', (e) => {
                    if (!isDragging) return;
                    e.preventDefault();
                });
                
                this.container.addEventListener('touchend', (e) => {
                    if (!isDragging) return;
                    isDragging = false;
                    
                    const endX = e.changedTouches[0].clientX;
                    const endY = e.changedTouches[0].clientY;
                    const deltaX = endX - startX;
                    const deltaY = endY - startY;
                    
                    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                        if (deltaX > 0) {
                            this.prevSlide();
                        } else {
                            this.nextSlide();
                        }
                    }
                    
                    this.startAutoPlay();
                });
                
                this.container.addEventListener('mouseenter', () => this.stopAutoPlay());
                this.container.addEventListener('mouseleave', () => this.startAutoPlay());
            }
            
            goToSlide(index) {
                if (index < 0 || index >= this.slides.length) return;
                
                this.currentSlide = index;
                this.updateSlidePosition();
                this.updateIndicators();
            }
            
            nextSlide() {
                const nextIndex = (this.currentSlide + 1) % this.slides.length;
                this.goToSlide(nextIndex);
            }
            
            prevSlide() {
                const prevIndex = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
                this.goToSlide(prevIndex);
            }
            
            updateSlidePosition() {
                if (!this.wrapper) return;
                const translateX = -this.currentSlide * 100;
                this.wrapper.style.transform = `translateX(${translateX}%)`;
            }
            
            updateIndicators() {
                const indicators = this.container.querySelectorAll('.carousel-indicator');
                indicators.forEach((indicator, index) => {
                    indicator.classList.toggle('active', index === this.currentSlide);
                });
            }
            
            startAutoPlay() {
                if (this.slides.length <= 1) return;
                
                this.stopAutoPlay();
                this.autoPlayInterval = setInterval(() => {
                    this.nextSlide();
                }, this.autoPlayDelay);
            }
            
            stopAutoPlay() {
                if (this.autoPlayInterval) {
                    clearInterval(this.autoPlayInterval);
                    this.autoPlayInterval = null;
                }
            }
            
            destroy() {
                this.stopAutoPlay();
                if (this.container) {
                    this.container.innerHTML = '';
                }
            }
        }

        // 全局轮播图实例
        let testCarousel;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            testCarousel = new Carousel('test-carousel');
        });

        // 重新加载轮播图
        function reloadCarousel() {
            if (testCarousel) {
                testCarousel.destroy();
            }
            testCarousel = new Carousel('test-carousel');
            
            const resultDiv = document.getElementById('operation-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '✅ 轮播图已重新加载';
        }

        // 测试API接口
        function testCarouselAPI() {
            const resultDiv = document.getElementById('operation-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试API接口...';
            
            fetch('banner_list.php')
                .then(response => response.json())
                .then(data => {
                    resultDiv.textContent = `API测试结果:

${JSON.stringify(data, null, 2)}

${data.success ? '✅ API调用成功' : '❌ API调用失败'}
轮播图数量: ${data.data ? data.data.length : 0}`;
                })
                .catch(error => {
                    resultDiv.textContent = `❌ API测试失败: ${error.message}`;
                });
        }

        // 显示轮播图信息
        function showCarouselInfo() {
            const resultDiv = document.getElementById('operation-result');
            resultDiv.style.display = 'block';
            
            if (testCarousel && testCarousel.slides.length > 0) {
                resultDiv.textContent = `轮播图信息:

总数量: ${testCarousel.slides.length}
当前显示: 第 ${testCarousel.currentSlide + 1} 张
自动播放: ${testCarousel.autoPlayInterval ? '开启' : '关闭'}

轮播图列表:
${testCarousel.slides.map((slide, index) => 
    `${index + 1}. ${slide.title || '无标题'} (${slide.img})`
).join('\n')}`;
            } else {
                resultDiv.textContent = '❌ 暂无轮播图数据';
            }
        }

        // 打开动态页面
        function openFeedPage() {
            window.open('feed.html', '_blank');
        }
    </script>
</body>
</html>
