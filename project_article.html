<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <title>项目介绍详情</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { background: #f7f8fa; font-family: 'Roboto', 'PingFang SC', 'Microsoft YaHei', sans-serif; margin: 0; color: #333; }
        .container { max-width: 600px; margin: 32px auto; background: #fff; border-radius: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.06); padding: 32px 24px; }
        .title { font-size: 24px; font-weight: bold; color: #222; margin-bottom: 18px; }
        .content { font-size: 16px; color: #444; line-height: 1.8; white-space: pre-line; }
        .back { display: inline-block; margin-bottom: 18px; color: #00c6a2; text-decoration: none; font-size: 15px; }
        .back:hover { text-decoration: underline; }
        .back-btn { display:inline-block; margin-top:28px; background:#2196f3; color:#fff; border:none; border-radius:8px; padding:8px 28px; font-size:16px; cursor:pointer; text-align:center; }
    @media (max-width:700px) {
      .article-wrap { max-width:98vw; padding:18px 6vw; }
      .article-title { font-size:18px; }
      .article-content { font-size:15px; }
    }
    </style>
</head>
<body>
    <div class="container">
        <div class="title" id="article-title" style="font-size:26px;font-weight:bold;color:#00c6a2;letter-spacing:1px;text-align:center;">加载中...</div>
        <div class="content" id="article-content" style="font-size:18px;color:#222;line-height:2.1;font-weight:500;white-space:pre-line;"></div>
        <button class="back-btn" onclick="window.history.back()">返回</button>
    </div>
    <script>
    // 自动加载项目介绍内容
    fetch('admin/project_article.php')
      .then(res => res.json())
      .then(data => {
        if(data.success && data.data){
          document.getElementById('article-title').textContent = data.data.title || '暂无标题';
          document.getElementById('article-content').textContent = data.data.content || '暂无内容';
        }else{
          document.getElementById('article-title').textContent = '加载失败';
          document.getElementById('article-content').textContent = data.msg || '';
        }
      })
      .catch(()=>{
        document.getElementById('article-title').textContent = '加载失败';
        document.getElementById('article-content').textContent = '无法获取项目介绍内容。';
      });
    </script>
</body>
</html>
