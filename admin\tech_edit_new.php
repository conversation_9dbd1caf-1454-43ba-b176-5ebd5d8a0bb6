<?php
// tech_edit_new.php - 编辑技师资料（重写版）

// 错误处理设置
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// 输出缓冲
ob_start();

// 错误处理函数
function sendJsonError($message) {
    ob_clean();
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['success' => false, 'msg' => $message]);
    exit;
}

function sendJsonSuccess($message) {
    ob_clean();
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['success' => true, 'msg' => $message]);
    exit;
}

// 设置错误处理
set_error_handler(function($severity, $message, $file, $line) {
    error_log("PHP Error: $message in $file on line $line");
    return true;
});

register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        sendJsonError('服务器内部错误');
    }
});

try {
    // 检查登录状态
    session_start();
    if (!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] !== true) {
        sendJsonError('未登录');
    }

    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendJsonError('请求方法错误');
    }

    // 数据库连接
    require_once '../db.php';
    if (!$pdo) {
        sendJsonError('数据库连接失败');
    }

    // 获取技师ID
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    if ($id <= 0) {
        sendJsonError('无效的技师ID');
    }

    // 获取表单数据
    $nick = isset($_POST['nick']) ? trim($_POST['nick']) : '';
    $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    $age = isset($_POST['age']) ? trim($_POST['age']) : '';
    $height = isset($_POST['height']) ? trim($_POST['height']) : '';
    $weight = isset($_POST['weight']) ? trim($_POST['weight']) : '';
    $size = isset($_POST['size']) ? trim($_POST['size']) : '';
    $model = isset($_POST['model']) ? trim($_POST['model']) : '';
    $city = isset($_POST['city']) ? trim($_POST['city']) : '';
    $hotel = isset($_POST['hotel']) ? trim($_POST['hotel']) : '';
    $virtual_orders = isset($_POST['virtual_orders']) ? intval($_POST['virtual_orders']) : 0;

    // 基本验证
    if (!$nick || !$phone || !$age || !$height || !$weight || !$size || $model === '' || !$city || !$hotel) {
        sendJsonError('请填写完整信息');
    }

    // 验证虚拟单量
    if ($virtual_orders < 0 || $virtual_orders > 9999) {
        sendJsonError('虚拟单量必须在0-9999之间');
    }

    // 查询现有技师信息
    $stmt = $pdo->prepare("SELECT * FROM technician WHERE id = ?");
    $stmt->execute([$id]);
    $existingTech = $stmt->fetch();
    
    if (!$existingTech) {
        sendJsonError('技师不存在');
    }

    // 保持原有文件路径
    $workimg_path = $existingTech['workimg'];
    $lifeimg_str = $existingTech['lifeimg'];
    $video_path = $existingTech['video'];

    // 处理文件上传（简化版，先不处理文件上传）
    // TODO: 添加文件上传处理

    // 更新数据库
    $stmt = $pdo->prepare("UPDATE technician SET nick=?, phone=?, age=?, height=?, weight=?, size=?, model=?, city=?, hotel=?, virtual_orders=? WHERE id=?");
    $result = $stmt->execute([
        $nick, $phone, $age, $height, $weight, $size, $model, $city, $hotel, $virtual_orders, $id
    ]);

    if ($result) {
        sendJsonSuccess('技师资料修改成功');
    } else {
        sendJsonError('修改失败');
    }

} catch (Exception $e) {
    error_log("Tech edit error: " . $e->getMessage());
    sendJsonError('修改失败: ' . $e->getMessage());
}
?>
