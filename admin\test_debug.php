<?php
// 测试脚本 - 检查基本功能
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "开始测试...\n";

// 测试 session
session_start();
echo "Session 启动成功\n";

// 测试数据库连接
try {
    require_once '../db.php';
    if ($pdo) {
        echo "数据库连接成功\n";
        
        // 测试查询
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM technician");
        $stmt->execute();
        $result = $stmt->fetch();
        echo "技师表记录数: " . $result['count'] . "\n";
    } else {
        echo "数据库连接失败\n";
    }
} catch (Exception $e) {
    echo "数据库错误: " . $e->getMessage() . "\n";
}

// 测试 JSON 输出
header('Content-Type: application/json; charset=utf-8');
echo json_encode(['success' => true, 'msg' => '测试成功']);
?>
